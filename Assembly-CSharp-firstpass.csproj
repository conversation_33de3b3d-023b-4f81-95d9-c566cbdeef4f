<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>8.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{a0431b3a-6e27-fb34-197e-1acde9001a0f}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-firstpass</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp-firstpass\</OutputPath>
    <DefineConstants>UNITY_2020_3_47;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_STANDARD_2_0;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;AMPLIFY_SHADER_EDITOR;DISABLE_AIHELP;UNITY_POST_PROCESSING_STACK_V2;ODIN_INSPECTOR;ODIN_INSPECTOR_3;FISH_PORTRAIT;FISH_DEBUG;FISH_PAY;USE_HUATUO;PLAYMAKER;PLAYMAKER_1_9;PLAYMAKER_1_9_1;PLAYMAKER_1_8_OR_NEWER;PLAYMAKER_1_8_5_OR_NEWER;PLAYMAKER_1_9_OR_NEWER;PLAYMAKER_TMPRO;PLAYMAKER_UTILS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Assets\Plugins\ProjectorShadow\Scripts\ProjectorShadow.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\ListView\LoopListItemPool.cs" />
    <Compile Include="Assets\Plugins\BuglyPlugins\BuglyInit.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\SpinDatePickerDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\GridViewDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\GridViewSampleDemo.cs" />
    <Compile Include="Assets\Plugins\StreamingAssetsHelper\StreamingAssetsHelperOLD.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\TopToBottomDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\GridView\GridViewDeleteItemDemoScript2.cs" />
    <Compile Include="Assets\Plugins\BuglyPlugins\BuglyCallback.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem16.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\Packer.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\ListView\LoopListView2.cs" />
    <Compile Include="Assets\Plugins\AnimFX Preview\Scripts\AnimPreview.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIHsvModifier.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIShiny.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\ToneMode.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem4.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIGradient.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem3.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\BaseMeshEffect.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\DragEventHelper.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\PullToRefreshDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ClickAndLoadMoreDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\DeleteItemDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ChangeItemHeightDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem14.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem18.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\EffectPlayer.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIEffectCapturedImage.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem2.cs" />
    <Compile Include="Assets\Plugins\PostProcessing\CustomEffects\RadialBlur\RadialBlur.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\MaterialCache.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\PageViewDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\DragChangSizeScript.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModulePhysics2D.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\LeftToRightDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\Common\ClickEventListener.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\TopToBottomSampleDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\EffectArea.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\TreeViewDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem1.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem9.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModuleUnityVersion.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem10.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\TreeViewDataSourceMgr.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\StaggeredGridView\StaggeredGridItemGroup.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModuleUtils.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\StaggeredGridView\LoopStaggeredGridView.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIShadow.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\AutoSetAnchorPosForIphonex.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ResponsiveGridViewDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIDissolve.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\StaggeredGridView\StaggeredGridView_TopToBottomDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\Common\CommonDefine.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ChangeViewPortHeightDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\Matrix2x3.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\TreeViewWithStickyHeadDemoScript.cs" />
    <Compile Include="Assets\Plugins\PostProcessing\CustomEffects\Sharpen\Sharpen.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\BottomToTopDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem19.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem7.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem12.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\HorizontalGalleryDemoScript.cs" />
    <Compile Include="Assets\Plugins\ParticleEffect\LineRendererExtension.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\StaggeredGridView\StaggeredGridView_LeftToRightDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\ShadowStyle.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem15.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\DataSourceMgr.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\PullToRefreshAndLoadMoreDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UITransitionEffect.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\TreeViewItemCountMgr.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\MaterialResolver.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\ColorMode.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIEffect.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\GridView\LoopGridView.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\PullToLoadMoreDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem17.cs" />
    <Compile Include="Assets\Plugins\UIParticles\Scripts\SetPropertyUtility.cs" />
    <Compile Include="Assets\Plugins\AnimFX Preview\Common\ReorderableList\ReorderableArray.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem5.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\TopToBottomFilterSampleDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\RotateScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\GridViewDeleteItemDemoScript.cs" />
    <Compile Include="Assets\Plugins\BuglyPlugins\BuglyAgent.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\FPSDisplay.cs" />
    <Compile Include="Assets\Plugins\StreamingAssetsHelper\BuildinFileManifest.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\VerticalGalleryDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\MenuSceneScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\GridView\LoopGridItemPool.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\ParameterTexture.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\GridView\GridViewDemoScript2.cs" />
    <Compile Include="Assets\Plugins\AnimFX Preview\Common\ReorderableList\Attributes\ReorderableAttribute.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\RightToLeftDemoScript.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem0.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModulePhysics.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem13.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\UIEffectBase.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModuleSprite.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem11.cs" />
    <Compile Include="Assets\Plugins\StreamingAssetsHelper\StreamingAssetsHelper.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\ListView\LoopListViewItem2.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\GridView\LoopGridViewItem.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModuleUI.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem6.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\Common\ItemPosMgr.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\StaggeredGridView\LoopStaggeredGridViewItem.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ResManager.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ListItem8.cs" />
    <Compile Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\CameraOutline.cs" />
    <Compile Include="Assets\Plugins\DOTween\Modules\DOTweenModuleAudio.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\Common\BlurMode.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ResponsiveGridViewDemoScript2.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\Common\ChatMsgDataSourceMgr.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Demo\Scripts\ListView\ChatMsgListViewDemoScript.cs" />
    <Compile Include="Assets\Plugins\UIParticles\Scripts\UiParticles.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\GridView\GridItemGroup.cs" />
    <Compile Include="Assets\Plugins\SuperScrollView\Scripts\StaggeredGridView\StaggeredGridItemPool.cs" />
    <Compile Include="Assets\Plugins\UIEffect\Scripts\UIFlip.cs" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\StdLib.hlsl" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect-Shiny.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Waveform.shader" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\RadialBlur\Shader\RadialBlur.shader" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Particle Multiply.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MultiScaleVOUpsample.compute" />
    <None Include="Assets\Plugins\Spine\version.txt" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Particle Dissolve Add.shader" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Sharpen\Shader\Sharpen.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Distortion.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ExposureHistogram.hlsl" />
    <None Include="Assets\Plugins\BitmapFontImporter\Examples\Font1\font1.fnt" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Texture3DLerp.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\DeferredFog.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\TemporalAntialiasing.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\FastApproximateAntialiasing.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\SubpixelMorphologicalAntialiasing.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Dithering.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Bloom.shader" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect-Dissolve.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Vectorscope.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Sampling.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\CopyStdFromTexArray.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Lut3DBaker.compute" />
    <None Include="Assets\Plugins\UIParticles\Shaders\UI_raoluan.shader" />
    <None Include="Assets\Plugins\ParticleEffectForUGUI\package.json" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Waveform.compute" />
    <None Include="Assets\Plugins\UIParticles\version.txt" />
    <None Include="Assets\Plugins\Android\AndroidManifest.xml" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Particle Add.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\PSSL.hlsl" />
    <None Include="Assets\Plugins\iOS\XYSDK\facebook\FBSDKLoginKit.framework\Modules\FBSDKLoginKit.swiftmodule\arm64-apple-ios.abi.json" />
    <None Include="Assets\Plugins\BitmapFontImporter\Examples\Font2\font2.fnt" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\LightMeter.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\AutoExposure.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MultiScaleVORender.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Texture2DLerp.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\SubpixelMorphologicalAntialiasingBridge.hlsl" />
    <None Include="Assets\Plugins\UIEffect\package.json" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect.cginc" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\DiskKernels.hlsl" />
    <None Include="Assets\Plugins\BitmapFontImporter\Examples\font3\font3.fnt" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\D3D11.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MultiScaleVODownsample2.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ScalableAO.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\ClearStencil.shader" />
    <None Include="Assets\Plugins\Android\proguard-user.txt" />
    <None Include="Assets\Plugins\YooAsset\package.json" />
    <None Include="Assets\Plugins\iOS\XYSDK\facebook\FBSDKShareKit.framework\Modules\FBSDKShareKit.swiftmodule\arm64-apple-ios.abi.json" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect-Transition.shader" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\Copy.shader" />
    <None Include="Assets\Plugins\BitmapFontImporter\Shaders\BFILogic.cginc" />
    <None Include="Assets\Plugins\JsonDotNet\link.xml" />
    <None Include="Assets\Plugins\iOS\XYSDK\facebook\FBSDKGamingServicesKit.framework\Modules\FBSDKGamingServicesKit.swiftmodule\arm64-apple-ios.abi.json" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MultiScaleVO.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Vectorscope.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\D3D9.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ScalableAO.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\ACES.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\FinalPass.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Histogram.shader" />
    <None Include="Assets\Plugins\BitmapFontImporter\Shaders\Font5.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Histogram.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\Switch.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\Metal.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Uber.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\xRLib.hlsl" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect.shader" />
    <None Include="Assets\Plugins\iOS\XYSDK\WeChat\README.txt" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\CopyStdFromDoubleWide.shader" />
    <None Include="Assets\Plugins\Spine\package.json" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\Expand.shader" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Glow Additive Simple.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Debug\Overlays.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\PSP2.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ScreenSpaceReflections.shader" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\ObjectInfo.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\DepthOfField.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\DiscardAlpha.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\OpenGL.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Lut2DBaker.shader" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-EffectCapture.shader" />
    <None Include="Assets\Plugins\ProjectorShadow\Shader\ShadowCaster.shader" />
    <None Include="Assets\Plugins\ProjectorShadow\Shader\ShadowReceive.shader" />
    <None Include="Assets\Plugins\iOS\XYSDK\facebook\FBSDKCoreKit.framework\Modules\FBSDKCoreKit.swiftmodule\arm64-apple-ios.abi.json" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\CopyStd.shader" />
    <None Include="Assets\Plugins\ICSharpCode.SharpZipLib\netstandard2.0\ICSharpCode.SharpZipLib.xml" />
    <None Include="Assets\Plugins\BitmapFontImporter\Shaders\Font4.shader" />
    <None Include="Assets\Plugins\ParticleEffectForUGUI\Shaders\particle_distort_UI.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MotionBlur.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\MultiScaleVODownsample1.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Fog.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ExposureHistogram.compute" />
    <None Include="Assets\Plugins\ParticleEffectForUGUI\Shaders\UIAdditive.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\GrainBaker.shader" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Particle UV Mask.shader" />
    <None Include="Assets\Plugins\UIParticles\Shaders\UI Particle Alpha Blend.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\RadiaBlur.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\GaussianDownsample.compute" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\ScreenSpaceReflections.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\XboxOne.hlsl" />
    <None Include="Assets\Plugins\UIParticles\Shaders\Ui Particle Distort Add.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\Copy.shader" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect-Sprite.cginc" />
    <None Include="Assets\Plugins\SuperScrollView\Scripts\Version 2.4.5.txt" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\D3D12.hlsl" />
    <None Include="Assets\Plugins\BitmapFontImporter\Shaders\Font3.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\DepthOfField.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Builtins\SubpixelMorphologicalAntialiasing.hlsl" />
    <None Include="Assets\Plugins\Android\LauncherManifest.xml" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\Blur.shader" />
    <None Include="Assets\Plugins\ParticleEffectForUGUI\Samples\Performance Demo\NanoMonitor\package.json" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\Final_Blend.shader" />
    <None Include="Assets\Plugins\iOS\XYSDK\facebook\FBAEMKit.framework\Modules\FBAEMKit.swiftmodule\arm64-apple-ios.abi.json" />
    <None Include="Assets\Plugins\BitmapFontImporter\Shaders\Font2.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\API\Vulkan.hlsl" />
    <None Include="Assets\Plugins\UIEffect\Shaders\UI-Effect-HSV.shader" />
    <None Include="Assets\Plugins\PostProcessing\Shaders\Colors.hlsl" />
    <None Include="Assets\Plugins\PostProcessing\CustomEffects\Outline\Shader\Final.shader" />
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Mdb">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="UWAShared">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\UWA\UWA_SDK\Runtime\ManagedLibs\UWAShared.dll</HintPath>
    </Reference>
    <Reference Include="proxima-websocket-sharp">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\ThirdPart\Proxima\WebSocketSharp\proxima-websocket-sharp.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ICSharpCode.SharpZipLib\netstandard2.0\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="PlayMaker">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\PlayMaker\PlayMaker.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zlib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\Ionic.Zlib.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\JsonDotNet\Assemblies\Standalone\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Pdb">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\zxing.unity.dll</HintPath>
    </Reference>
    <Reference Include="ConditionalExpression">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\ConditionalExpression.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\ref\2.0.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CacheServer">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.CacheServer.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Cursor.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Cursor.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.DeviceSimulator.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.DeviceSimulator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="UniFramework.Machine.csproj">
      <Project>{c46542a4-cad3-0a16-0742-cec97b7020de}</Project>
      <Name>UniFramework.Machine</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Runtime.csproj">
      <Project>{7dacd347-4b99-43ff-7bd2-399821e768b0}</Project>
      <Name>HybridCLR.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Network.csproj">
      <Project>{32f18cff-ad0a-fac8-883d-ac8796c2bedc}</Project>
      <Name>UniFramework.Network</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.Editor.csproj">
      <Project>{8d9f312e-1d9e-9a0a-b421-889026abeeca}</Project>
      <Name>YooAsset.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Singleton.csproj">
      <Project>{87944ebe-9e82-036b-18cb-a9f80c3fa62a}</Project>
      <Name>UniFramework.Singleton</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.Editor.csproj">
      <Project>{8ea8bc99-78ec-b440-8f0a-591fd1f43326}</Project>
      <Name>Proxima.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Event.csproj">
      <Project>{ef7c93d4-6571-47fa-6170-a3558a123916}</Project>
      <Name>UniFramework.Event</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity.csproj">
      <Project>{ce5d6b30-a2dd-b9db-65ae-bf6bf68ceff5}</Project>
      <Name>spine-unity</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Tween.csproj">
      <Project>{79a1243a-51ed-79d2-d0c6-d9fe5c9f779d}</Project>
      <Name>UniFramework.Tween</Name>
    </ProjectReference>
    <ProjectReference Include="GameRuntime.csproj">
      <Project>{384add0f-ab48-6bb7-0d2f-8a5f173dc79c}</Project>
      <Name>GameRuntime</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Animation.csproj">
      <Project>{108da027-93aa-7cb7-ac76-cecce677c424}</Project>
      <Name>UniFramework.Animation</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.csproj">
      <Project>{c0720ac4-aec5-5da5-a31e-a887f067c27e}</Project>
      <Name>Proxima</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonanceEditor.csproj">
      <Project>{4d2d783a-89e5-af18-93e4-1cd1706fd8dd}</Project>
      <Name>FMODUnityResonanceEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Editor.csproj">
      <Project>{258d3d40-386b-3526-f193-f03d28a8b899}</Project>
      <Name>Unity.Postprocessing.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Pooling.csproj">
      <Project>{fefe0f0d-5327-4908-feb7-148a7048665c}</Project>
      <Name>UniFramework.Pooling</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Editor.csproj">
      <Project>{abcdaff7-8ceb-ea50-aeb8-4f5402d7e818}</Project>
      <Name>HybridCLR.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.Editor.csproj">
      <Project>{1829fc53-2793-8581-c2b2-ed4bcc7f8bf6}</Project>
      <Name>youhu.unity_uwa_sdk.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="RBG.Mulligan.csproj">
      <Project>{cbfc065b-8b45-689e-91b4-64cd9742c8b4}</Project>
      <Name>RBG.Mulligan</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Window.csproj">
      <Project>{f23b84e2-3bd6-49ef-de9b-ccea0def86fb}</Project>
      <Name>UniFramework.Window</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.csproj">
      <Project>{b53f86b4-8f62-c0d4-94f8-4b9e284011ad}</Project>
      <Name>youhu.unity_uwa_sdk</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Runtime.csproj">
      <Project>{be55b292-cc44-17c8-5069-aa98556bdc44}</Project>
      <Name>Unity.Postprocessing.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="MeshEditor.Effects.RunTime.csproj">
      <Project>{a1e39cda-e28c-97a5-aff4-2a916ec95569}</Project>
      <Name>MeshEditor.Effects.RunTime</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Editor.csproj">
      <Project>{58a638ce-8fe9-ad75-5f36-6492c5455e30}</Project>
      <Name>NativeGallery.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Utility.csproj">
      <Project>{7c28f787-7f16-1728-6d1f-db87d0ed1482}</Project>
      <Name>UniFramework.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.CFX_Demo_With_UIParticle.csproj">
      <Project>{a8b0ee9f-f9e9-f15d-63a5-a175e53896d7}</Project>
      <Name>Coffee.CFX_Demo_With_UIParticle</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnity.csproj">
      <Project>{b2a04fda-593f-2822-75b6-7a0ce85de5c8}</Project>
      <Name>FMODUnity</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityEditor.csproj">
      <Project>{ac2f2cea-5f4d-2521-9100-a99822067e71}</Project>
      <Name>FMODUnityEditor</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Runtime.csproj">
      <Project>{b25065d1-1177-4f4e-abc4-806c113ca925}</Project>
      <Name>NativeGallery.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonance.csproj">
      <Project>{710aab89-2b5d-111c-d2b6-4b78886180d2}</Project>
      <Name>FMODUnityResonance</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity-editor.csproj">
      <Project>{c16d3fc2-d438-a49f-0d35-4d45f5039eb5}</Project>
      <Name>spine-unity-editor</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.csproj">
      <Project>{8adfa466-57e6-7a4c-bea4-51b7aecf0597}</Project>
      <Name>YooAsset</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.UIParticle.csproj">
      <Project>{04540531-31b5-ecaf-3050-921f628688e1}</Project>
      <Name>Coffee.UIParticle</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
