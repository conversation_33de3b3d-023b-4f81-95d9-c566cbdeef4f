#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionQuickFsmAction))]
    public class GActionQuickFsmActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as GActionQuickFsmAction;
            
            EditField("targetObject");
            EditField("quickActionType");
            
            var actionType = action.quickActionType;
            
            // Show duration for actions that support it
            if (SupportsDuration(actionType))
            {
                EditField("duration");
            }
            
            // Show easing for actions that support it
            if (SupportsEasing(actionType))
            {
                EditField("easeType");
            }
            
            // Show specific parameters based on action type
            switch (actionType)
            {
                case GActionQuickFsmAction.QuickActionType.MoveTo:
                case GActionQuickFsmAction.QuickActionType.MoveBy:
                case GActionQuickFsmAction.QuickActionType.MoveToLocal:
                case GActionQuickFsmAction.QuickActionType.MoveByLocal:
                    EditField("targetValue");
                    if (actionType == GActionQuickFsmAction.QuickActionType.MoveTo || 
                        actionType == GActionQuickFsmAction.QuickActionType.MoveBy)
                    {
                        EditField("useWorldSpace");
                    }
                    break;
                    
                case GActionQuickFsmAction.QuickActionType.RotateTo:
                case GActionQuickFsmAction.QuickActionType.RotateBy:
                    EditorGUILayout.LabelField("Use Z component of Target Value for rotation angle", EditorStyles.helpBox);
                    EditField("targetValue");
                    break;
                    
                case GActionQuickFsmAction.QuickActionType.ScaleTo:
                case GActionQuickFsmAction.QuickActionType.ScaleBy:
                    EditField("targetValue");
                    break;
                    
                case GActionQuickFsmAction.QuickActionType.FadeTo:
                    EditField("targetAlpha");
                    break;
                    
                case GActionQuickFsmAction.QuickActionType.SlideInLeft:
                case GActionQuickFsmAction.QuickActionType.SlideInRight:
                case GActionQuickFsmAction.QuickActionType.SlideInUp:
                case GActionQuickFsmAction.QuickActionType.SlideInDown:
                    EditField("targetValue");
                    EditField("useWorldSpace");
                    EditorGUILayout.HelpBox("Target Value is the final position after sliding in.", MessageType.Info);
                    break;
            }
            
            // Always show finished event
            EditField("finishedEvent");
            
            // Show action-specific help
            ShowActionHelp(actionType);
            
            return GUI.changed;
        }
        
        private bool SupportsDuration(GActionQuickFsmAction.QuickActionType actionType)
        {
            switch (actionType)
            {
                case GActionQuickFsmAction.QuickActionType.Show:
                case GActionQuickFsmAction.QuickActionType.Hide:
                case GActionQuickFsmAction.QuickActionType.RemoveSelf:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool SupportsEasing(GActionQuickFsmAction.QuickActionType actionType)
        {
            return SupportsDuration(actionType);
        }
        
        private void ShowActionHelp(GActionQuickFsmAction.QuickActionType actionType)
        {
            string helpText = GetActionHelpText(actionType);
            if (!string.IsNullOrEmpty(helpText))
            {
                EditorGUILayout.HelpBox(helpText, MessageType.Info);
            }
        }
        
        private string GetActionHelpText(GActionQuickFsmAction.QuickActionType actionType)
        {
            switch (actionType)
            {
                case GActionQuickFsmAction.QuickActionType.Show:
                    return "立即显示目标对象";
                    
                case GActionQuickFsmAction.QuickActionType.Hide:
                    return "立即隐藏目标对象";
                    
                case GActionQuickFsmAction.QuickActionType.MoveTo:
                    return "移动到指定位置";
                    
                case GActionQuickFsmAction.QuickActionType.MoveBy:
                    return "相对当前位置移动指定偏移";
                    
                case GActionQuickFsmAction.QuickActionType.MoveToLocal:
                    return "移动到指定本地坐标位置";
                    
                case GActionQuickFsmAction.QuickActionType.MoveByLocal:
                    return "相对当前本地坐标移动指定偏移";
                    
                case GActionQuickFsmAction.QuickActionType.RotateTo:
                    return "旋转到指定角度（Z轴）";
                    
                case GActionQuickFsmAction.QuickActionType.RotateBy:
                    return "相对当前角度旋转指定度数（Z轴）";
                    
                case GActionQuickFsmAction.QuickActionType.ScaleTo:
                    return "缩放到指定大小";
                    
                case GActionQuickFsmAction.QuickActionType.ScaleBy:
                    return "相对当前大小进行缩放";
                    
                case GActionQuickFsmAction.QuickActionType.ScaleToZero:
                    return "缩放到0（常用于消失效果）";
                    
                case GActionQuickFsmAction.QuickActionType.ScaleToOne:
                    return "缩放到1（常用于恢复原始大小）";
                    
                case GActionQuickFsmAction.QuickActionType.FadeIn:
                    return "淡入到完全不透明";
                    
                case GActionQuickFsmAction.QuickActionType.FadeOut:
                    return "淡出到完全透明";
                    
                case GActionQuickFsmAction.QuickActionType.FadeTo:
                    return "淡入淡出到指定透明度";
                    
                case GActionQuickFsmAction.QuickActionType.PopIn:
                    return "弹出效果：从缩放0到1";
                    
                case GActionQuickFsmAction.QuickActionType.PopOut:
                    return "弹出消失效果：从当前缩放到0";
                    
                case GActionQuickFsmAction.QuickActionType.SlideInLeft:
                    return "从左侧滑入到目标位置";
                    
                case GActionQuickFsmAction.QuickActionType.SlideInRight:
                    return "从右侧滑入到目标位置";
                    
                case GActionQuickFsmAction.QuickActionType.SlideInUp:
                    return "从上方滑入到目标位置";
                    
                case GActionQuickFsmAction.QuickActionType.SlideInDown:
                    return "从下方滑入到目标位置";
                    
                case GActionQuickFsmAction.QuickActionType.DelayTime:
                    return "延迟指定时间";
                    
                case GActionQuickFsmAction.QuickActionType.RemoveSelf:
                    return "销毁目标对象";
                    
                default:
                    return "";
            }
        }
    }
}
#endif
