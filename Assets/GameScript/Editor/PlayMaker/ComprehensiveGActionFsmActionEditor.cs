#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(ComprehensiveGActionFsmAction))]
    public class ComprehensiveGActionFsmActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as ComprehensiveGActionFsmAction;
            
            EditField("targetObject");
            EditField("actionType");
            
            var actionType = action.actionType;
            
            // Show duration for actions that support it
            if (SupportsDuration(actionType))
            {
                EditField("duration");
            }
            
            // Show easing for actions that support it
            if (SupportsEasing(actionType))
            {
                EditField("easeType");
            }
            
            // Show specific parameters based on action type
            switch (actionType)
            {
                case ComprehensiveGActionFsmAction.GActionType.MoveTo:
                case ComprehensiveGActionFsmAction.GActionType.MoveBy:
                    EditField("targetPosition");
                    EditField("useWorldSpace");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.RotateTo:
                case ComprehensiveGActionFsmAction.GActionType.RotateBy:
                    EditField("targetAngle");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.RotateBy3D:
                    EditField("targetRotation3D");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.ScaleTo:
                case ComprehensiveGActionFsmAction.GActionType.ScaleBy:
                    EditField("targetScale");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.FadeTo:
                case ComprehensiveGActionFsmAction.GActionType.CanvasGroupAlphaFadeTo:
                    EditField("targetAlpha");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.TintTo:
                    EditField("targetColor");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.BlendTo:
                    EditField("blendValue");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.BezierTo:
                    EditorGUILayout.LabelField("Bezier Control Points", EditorStyles.boldLabel);
                    EditField("bezierP0");
                    EditField("bezierP1");
                    EditField("bezierP2");
                    EditField("bezierP3");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.CallFunc:
                    EditField("callbackEvent");
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.Repeat:
                    EditField("repeatTimes");
                    EditorGUILayout.HelpBox("Note: Repeat functionality requires child actions to be implemented.", MessageType.Info);
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.RepeatForever:
                    EditField("repeatForever");
                    EditorGUILayout.HelpBox("Note: RepeatForever functionality requires child actions to be implemented.", MessageType.Info);
                    break;
                    
                case ComprehensiveGActionFsmAction.GActionType.Sequence:
                case ComprehensiveGActionFsmAction.GActionType.Spawn:
                    EditorGUILayout.HelpBox("Note: Composite actions (Sequence/Spawn) require additional implementation for child actions.", MessageType.Info);
                    break;
            }
            
            // Show action-specific help
            ShowActionHelp(actionType);
            
            return GUI.changed;
        }
        
        private bool SupportsDuration(ComprehensiveGActionFsmAction.GActionType actionType)
        {
            switch (actionType)
            {
                case ComprehensiveGActionFsmAction.GActionType.Show:
                case ComprehensiveGActionFsmAction.GActionType.Hide:
                case ComprehensiveGActionFsmAction.GActionType.RemoveSelf:
                case ComprehensiveGActionFsmAction.GActionType.FlipX:
                case ComprehensiveGActionFsmAction.GActionType.FlipY:
                case ComprehensiveGActionFsmAction.GActionType.CallFunc:
                case ComprehensiveGActionFsmAction.GActionType.Flash:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool SupportsEasing(ComprehensiveGActionFsmAction.GActionType actionType)
        {
            return SupportsDuration(actionType);
        }
        
        private void ShowActionHelp(ComprehensiveGActionFsmAction.GActionType actionType)
        {
            string helpText = GetActionHelpText(actionType);
            if (!string.IsNullOrEmpty(helpText))
            {
                EditorGUILayout.HelpBox(helpText, MessageType.Info);
            }
        }
        
        private string GetActionHelpText(ComprehensiveGActionFsmAction.GActionType actionType)
        {
            switch (actionType)
            {
                case ComprehensiveGActionFsmAction.GActionType.Show:
                    return "显示目标对象（设置active为true）";
                    
                case ComprehensiveGActionFsmAction.GActionType.Hide:
                    return "隐藏目标对象（设置active为false）";
                    
                case ComprehensiveGActionFsmAction.GActionType.RemoveSelf:
                    return "销毁目标对象";
                    
                case ComprehensiveGActionFsmAction.GActionType.FlipX:
                    return "水平翻转目标对象";
                    
                case ComprehensiveGActionFsmAction.GActionType.FlipY:
                    return "垂直翻转目标对象";
                    
                case ComprehensiveGActionFsmAction.GActionType.DelayTime:
                    return "延迟指定时间";
                    
                case ComprehensiveGActionFsmAction.GActionType.MoveTo:
                    return "移动到指定位置";
                    
                case ComprehensiveGActionFsmAction.GActionType.MoveBy:
                    return "相对当前位置移动指定偏移";
                    
                case ComprehensiveGActionFsmAction.GActionType.RotateTo:
                    return "旋转到指定角度";
                    
                case ComprehensiveGActionFsmAction.GActionType.RotateBy:
                    return "相对当前角度旋转指定度数";
                    
                case ComprehensiveGActionFsmAction.GActionType.RotateBy3D:
                    return "3D旋转，分别指定X、Y、Z轴的旋转角度";
                    
                case ComprehensiveGActionFsmAction.GActionType.ScaleTo:
                    return "缩放到指定大小";
                    
                case ComprehensiveGActionFsmAction.GActionType.ScaleBy:
                    return "相对当前大小进行缩放";
                    
                case ComprehensiveGActionFsmAction.GActionType.FadeTo:
                    return "淡入淡出到指定透明度";
                    
                case ComprehensiveGActionFsmAction.GActionType.TintTo:
                    return "改变颜色到指定RGB值";
                    
                case ComprehensiveGActionFsmAction.GActionType.BlendTo:
                    return "混合到指定值";
                    
                case ComprehensiveGActionFsmAction.GActionType.Flash:
                    return "闪烁效果";
                    
                case ComprehensiveGActionFsmAction.GActionType.CanvasGroupAlphaFadeTo:
                    return "CanvasGroup透明度渐变";
                    
                case ComprehensiveGActionFsmAction.GActionType.BezierTo:
                    return "沿贝塞尔曲线移动，需要4个控制点";
                    
                case ComprehensiveGActionFsmAction.GActionType.CallFunc:
                    return "执行回调函数，触发指定事件";
                    
                case ComprehensiveGActionFsmAction.GActionType.Sequence:
                    return "按顺序执行多个动作";
                    
                case ComprehensiveGActionFsmAction.GActionType.Spawn:
                    return "同时执行多个动作";
                    
                case ComprehensiveGActionFsmAction.GActionType.Repeat:
                    return "重复执行动作指定次数";
                    
                case ComprehensiveGActionFsmAction.GActionType.RepeatForever:
                    return "无限重复执行动作";
                    
                default:
                    return "";
            }
        }
    }
}
#endif
