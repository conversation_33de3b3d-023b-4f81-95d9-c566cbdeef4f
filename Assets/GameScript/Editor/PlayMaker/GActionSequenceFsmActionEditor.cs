#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;
using System.Collections.Generic;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionSequenceFsmAction))]
    public class GActionSequenceFsmActionEditor : CustomActionEditor
    {
        private GActionSequenceFsmAction action;
        private SerializedProperty actionTypesProp;
        private SerializedProperty durationsProp;
        private SerializedProperty easeTypesProp;
        private SerializedProperty targetPositionsProp;
        private SerializedProperty useWorldSpacesProp;
        private SerializedProperty targetRotationsProp;
        private SerializedProperty targetScalesProp;
        private SerializedProperty targetAlphasProp;
        
        public override void OnEnable()
        {
            action = target as GActionSequenceFsmAction;
            
            // 获取序列化属性
            actionTypesProp = serializedObject.FindProperty("actionTypes");
            durationsProp = serializedObject.FindProperty("durations");
            easeTypesProp = serializedObject.FindProperty("easeTypes");
            targetPositionsProp = serializedObject.FindProperty("targetPositions");
            useWorldSpacesProp = serializedObject.FindProperty("useWorldSpaces");
            targetRotationsProp = serializedObject.FindProperty("targetRotations");
            targetScalesProp = serializedObject.FindProperty("targetScales");
            targetAlphasProp = serializedObject.FindProperty("targetAlphas");
        }
        
        public override bool OnGUI()
        {
            EditorGUI.BeginChangeCheck();
            
            // 编辑目标对象
            EditField("targetObject");
            
            // 显示动作序列
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Action Sequence", EditorStyles.boldLabel);
            
            serializedObject.Update();
            
            // 添加/删除按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Action"))
            {
                AddAction();
            }
            
            if (actionTypesProp.arraySize > 0 && GUILayout.Button("Remove Last"))
            {
                RemoveLastAction();
            }
            EditorGUILayout.EndHorizontal();
            
            // 显示所有动作
            for (int i = 0; i < actionTypesProp.arraySize; i++)
            {
                EditorGUILayout.Space();
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                
                // 动作标题
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Action " + (i + 1), EditorStyles.boldLabel);
                
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    RemoveActionAt(i);
                    break;
                }
                EditorGUILayout.EndHorizontal();
                
                // 动作类型
                SerializedProperty actionTypeProp = actionTypesProp.GetArrayElementAtIndex(i);
                EditorGUILayout.PropertyField(actionTypeProp, new GUIContent("Action Type"));
                
                GActionSequenceFsmAction.ActionType actionType = 
                    (GActionSequenceFsmAction.ActionType)actionTypeProp.enumValueIndex;
                
                // 根据动作类型显示相关参数
                bool isInstantAction = (actionType == GActionSequenceFsmAction.ActionType.Show || 
                                       actionType == GActionSequenceFsmAction.ActionType.Hide);
                
                // 持续时间（对于非即时动作）
                if (!isInstantAction && i < durationsProp.arraySize)
                {
                    EditorGUILayout.PropertyField(durationsProp.GetArrayElementAtIndex(i), new GUIContent("Duration"));
                    
                    // 缓动类型
                    if (i < easeTypesProp.arraySize)
                    {
                        EditorGUILayout.PropertyField(easeTypesProp.GetArrayElementAtIndex(i), new GUIContent("Ease Type"));
                    }
                }
                
                // 根据动作类型显示特定参数
                switch (actionType)
                {
                    case GActionSequenceFsmAction.ActionType.MoveTo:
                    case GActionSequenceFsmAction.ActionType.MoveBy:
                        if (i < targetPositionsProp.arraySize)
                        {
                            EditorGUILayout.PropertyField(targetPositionsProp.GetArrayElementAtIndex(i), 
                                                         new GUIContent("Target Position"));
                        }
                        
                        if (i < useWorldSpacesProp.arraySize)
                        {
                            EditorGUILayout.PropertyField(useWorldSpacesProp.GetArrayElementAtIndex(i), 
                                                         new GUIContent("Use World Space"));
                        }
                        break;
                        
                    case GActionSequenceFsmAction.ActionType.RotateTo:
                    case GActionSequenceFsmAction.ActionType.RotateBy:
                        if (i < targetRotationsProp.arraySize)
                        {
                            EditorGUILayout.PropertyField(targetRotationsProp.GetArrayElementAtIndex(i), 
                                                         new GUIContent("Target Rotation"));
                        }
                        break;
                        
                    case GActionSequenceFsmAction.ActionType.ScaleTo:
                    case GActionSequenceFsmAction.ActionType.ScaleBy:
                        if (i < targetScalesProp.arraySize)
                        {
                            EditorGUILayout.PropertyField(targetScalesProp.GetArrayElementAtIndex(i), 
                                                         new GUIContent("Target Scale"));
                        }
                        break;
                        
                    case GActionSequenceFsmAction.ActionType.FadeTo:
                        if (i < targetAlphasProp.arraySize)
                        {
                            EditorGUILayout.PropertyField(targetAlphasProp.GetArrayElementAtIndex(i), 
                                                         new GUIContent("Target Alpha"));
                        }
                        break;
                }
                
                EditorGUILayout.EndVertical();
            }
            
            serializedObject.ApplyModifiedProperties();
            
            return EditorGUI.EndChangeCheck();
        }
        
        private void AddAction()
        {
            actionTypesProp.arraySize++;
            durationsProp.arraySize = actionTypesProp.arraySize;
            easeTypesProp.arraySize = actionTypesProp.arraySize;
            targetPositionsProp.arraySize = actionTypesProp.arraySize;
            useWorldSpacesProp.arraySize = actionTypesProp.arraySize;
            targetRotationsProp.arraySize = actionTypesProp.arraySize;
            targetScalesProp.arraySize = actionTypesProp.arraySize;
            targetAlphasProp.arraySize = actionTypesProp.arraySize;
            
            // 设置默认值
            int index = actionTypesProp.arraySize - 1;
            durationsProp.GetArrayElementAtIndex(index).floatValue = 1.0f;
            easeTypesProp.GetArrayElementAtIndex(index).enumValueIndex = (int)EaseType.Linear;
        }
        
        private void RemoveLastAction()
        {
            if (actionTypesProp.arraySize > 0)
            {
                actionTypesProp.arraySize--;
                durationsProp.arraySize = actionTypesProp.arraySize;
                easeTypesProp.arraySize = actionTypesProp.arraySize;
                targetPositionsProp.arraySize = actionTypesProp.arraySize;
                useWorldSpacesProp.arraySize = actionTypesProp.arraySize;
                targetRotationsProp.arraySize = actionTypesProp.arraySize;
                targetScalesProp.arraySize = actionTypesProp.arraySize;
                targetAlphasProp.arraySize = actionTypesProp.arraySize;
            }
        }
        
        private void RemoveActionAt(int index)
        {
            if (index >= 0 && index < actionTypesProp.arraySize)
            {
                // 移除指定索引的元素
                for (int i = index; i < actionTypesProp.arraySize - 1; i++)
                {
                    actionTypesProp.GetArrayElementAtIndex(i).enumValueIndex = 
                        actionTypesProp.GetArrayElementAtIndex(i + 1).enumValueIndex;
                        
                    if (i < durationsProp.arraySize - 1)
                        durationsProp.GetArrayElementAtIndex(i).floatValue = 
                            durationsProp.GetArrayElementAtIndex(i + 1).floatValue;
                            
                    if (i < easeTypesProp.arraySize - 1)
                        easeTypesProp.GetArrayElementAtIndex(i).enumValueIndex = 
                            easeTypesProp.GetArrayElementAtIndex(i + 1).enumValueIndex;
                            
                    // 其他数组也需要类似处理
                }
                
                // 减少数组大小
                actionTypesProp.arraySize--;
                durationsProp.arraySize = actionTypesProp.arraySize;
                easeTypesProp.arraySize = actionTypesProp.arraySize;
                targetPositionsProp.arraySize = actionTypesProp.arraySize;
                useWorldSpacesProp.arraySize = actionTypesProp.arraySize;
                targetRotationsProp.arraySize = actionTypesProp.arraySize;
                targetScalesProp.arraySize = actionTypesProp.arraySize;
                targetAlphasProp.arraySize = actionTypesProp.arraySize;
            }
        }
    }
}
#endif