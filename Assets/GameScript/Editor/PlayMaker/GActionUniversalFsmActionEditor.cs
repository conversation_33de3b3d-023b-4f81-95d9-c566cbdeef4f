#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionUniversalFsmAction))]
    public class GActionUniversalFsmActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as GActionUniversalFsmAction;
            
            EditField("targetObject");
            EditField("actionType");
            
            var actionType = action.actionType;
            
            // Show duration for actions that support it
            if (SupportsDuration(actionType))
            {
                EditField("duration");
            }
            
            // Show easing for actions that support it
            if (SupportsEasing(actionType))
            {
                EditField("easeType");
            }
            
            // Show specific parameters based on action type
            switch (actionType)
            {
                case GActionUniversalFsmAction.ActionType.MoveTo:
                case GActionUniversalFsmAction.ActionType.MoveBy:
                    EditField("targetPosition");
                    EditField("useWorldSpace");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.RotateTo:
                case GActionUniversalFsmAction.ActionType.RotateBy:
                    EditField("targetAngle");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.RotateBy3D:
                    EditField("targetRotation3D");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.ScaleTo:
                case GActionUniversalFsmAction.ActionType.ScaleBy:
                    EditField("targetScale");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.FadeTo:
                case GActionUniversalFsmAction.ActionType.CanvasGroupAlphaFadeTo:
                    EditField("targetAlpha");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.TintTo:
                    EditField("targetColor");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.BlendTo:
                    EditField("blendValue");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.BezierTo:
                    EditorGUILayout.LabelField("Bezier Control Points", EditorStyles.boldLabel);
                    EditField("bezierP0");
                    EditField("bezierP1");
                    EditField("bezierP2");
                    EditField("bezierP3");
                    break;
                    
                case GActionUniversalFsmAction.ActionType.CallFunc:
                    EditField("callbackEvent");
                    break;
            }
            
            // Always show finished event
            EditField("finishedEvent");
            
            // Show action-specific help
            ShowActionHelp(actionType);
            
            return GUI.changed;
        }
        
        private bool SupportsDuration(GActionUniversalFsmAction.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionUniversalFsmAction.ActionType.Show:
                case GActionUniversalFsmAction.ActionType.Hide:
                case GActionUniversalFsmAction.ActionType.RemoveSelf:
                case GActionUniversalFsmAction.ActionType.FlipX:
                case GActionUniversalFsmAction.ActionType.FlipY:
                case GActionUniversalFsmAction.ActionType.CallFunc:
                case GActionUniversalFsmAction.ActionType.Flash:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool SupportsEasing(GActionUniversalFsmAction.ActionType actionType)
        {
            return SupportsDuration(actionType);
        }
        
        private void ShowActionHelp(GActionUniversalFsmAction.ActionType actionType)
        {
            string helpText = GetActionHelpText(actionType);
            if (!string.IsNullOrEmpty(helpText))
            {
                EditorGUILayout.HelpBox(helpText, MessageType.Info);
            }
        }
        
        private string GetActionHelpText(GActionUniversalFsmAction.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionUniversalFsmAction.ActionType.Show:
                    return "显示目标对象（设置active为true）";
                    
                case GActionUniversalFsmAction.ActionType.Hide:
                    return "隐藏目标对象（设置active为false）";
                    
                case GActionUniversalFsmAction.ActionType.RemoveSelf:
                    return "销毁目标对象";
                    
                case GActionUniversalFsmAction.ActionType.FlipX:
                    return "水平翻转目标对象";
                    
                case GActionUniversalFsmAction.ActionType.FlipY:
                    return "垂直翻转目标对象";
                    
                case GActionUniversalFsmAction.ActionType.Flash:
                    return "闪烁效果";
                    
                case GActionUniversalFsmAction.ActionType.DelayTime:
                    return "延迟指定时间";
                    
                case GActionUniversalFsmAction.ActionType.MoveTo:
                    return "移动到指定位置";
                    
                case GActionUniversalFsmAction.ActionType.MoveBy:
                    return "相对当前位置移动指定偏移";
                    
                case GActionUniversalFsmAction.ActionType.RotateTo:
                    return "旋转到指定角度（Z轴）";
                    
                case GActionUniversalFsmAction.ActionType.RotateBy:
                    return "相对当前角度旋转指定度数（Z轴）";
                    
                case GActionUniversalFsmAction.ActionType.RotateBy3D:
                    return "3D旋转，分别指定X、Y、Z轴的旋转角度";
                    
                case GActionUniversalFsmAction.ActionType.ScaleTo:
                    return "缩放到指定大小";
                    
                case GActionUniversalFsmAction.ActionType.ScaleBy:
                    return "相对当前大小进行缩放";
                    
                case GActionUniversalFsmAction.ActionType.FadeTo:
                    return "淡入淡出到指定透明度（适用于SpriteRenderer、Image等）";
                    
                case GActionUniversalFsmAction.ActionType.TintTo:
                    return "改变颜色到指定RGB值";
                    
                case GActionUniversalFsmAction.ActionType.BlendTo:
                    return "混合到指定值";
                    
                case GActionUniversalFsmAction.ActionType.CanvasGroupAlphaFadeTo:
                    return "CanvasGroup透明度渐变";
                    
                case GActionUniversalFsmAction.ActionType.BezierTo:
                    return "沿贝塞尔曲线移动，需要4个控制点（起点、控制点1、控制点2、终点）";
                    
                case GActionUniversalFsmAction.ActionType.CallFunc:
                    return "执行回调函数，触发指定事件";
                    
                default:
                    return "";
            }
        }
    }
}
#endif
