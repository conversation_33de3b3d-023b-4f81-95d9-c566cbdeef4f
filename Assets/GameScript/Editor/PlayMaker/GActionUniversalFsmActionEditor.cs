#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionPlayMaker))]
    public class GActionUniversalFsmActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as GActionPlayMaker;
            
            EditField("targetObject");
            EditField("actionType");
            
            var actionType = action.actionType;
            
            ShowActionHelp(actionType);
            
            if (GActionPlayMaker.SupportsEasing(actionType))
            {
                EditField("duration");
                EditField("easeType");
            }
            
            switch (actionType)
            {
                case GActionPlayMaker.ActionType.MoveTo:
                case GActionPlayMaker.ActionType.MoveBy:
                    EditField("targetPosition");
                    EditField("useWorldSpace");
                    break;
                    
                case GActionPlayMaker.ActionType.RotateTo:
                case GActionPlayMaker.ActionType.RotateBy:
                    EditField("targetAngle");
                    break;
                    
                case GActionPlayMaker.ActionType.RotateBy3D:
                    EditField("targetRotation3D");
                    break;
                    
                case GActionPlayMaker.ActionType.ScaleTo:
                case GActionPlayMaker.ActionType.ScaleBy:
                    EditField("targetScale");
                    break;
                    
                case GActionPlayMaker.ActionType.FadeTo:
                case GActionPlayMaker.ActionType.CanvasGroupAlphaFadeTo:
                    EditField("targetAlpha");
                    break;
                    
                case GActionPlayMaker.ActionType.TintTo:
                    EditField("targetColor");
                    break;
                    
                case GActionPlayMaker.ActionType.BlendTo:
                    EditField("blendValue");
                    break;
                    
                case GActionPlayMaker.ActionType.BezierTo:
                    EditorGUILayout.LabelField("Bezier Control Points", EditorStyles.boldLabel);
                    EditField("bezierP0");
                    EditField("bezierP1");
                    EditField("bezierP2");
                    EditField("bezierP3");
                    break;
                    
                case GActionPlayMaker.ActionType.CallFunc:
                    EditField("callbackEvent");
                    break;
            }
            
            EditField("finishedEvent");
            
            return GUI.changed;
        }
        
        private void ShowActionHelp(GActionPlayMaker.ActionType actionType)
        {
            string helpText = GetActionHelpText(actionType);
            if (!string.IsNullOrEmpty(helpText))
            {
                EditorGUILayout.HelpBox(helpText, MessageType.Info);
            }
        }
        
        private string GetActionHelpText(GActionPlayMaker.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionPlayMaker.ActionType.Show:
                    return "显示目标对象（设置active为true）";
                    
                case GActionPlayMaker.ActionType.Hide:
                    return "隐藏目标对象（设置active为false）。慎用！因为如果目标身上有Fsm，会导致Fsm停止运行";
                    
                case GActionPlayMaker.ActionType.RemoveSelf:
                    return "销毁目标对象";
                    
                case GActionPlayMaker.ActionType.FlipX:
                    return "水平翻转目标对象";
                    
                case GActionPlayMaker.ActionType.FlipY:
                    return "垂直翻转目标对象";
                    
                case GActionPlayMaker.ActionType.Flash:
                    return "闪烁效果";
                    
                case GActionPlayMaker.ActionType.DelayTime:
                    return "延迟指定时间";
                    
                case GActionPlayMaker.ActionType.MoveTo:
                    return "移动到指定位置";
                    
                case GActionPlayMaker.ActionType.MoveBy:
                    return "相对当前位置移动指定偏移";
                    
                case GActionPlayMaker.ActionType.RotateTo:
                    return "旋转到指定角度（Z轴）";
                    
                case GActionPlayMaker.ActionType.RotateBy:
                    return "相对当前角度旋转指定度数（Z轴）";
                    
                case GActionPlayMaker.ActionType.RotateBy3D:
                    return "3D旋转，分别指定X、Y、Z轴的旋转角度";
                    
                case GActionPlayMaker.ActionType.ScaleTo:
                    return "缩放到指定大小";
                    
                case GActionPlayMaker.ActionType.ScaleBy:
                    return "相对当前大小进行缩放";
                    
                case GActionPlayMaker.ActionType.FadeTo:
                    return "淡入淡出到指定透明度（适用于SpriteRenderer、Image等）";
                    
                case GActionPlayMaker.ActionType.TintTo:
                    return "改变颜色到指定RGB值";
                    
                case GActionPlayMaker.ActionType.BlendTo:
                    return "混合到指定值";
                    
                case GActionPlayMaker.ActionType.CanvasGroupAlphaFadeTo:
                    return "CanvasGroup透明度渐变";
                    
                case GActionPlayMaker.ActionType.BezierTo:
                    return "沿贝塞尔曲线移动，需要4个控制点（起点、控制点1、控制点2、终点）";
                    
                case GActionPlayMaker.ActionType.CallFunc:
                    return "执行回调函数，触发指定事件";
                    
                default:
                    return "";
            }
        }
    }
}
#endif
