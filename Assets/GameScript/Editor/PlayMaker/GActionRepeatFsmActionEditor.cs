#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionRepeatFsmAction))]
    public class GActionRepeatFsmActionEditor : CustomActionEditor
    {
        public override bool OnGUI()
        {
            var action = target as GActionRepeatFsmAction;
            
            EditField("targetObject");
            EditField("actionType");
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Repeat Settings", EditorStyles.boldLabel);
            
            EditField("repeatForever");
            
            // Only show repeat count if not repeating forever
            if (!action.repeatForever.Value)
            {
                EditField("repeatCount");
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Action Settings", EditorStyles.boldLabel);
            
            var actionType = action.actionType;
            
            // Show duration for actions that support it
            if (SupportsDuration(actionType))
            {
                EditField("duration");
            }
            
            // Show easing for actions that support it
            if (SupportsEasing(actionType))
            {
                EditField("easeType");
            }
            
            // Show specific parameters based on action type
            switch (actionType)
            {
                case GActionRepeatFsmAction.ActionType.MoveTo:
                case GActionRepeatFsmAction.ActionType.MoveBy:
                    EditField("targetPosition");
                    EditField("useWorldSpace");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.RotateTo:
                case GActionRepeatFsmAction.ActionType.RotateBy:
                    EditField("targetAngle");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.RotateBy3D:
                    EditField("targetRotation3D");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.ScaleTo:
                case GActionRepeatFsmAction.ActionType.ScaleBy:
                    EditField("targetScale");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.FadeTo:
                case GActionRepeatFsmAction.ActionType.CanvasGroupAlphaFadeTo:
                    EditField("targetAlpha");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.TintTo:
                    EditField("targetColor");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.BlendTo:
                    EditField("blendValue");
                    break;
                    
                case GActionRepeatFsmAction.ActionType.CallFunc:
                    EditField("callbackEvent");
                    break;
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Events", EditorStyles.boldLabel);
            
            EditField("onRepeatEvent");
            EditField("finishedEvent");
            
            // Show action-specific help
            ShowActionHelp(actionType);
            
            // Show repeat mode help
            ShowRepeatHelp(action);
            
            return GUI.changed;
        }
        
        private bool SupportsDuration(GActionRepeatFsmAction.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionRepeatFsmAction.ActionType.CallFunc:
                case GActionRepeatFsmAction.ActionType.Flash:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool SupportsEasing(GActionRepeatFsmAction.ActionType actionType)
        {
            return SupportsDuration(actionType);
        }
        
        private void ShowActionHelp(GActionRepeatFsmAction.ActionType actionType)
        {
            string helpText = GetActionHelpText(actionType);
            if (!string.IsNullOrEmpty(helpText))
            {
                EditorGUILayout.HelpBox(helpText, MessageType.Info);
            }
        }
        
        private void ShowRepeatHelp(GActionRepeatFsmAction action)
        {
            string repeatHelp = "";
            
            if (action.repeatForever.Value)
            {
                repeatHelp = "无限重复模式：动作将持续重复执行，直到状态退出或手动停止。";
            }
            else
            {
                repeatHelp = $"有限重复模式：动作将重复执行 {action.repeatCount.Value} 次。";
            }
            
            EditorGUILayout.HelpBox(repeatHelp, MessageType.Info);
            
            // Show event help
            if (action.onRepeatEvent != null || action.finishedEvent != null)
            {
                EditorGUILayout.HelpBox("事件说明：\n• On Repeat Event: 每次单个动作完成时触发\n• Finished Event: 所有重复完成时触发", MessageType.Info);
            }
        }
        
        private string GetActionHelpText(GActionRepeatFsmAction.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionRepeatFsmAction.ActionType.MoveTo:
                    return "重复移动到指定位置（每次都从当前位置移动到目标位置）";
                    
                case GActionRepeatFsmAction.ActionType.MoveBy:
                    return "重复相对移动（每次都在当前位置基础上移动指定偏移）";
                    
                case GActionRepeatFsmAction.ActionType.RotateTo:
                    return "重复旋转到指定角度（每次都从当前角度旋转到目标角度）";
                    
                case GActionRepeatFsmAction.ActionType.RotateBy:
                    return "重复相对旋转（每次都在当前角度基础上旋转指定度数）";
                    
                case GActionRepeatFsmAction.ActionType.RotateBy3D:
                    return "重复3D旋转（每次都在当前角度基础上进行3D旋转）";
                    
                case GActionRepeatFsmAction.ActionType.ScaleTo:
                    return "重复缩放到指定大小（每次都从当前大小缩放到目标大小）";
                    
                case GActionRepeatFsmAction.ActionType.ScaleBy:
                    return "重复相对缩放（每次都在当前大小基础上进行缩放）";
                    
                case GActionRepeatFsmAction.ActionType.FadeTo:
                    return "重复淡入淡出到指定透明度";
                    
                case GActionRepeatFsmAction.ActionType.TintTo:
                    return "重复改变颜色到指定RGB值";
                    
                case GActionRepeatFsmAction.ActionType.BlendTo:
                    return "重复混合到指定值";
                    
                case GActionRepeatFsmAction.ActionType.CanvasGroupAlphaFadeTo:
                    return "重复CanvasGroup透明度渐变";
                    
                case GActionRepeatFsmAction.ActionType.DelayTime:
                    return "重复延迟指定时间（常用于创建定时器效果）";
                    
                case GActionRepeatFsmAction.ActionType.CallFunc:
                    return "重复执行回调函数（每次重复都会触发指定事件）";
                    
                case GActionRepeatFsmAction.ActionType.Flash:
                    return "重复闪烁效果";
                    
                default:
                    return "";
            }
        }
    }
}
#endif
