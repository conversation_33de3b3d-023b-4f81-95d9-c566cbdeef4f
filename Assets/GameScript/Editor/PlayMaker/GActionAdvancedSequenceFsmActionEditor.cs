#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionAdvancedSequenceFsmAction))]
    public class GActionAdvancedSequenceFsmActionEditor : CustomActionEditor
    {
        private SerializedProperty actionTypesProp;
        private SerializedProperty durationsProp;
        private SerializedProperty easeTypesProp;
        private SerializedProperty targetPositionsProp;
        private SerializedProperty useWorldSpacesProp;
        private SerializedProperty targetAnglesProp;
        private SerializedProperty targetRotations3DProp;
        private SerializedProperty targetScalesProp;
        private SerializedProperty targetAlphasProp;
        private SerializedProperty targetColorsProp;
        private SerializedProperty blendValuesProp;
        private SerializedProperty callbackEventsProp;
        
        public override void OnEnable()
        {
            // Get serialized properties
            actionTypesProp = serializedObject.FindProperty("actionTypes");
            durationsProp = serializedObject.FindProperty("durations");
            easeTypesProp = serializedObject.FindProperty("easeTypes");
            targetPositionsProp = serializedObject.FindProperty("targetPositions");
            useWorldSpacesProp = serializedObject.FindProperty("useWorldSpaces");
            targetAnglesProp = serializedObject.FindProperty("targetAngles");
            targetRotations3DProp = serializedObject.FindProperty("targetRotations3D");
            targetScalesProp = serializedObject.FindProperty("targetScales");
            targetAlphasProp = serializedObject.FindProperty("targetAlphas");
            targetColorsProp = serializedObject.FindProperty("targetColors");
            blendValuesProp = serializedObject.FindProperty("blendValues");
            callbackEventsProp = serializedObject.FindProperty("callbackEvents");
        }
        
        public override bool OnGUI()
        {
            var action = target as GActionAdvancedSequenceFsmAction;
            
            EditField("targetObject");
            EditField("sequenceType");
            
            // Show repeat count for repeat types
            if (action.sequenceType == GActionAdvancedSequenceFsmAction.SequenceType.Repeat)
            {
                EditField("repeatCount");
            }
            
            EditField("finishedEvent");
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            // Add/Remove action buttons
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Action"))
            {
                AddAction();
            }
            if (GUILayout.Button("Clear All"))
            {
                if (EditorUtility.DisplayDialog("Clear All Actions", "Are you sure you want to clear all actions?", "Yes", "No"))
                {
                    ClearAllActions();
                }
            }
            EditorGUILayout.EndHorizontal();
            
            // Display all actions
            for (int i = 0; i < actionTypesProp.arraySize; i++)
            {
                EditorGUILayout.Space();
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                
                // Action header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Action {i + 1}", EditorStyles.boldLabel);
                
                if (GUILayout.Button("↑", GUILayout.Width(25)) && i > 0)
                {
                    MoveActionUp(i);
                    break;
                }
                if (GUILayout.Button("↓", GUILayout.Width(25)) && i < actionTypesProp.arraySize - 1)
                {
                    MoveActionDown(i);
                    break;
                }
                if (GUILayout.Button("X", GUILayout.Width(25)))
                {
                    RemoveActionAt(i);
                    break;
                }
                EditorGUILayout.EndHorizontal();
                
                // Action type
                var actionTypeProp = actionTypesProp.GetArrayElementAtIndex(i);
                EditorGUILayout.PropertyField(actionTypeProp, new GUIContent("Action Type"));
                
                var actionType = (GActionAdvancedSequenceFsmAction.ActionType)actionTypeProp.enumValueIndex;
                
                // Show duration and easing for applicable actions
                if (SupportsDuration(actionType))
                {
                    if (i < durationsProp.arraySize)
                    {
                        EditorGUILayout.PropertyField(durationsProp.GetArrayElementAtIndex(i), new GUIContent("Duration"));
                    }
                    
                    if (SupportsEasing(actionType) && i < easeTypesProp.arraySize)
                    {
                        EditorGUILayout.PropertyField(easeTypesProp.GetArrayElementAtIndex(i), new GUIContent("Ease Type"));
                    }
                }
                
                // Show action-specific parameters
                ShowActionParameters(actionType, i);
                
                EditorGUILayout.EndVertical();
            }
            
            serializedObject.ApplyModifiedProperties();
            return EditorGUI.EndChangeCheck();
        }
        
        private void ShowActionParameters(GActionAdvancedSequenceFsmAction.ActionType actionType, int index)
        {
            switch (actionType)
            {
                case GActionAdvancedSequenceFsmAction.ActionType.MoveTo:
                case GActionAdvancedSequenceFsmAction.ActionType.MoveBy:
                    if (index < targetPositionsProp.arraySize)
                        EditorGUILayout.PropertyField(targetPositionsProp.GetArrayElementAtIndex(index), new GUIContent("Target Position"));
                    if (index < useWorldSpacesProp.arraySize)
                        EditorGUILayout.PropertyField(useWorldSpacesProp.GetArrayElementAtIndex(index), new GUIContent("Use World Space"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.RotateTo:
                case GActionAdvancedSequenceFsmAction.ActionType.RotateBy:
                    if (index < targetAnglesProp.arraySize)
                        EditorGUILayout.PropertyField(targetAnglesProp.GetArrayElementAtIndex(index), new GUIContent("Target Angle"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.RotateBy3D:
                    if (index < targetRotations3DProp.arraySize)
                        EditorGUILayout.PropertyField(targetRotations3DProp.GetArrayElementAtIndex(index), new GUIContent("Target Rotation 3D"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.ScaleTo:
                case GActionAdvancedSequenceFsmAction.ActionType.ScaleBy:
                    if (index < targetScalesProp.arraySize)
                        EditorGUILayout.PropertyField(targetScalesProp.GetArrayElementAtIndex(index), new GUIContent("Target Scale"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.FadeTo:
                case GActionAdvancedSequenceFsmAction.ActionType.CanvasGroupAlphaFadeTo:
                    if (index < targetAlphasProp.arraySize)
                        EditorGUILayout.PropertyField(targetAlphasProp.GetArrayElementAtIndex(index), new GUIContent("Target Alpha"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.TintTo:
                    if (index < targetColorsProp.arraySize)
                        EditorGUILayout.PropertyField(targetColorsProp.GetArrayElementAtIndex(index), new GUIContent("Target Color"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.BlendTo:
                    if (index < blendValuesProp.arraySize)
                        EditorGUILayout.PropertyField(blendValuesProp.GetArrayElementAtIndex(index), new GUIContent("Blend Value"));
                    break;
                    
                case GActionAdvancedSequenceFsmAction.ActionType.CallFunc:
                    if (index < callbackEventsProp.arraySize)
                        EditorGUILayout.PropertyField(callbackEventsProp.GetArrayElementAtIndex(index), new GUIContent("Callback Event"));
                    break;
            }
        }
        
        private void AddAction()
        {
            // Increase array sizes
            actionTypesProp.arraySize++;
            durationsProp.arraySize++;
            easeTypesProp.arraySize++;
            targetPositionsProp.arraySize++;
            useWorldSpacesProp.arraySize++;
            targetAnglesProp.arraySize++;
            targetRotations3DProp.arraySize++;
            targetScalesProp.arraySize++;
            targetAlphasProp.arraySize++;
            targetColorsProp.arraySize++;
            blendValuesProp.arraySize++;
            callbackEventsProp.arraySize++;
            
            // Set default values for new elements
            int newIndex = actionTypesProp.arraySize - 1;
            
            if (durationsProp.GetArrayElementAtIndex(newIndex).objectReferenceValue == null)
            {
                // Create new FsmFloat with default value
                var newDuration = new FsmFloat { Value = 1.0f };
                durationsProp.GetArrayElementAtIndex(newIndex).objectReferenceValue = newDuration;
            }
        }
        
        private void RemoveActionAt(int index)
        {
            actionTypesProp.DeleteArrayElementAtIndex(index);
            if (index < durationsProp.arraySize) durationsProp.DeleteArrayElementAtIndex(index);
            if (index < easeTypesProp.arraySize) easeTypesProp.DeleteArrayElementAtIndex(index);
            if (index < targetPositionsProp.arraySize) targetPositionsProp.DeleteArrayElementAtIndex(index);
            if (index < useWorldSpacesProp.arraySize) useWorldSpacesProp.DeleteArrayElementAtIndex(index);
            if (index < targetAnglesProp.arraySize) targetAnglesProp.DeleteArrayElementAtIndex(index);
            if (index < targetRotations3DProp.arraySize) targetRotations3DProp.DeleteArrayElementAtIndex(index);
            if (index < targetScalesProp.arraySize) targetScalesProp.DeleteArrayElementAtIndex(index);
            if (index < targetAlphasProp.arraySize) targetAlphasProp.DeleteArrayElementAtIndex(index);
            if (index < targetColorsProp.arraySize) targetColorsProp.DeleteArrayElementAtIndex(index);
            if (index < blendValuesProp.arraySize) blendValuesProp.DeleteArrayElementAtIndex(index);
            if (index < callbackEventsProp.arraySize) callbackEventsProp.DeleteArrayElementAtIndex(index);
        }
        
        private void ClearAllActions()
        {
            actionTypesProp.arraySize = 0;
            durationsProp.arraySize = 0;
            easeTypesProp.arraySize = 0;
            targetPositionsProp.arraySize = 0;
            useWorldSpacesProp.arraySize = 0;
            targetAnglesProp.arraySize = 0;
            targetRotations3DProp.arraySize = 0;
            targetScalesProp.arraySize = 0;
            targetAlphasProp.arraySize = 0;
            targetColorsProp.arraySize = 0;
            blendValuesProp.arraySize = 0;
            callbackEventsProp.arraySize = 0;
        }
        
        private void MoveActionUp(int index)
        {
            actionTypesProp.MoveArrayElement(index, index - 1);
            if (index < durationsProp.arraySize) durationsProp.MoveArrayElement(index, index - 1);
            if (index < easeTypesProp.arraySize) easeTypesProp.MoveArrayElement(index, index - 1);
            if (index < targetPositionsProp.arraySize) targetPositionsProp.MoveArrayElement(index, index - 1);
            if (index < useWorldSpacesProp.arraySize) useWorldSpacesProp.MoveArrayElement(index, index - 1);
            if (index < targetAnglesProp.arraySize) targetAnglesProp.MoveArrayElement(index, index - 1);
            if (index < targetRotations3DProp.arraySize) targetRotations3DProp.MoveArrayElement(index, index - 1);
            if (index < targetScalesProp.arraySize) targetScalesProp.MoveArrayElement(index, index - 1);
            if (index < targetAlphasProp.arraySize) targetAlphasProp.MoveArrayElement(index, index - 1);
            if (index < targetColorsProp.arraySize) targetColorsProp.MoveArrayElement(index, index - 1);
            if (index < blendValuesProp.arraySize) blendValuesProp.MoveArrayElement(index, index - 1);
            if (index < callbackEventsProp.arraySize) callbackEventsProp.MoveArrayElement(index, index - 1);
        }
        
        private void MoveActionDown(int index)
        {
            actionTypesProp.MoveArrayElement(index, index + 1);
            if (index < durationsProp.arraySize) durationsProp.MoveArrayElement(index, index + 1);
            if (index < easeTypesProp.arraySize) easeTypesProp.MoveArrayElement(index, index + 1);
            if (index < targetPositionsProp.arraySize) targetPositionsProp.MoveArrayElement(index, index + 1);
            if (index < useWorldSpacesProp.arraySize) useWorldSpacesProp.MoveArrayElement(index, index + 1);
            if (index < targetAnglesProp.arraySize) targetAnglesProp.MoveArrayElement(index, index + 1);
            if (index < targetRotations3DProp.arraySize) targetRotations3DProp.MoveArrayElement(index, index + 1);
            if (index < targetScalesProp.arraySize) targetScalesProp.MoveArrayElement(index, index + 1);
            if (index < targetAlphasProp.arraySize) targetAlphasProp.MoveArrayElement(index, index + 1);
            if (index < targetColorsProp.arraySize) targetColorsProp.MoveArrayElement(index, index + 1);
            if (index < blendValuesProp.arraySize) blendValuesProp.MoveArrayElement(index, index + 1);
            if (index < callbackEventsProp.arraySize) callbackEventsProp.MoveArrayElement(index, index + 1);
        }
        
        private bool SupportsDuration(GActionAdvancedSequenceFsmAction.ActionType actionType)
        {
            switch (actionType)
            {
                case GActionAdvancedSequenceFsmAction.ActionType.Show:
                case GActionAdvancedSequenceFsmAction.ActionType.Hide:
                case GActionAdvancedSequenceFsmAction.ActionType.RemoveSelf:
                case GActionAdvancedSequenceFsmAction.ActionType.FlipX:
                case GActionAdvancedSequenceFsmAction.ActionType.FlipY:
                case GActionAdvancedSequenceFsmAction.ActionType.Flash:
                case GActionAdvancedSequenceFsmAction.ActionType.CallFunc:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool SupportsEasing(GActionAdvancedSequenceFsmAction.ActionType actionType)
        {
            return SupportsDuration(actionType);
        }
    }
}
#endif
