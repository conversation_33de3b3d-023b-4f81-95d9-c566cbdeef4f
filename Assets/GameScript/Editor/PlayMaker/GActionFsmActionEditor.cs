#if UNITY_EDITOR
using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;

namespace Fish.PlayMakerEditor
{
    [CustomActionEditor(typeof(GActionFsmAction))]
    public class GActionFsmActionEditor : CustomActionEditor
    {
        public override void OnEnable()
        {
            // 初始化编辑器
        }

        public override bool OnGUI()
        {
            var action = target as GActionFsmAction;
            
            EditField("targetObject");
            EditField("actionType");
            
            // 根据动作类型显示不同的字段
            switch (action.actionType)
            {
                case GActionFsmAction.ActionType.MoveTo:
                case GActionFsmAction.ActionType.MoveBy:
                    EditField("duration");
                    EditField("easeType");
                    EditField("targetPosition");
                    EditField("useWorldSpace");
                    break;
                    
                case GActionFsmAction.ActionType.RotateTo:
                case GActionFsmAction.ActionType.RotateBy:
                    EditField("duration");
                    EditField("easeType");
                    EditField("targetRotation");
                    break;
                    
                case GActionFsmAction.ActionType.ScaleTo:
                case GActionFsmAction.ActionType.ScaleBy:
                    EditField("duration");
                    EditField("easeType");
                    EditField("targetScale");
                    break;
                    
                case GActionFsmAction.ActionType.FadeTo:
                    EditField("duration");
                    EditField("easeType");
                    EditField("targetAlpha");
                    break;
                    
                case GActionFsmAction.ActionType.Show:
                case GActionFsmAction.ActionType.Hide:
                    // 这些动作不需要额外参数
                    break;
            }
            
            return GUI.changed;
        }
    }
}
#endif