using UnityEngine;
using HutongGames.PlayMaker;
using Fish;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("通用GAction动作，支持所有常用的GAction功能")]
    public class GActionUniversalFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("动作类型")]
        public ActionType actionType;
        
        [HutongGames.PlayMaker.Tooltip("持续时间")]
        public FsmFloat duration = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType easeType = EaseType.Linear;
        
        // Movement parameters
        [HutongGames.PlayMaker.Tooltip("目标位置/移动偏移")]
        public FsmVector3 targetPosition;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool useWorldSpace = false;
        
        // Rotation parameters
        [HutongGames.PlayMaker.Tooltip("目标角度/角度偏移")]
        public FsmFloat targetAngle;
        
        [HutongGames.PlayMaker.Tooltip("3D旋转角度 (X, Y, Z)")]
        public FsmVector3 targetRotation3D;
        
        // Scale parameters
        [HutongGames.PlayMaker.Tooltip("目标缩放")]
        public FsmVector3 targetScale = new FsmVector3 { Value = Vector3.one };
        
        // Visual effect parameters
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat targetAlpha = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("目标颜色 (RGB)")]
        public FsmColor targetColor = Color.white;
        
        [HutongGames.PlayMaker.Tooltip("混合值")]
        public FsmFloat blendValue = 1.0f;
        
        // Bezier parameters
        [HutongGames.PlayMaker.Tooltip("贝塞尔曲线起点")]
        public FsmVector3 bezierP0;
        [HutongGames.PlayMaker.Tooltip("贝塞尔曲线控制点1")]
        public FsmVector3 bezierP1;
        [HutongGames.PlayMaker.Tooltip("贝塞尔曲线控制点2")]
        public FsmVector3 bezierP2;
        [HutongGames.PlayMaker.Tooltip("贝塞尔曲线终点")]
        public FsmVector3 bezierP3;
        
        // Callback parameters
        [HutongGames.PlayMaker.Tooltip("回调事件")]
        public FsmEvent callbackEvent;
        
        // Action completion event
        [HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
        public FsmEvent finishedEvent;
        
        private GAction currentAction;
        private GameObject targetGameObject;
        
        public enum ActionType
        {
            // Instant actions
            Show,
            Hide,
            RemoveSelf,
            FlipX,
            FlipY,
            Flash,
            CallFunc,
            
            // Timed actions
            DelayTime,
            MoveTo,
            MoveBy,
            RotateTo,
            RotateBy,
            RotateBy3D,
            ScaleTo,
            ScaleBy,
            FadeTo,
            TintTo,
            BlendTo,
            CanvasGroupAlphaFadeTo,
            BezierTo
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionType = ActionType.MoveTo;
            duration = 1.0f;
            easeType = EaseType.Linear;
            targetPosition = Vector3.zero;
            useWorldSpace = false;
            targetAngle = 0f;
            targetRotation3D = Vector3.zero;
            targetScale = Vector3.one;
            targetAlpha = 1.0f;
            targetColor = Color.white;
            blendValue = 1.0f;
            callbackEvent = null;
            finishedEvent = null;
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null)
            {
                LogError("Target object is null!");
                Finish();
                return;
            }
            
            currentAction = CreateGAction();
            
            if (currentAction != null)
            {
                // Apply easing if the action supports it
                if (SupportsEasing(actionType))
                {
                    currentAction.Ease(easeType);
                }
                
                currentAction.StartWithTarget(targetGameObject);
                
                // For instant actions, finish immediately
                if (IsInstantAction(actionType))
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                LogError("Failed to create GAction for type: " + actionType);
                Finish();
            }
        }
        
        public override void OnUpdate()
        {
            if (currentAction != null)
            {
                currentAction.Step(Time.deltaTime);
                
                if (currentAction.IsDone())
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
                currentAction = null;
            }
        }
        
        private GAction CreateGAction()
        {
            try
            {
                switch (actionType)
                {
                    case ActionType.Show:
                        return GAction.Show();
                        
                    case ActionType.Hide:
                        return GAction.Hide();
                        
                    case ActionType.RemoveSelf:
                        return GAction.RemoveSelf();
                        
                    case ActionType.FlipX:
                        return GAction.FlipX();
                        
                    case ActionType.FlipY:
                        return GAction.FlipY();
                        
                    case ActionType.Flash:
                        return GAction.Flash();
                        
                    case ActionType.CallFunc:
                        return GAction.CallFunc(() => {
                            if (callbackEvent != null)
                                Fsm.Event(callbackEvent);
                        });
                        
                    case ActionType.DelayTime:
                        return GAction.DelayTime(duration.Value);
                        
                    case ActionType.MoveTo:
                        return GAction.MoveTo(duration.Value, targetPosition.Value, useWorldSpace.Value);
                        
                    case ActionType.MoveBy:
                        return GAction.MoveBy(duration.Value, targetPosition.Value, useWorldSpace.Value);
                        
                    case ActionType.RotateTo:
                        return GAction.RotateTo(duration.Value, targetAngle.Value);
                        
                    case ActionType.RotateBy:
                        return GAction.RotateBy(duration.Value, targetAngle.Value);
                        
                    case ActionType.RotateBy3D:
                        return GAction.RotateBy3d(duration.Value, targetRotation3D.Value.x, 
                                                targetRotation3D.Value.y, targetRotation3D.Value.z);
                        
                    case ActionType.ScaleTo:
                        return GAction.ScaleTo(duration.Value, targetScale.Value.x, 
                                             targetScale.Value.y, targetScale.Value.z);
                        
                    case ActionType.ScaleBy:
                        return GAction.ScaleBy(duration.Value, targetScale.Value.x, 
                                             targetScale.Value.y, targetScale.Value.z);
                        
                    case ActionType.FadeTo:
                        return GAction.FadeTo(duration.Value, targetAlpha.Value);
                        
                    case ActionType.TintTo:
                        return GAction.TintTo(duration.Value, targetColor.Value.r, 
                                            targetColor.Value.g, targetColor.Value.b);
                        
                    case ActionType.BlendTo:
                        return GAction.BlendTo(duration.Value, blendValue.Value);
                        
                    case ActionType.CanvasGroupAlphaFadeTo:
                        return GAction.CanvasGroupAlphaFadeTo(duration.Value, targetAlpha.Value);
                        
                    case ActionType.BezierTo:
                        return GAction.BezierTo(duration.Value, bezierP0.Value, bezierP1.Value, 
                                              bezierP2.Value, bezierP3.Value);
                        
                    default:
                        return null;
                }
            }
            catch (System.Exception e)
            {
                LogError("Error creating GAction: " + e.Message);
                return null;
            }
        }
        
        private bool SupportsEasing(ActionType type)
        {
            switch (type)
            {
                case ActionType.Show:
                case ActionType.Hide:
                case ActionType.RemoveSelf:
                case ActionType.FlipX:
                case ActionType.FlipY:
                case ActionType.Flash:
                case ActionType.CallFunc:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool IsInstantAction(ActionType type)
        {
            switch (type)
            {
                case ActionType.Show:
                case ActionType.Hide:
                case ActionType.RemoveSelf:
                case ActionType.FlipX:
                case ActionType.FlipY:
                case ActionType.Flash:
                case ActionType.CallFunc:
                    return true;
                default:
                    return false;
            }
        }
    }
}
