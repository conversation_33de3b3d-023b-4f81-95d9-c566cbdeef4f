using UnityEngine;
using HutongGames.PlayMaker;
using Fish;
using System.Collections.Generic;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("高级GAction序列，支持复杂的动作组合")]
    public class GActionAdvancedSequenceFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("序列类型")]
        public SequenceType sequenceType = SequenceType.Sequence;
        
        [HutongGames.PlayMaker.Tooltip("重复次数（0为无限重复）")]
        public FsmInt repeatCount = 1;
        
        [HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
        public FsmEvent finishedEvent;
        
        // Action definitions
        [HutongGames.PlayMaker.Tooltip("动作列表")]
        [CompoundArray("Actions", "Action Type", "Duration", "Ease Type")]
        public ActionType[] actionTypes;
        public FsmFloat[] durations;
        public EaseType[] easeTypes;
        
        // Movement parameters
        [HutongGames.PlayMaker.Tooltip("移动目标位置")]
        public FsmVector3[] targetPositions;
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool[] useWorldSpaces;
        
        // Rotation parameters
        [HutongGames.PlayMaker.Tooltip("旋转目标角度")]
        public FsmFloat[] targetAngles;
        [HutongGames.PlayMaker.Tooltip("3D旋转角度")]
        public FsmVector3[] targetRotations3D;
        
        // Scale parameters
        [HutongGames.PlayMaker.Tooltip("缩放目标大小")]
        public FsmVector3[] targetScales;
        
        // Visual effect parameters
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat[] targetAlphas;
        [HutongGames.PlayMaker.Tooltip("目标颜色")]
        public FsmColor[] targetColors;
        [HutongGames.PlayMaker.Tooltip("混合值")]
        public FsmFloat[] blendValues;
        
        // Callback parameters
        [HutongGames.PlayMaker.Tooltip("回调事件")]
        public FsmEvent[] callbackEvents;
        
        private GAction currentAction;
        private GameObject targetGameObject;
        
        public enum SequenceType
        {
            Sequence,       // 按顺序执行
            Spawn,          // 同时执行
            Repeat,         // 重复执行序列
            RepeatForever   // 无限重复
        }
        
        public enum ActionType
        {
            Show,
            Hide,
            RemoveSelf,
            FlipX,
            FlipY,
            Flash,
            DelayTime,
            CallFunc,
            MoveTo,
            MoveBy,
            RotateTo,
            RotateBy,
            RotateBy3D,
            ScaleTo,
            ScaleBy,
            FadeTo,
            TintTo,
            BlendTo,
            CanvasGroupAlphaFadeTo
        }
        
        public override void Reset()
        {
            targetObject = null;
            sequenceType = SequenceType.Sequence;
            repeatCount = 1;
            finishedEvent = null;
            
            actionTypes = new ActionType[0];
            durations = new FsmFloat[0];
            easeTypes = new EaseType[0];
            targetPositions = new FsmVector3[0];
            useWorldSpaces = new FsmBool[0];
            targetAngles = new FsmFloat[0];
            targetRotations3D = new FsmVector3[0];
            targetScales = new FsmVector3[0];
            targetAlphas = new FsmFloat[0];
            targetColors = new FsmColor[0];
            blendValues = new FsmFloat[0];
            callbackEvents = new FsmEvent[0];
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null || actionTypes.Length == 0)
            {
                LogError("Target object is null or no actions defined!");
                Finish();
                return;
            }
            
            currentAction = CreateCompositeAction();
            
            if (currentAction != null)
            {
                currentAction.StartWithTarget(targetGameObject);
            }
            else
            {
                LogError("Failed to create composite action!");
                Finish();
            }
        }
        
        public override void OnUpdate()
        {
            if (currentAction != null)
            {
                currentAction.Step(Time.deltaTime);
                
                if (currentAction.IsDone())
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
                currentAction = null;
            }
        }
        
        private GAction CreateCompositeAction()
        {
            var actions = new List<GAction>();
            
            // Create individual actions
            for (int i = 0; i < actionTypes.Length; i++)
            {
                var action = CreateSingleAction(i);
                if (action != null)
                {
                    actions.Add(action);
                }
            }
            
            if (actions.Count == 0)
                return null;
            
            GAction baseAction;
            
            // Create composite action based on type
            switch (sequenceType)
            {
                case SequenceType.Sequence:
                    baseAction = GAction.Sequence(actions.ToArray());
                    break;
                    
                case SequenceType.Spawn:
                    baseAction = GAction.Spawn(actions.ToArray());
                    break;
                    
                case SequenceType.Repeat:
                    var sequenceAction = GAction.Sequence(actions.ToArray());
                    baseAction = GAction.Repeat(sequenceAction, (uint)Mathf.Max(1, repeatCount.Value));
                    break;
                    
                case SequenceType.RepeatForever:
                    var sequenceForeverAction = GAction.Sequence(actions.ToArray());
                    if (sequenceForeverAction is GActionInterval intervalAction)
                    {
                        baseAction = GAction.RepeatForever(intervalAction);
                    }
                    else
                    {
                        baseAction = sequenceForeverAction;
                    }
                    break;
                    
                default:
                    baseAction = GAction.Sequence(actions.ToArray());
                    break;
            }
            
            return baseAction;
        }
        
        private GAction CreateSingleAction(int index)
        {
            if (index >= actionTypes.Length)
                return null;
                
            var actionType = actionTypes[index];
            var duration = GetValueAtIndex(durations, index, 1.0f);
            var easeType = GetValueAtIndex(easeTypes, index, EaseType.Linear);
            
            GAction action = null;
            
            try
            {
                switch (actionType)
                {
                    case ActionType.Show:
                        action = GAction.Show();
                        break;
                        
                    case ActionType.Hide:
                        action = GAction.Hide();
                        break;
                        
                    case ActionType.RemoveSelf:
                        action = GAction.RemoveSelf();
                        break;
                        
                    case ActionType.FlipX:
                        action = GAction.FlipX();
                        break;
                        
                    case ActionType.FlipY:
                        action = GAction.FlipY();
                        break;
                        
                    case ActionType.Flash:
                        action = GAction.Flash();
                        break;
                        
                    case ActionType.DelayTime:
                        action = GAction.DelayTime(duration);
                        break;
                        
                    case ActionType.CallFunc:
                        var callbackEvent = GetValueAtIndex(callbackEvents, index, null);
                        action = GAction.CallFunc(() => {
                            if (callbackEvent != null)
                                Fsm.Event(callbackEvent);
                        });
                        break;
                        
                    case ActionType.MoveTo:
                        var targetPos = GetValueAtIndex(targetPositions, index, Vector3.zero);
                        var useWorld = GetValueAtIndex(useWorldSpaces, index, false);
                        action = GAction.MoveTo(duration, targetPos, useWorld);
                        break;
                        
                    case ActionType.MoveBy:
                        var moveOffset = GetValueAtIndex(targetPositions, index, Vector3.zero);
                        var useWorldMove = GetValueAtIndex(useWorldSpaces, index, false);
                        action = GAction.MoveBy(duration, moveOffset, useWorldMove);
                        break;
                        
                    case ActionType.RotateTo:
                        var targetAngle = GetValueAtIndex(targetAngles, index, 0f);
                        action = GAction.RotateTo(duration, targetAngle);
                        break;
                        
                    case ActionType.RotateBy:
                        var rotateAngle = GetValueAtIndex(targetAngles, index, 0f);
                        action = GAction.RotateBy(duration, rotateAngle);
                        break;
                        
                    case ActionType.RotateBy3D:
                        var rotation3D = GetValueAtIndex(targetRotations3D, index, Vector3.zero);
                        action = GAction.RotateBy3d(duration, rotation3D.x, rotation3D.y, rotation3D.z);
                        break;
                        
                    case ActionType.ScaleTo:
                        var targetScale = GetValueAtIndex(targetScales, index, Vector3.one);
                        action = GAction.ScaleTo(duration, targetScale.x, targetScale.y, targetScale.z);
                        break;
                        
                    case ActionType.ScaleBy:
                        var scaleBy = GetValueAtIndex(targetScales, index, Vector3.one);
                        action = GAction.ScaleBy(duration, scaleBy.x, scaleBy.y, scaleBy.z);
                        break;
                        
                    case ActionType.FadeTo:
                        var alpha = GetValueAtIndex(targetAlphas, index, 1.0f);
                        action = GAction.FadeTo(duration, alpha);
                        break;
                        
                    case ActionType.TintTo:
                        var color = GetValueAtIndex(targetColors, index, Color.white);
                        action = GAction.TintTo(duration, color.r, color.g, color.b);
                        break;
                        
                    case ActionType.BlendTo:
                        var blend = GetValueAtIndex(blendValues, index, 1.0f);
                        action = GAction.BlendTo(duration, blend);
                        break;
                        
                    case ActionType.CanvasGroupAlphaFadeTo:
                        var canvasAlpha = GetValueAtIndex(targetAlphas, index, 1.0f);
                        action = GAction.CanvasGroupAlphaFadeTo(duration, canvasAlpha);
                        break;
                }
                
                // Apply easing if the action supports it
                if (action != null && SupportsEasing(actionType))
                {
                    action.Ease(easeType);
                }
            }
            catch (System.Exception e)
            {
                LogError($"Error creating action {actionType} at index {index}: {e.Message}");
            }
            
            return action;
        }
        
        private T GetValueAtIndex<T>(T[] array, int index, T defaultValue)
        {
            if (array != null && index < array.Length && array[index] != null)
            {
                if (array[index] is FsmFloat fsmFloat)
                    return (T)(object)fsmFloat.Value;
                if (array[index] is FsmVector3 fsmVector3)
                    return (T)(object)fsmVector3.Value;
                if (array[index] is FsmBool fsmBool)
                    return (T)(object)fsmBool.Value;
                if (array[index] is FsmColor fsmColor)
                    return (T)(object)fsmColor.Value;
                if (array[index] is FsmEvent fsmEvent)
                    return (T)(object)fsmEvent;
                    
                return array[index];
            }
            return defaultValue;
        }
        
        private bool SupportsEasing(ActionType actionType)
        {
            switch (actionType)
            {
                case ActionType.Show:
                case ActionType.Hide:
                case ActionType.RemoveSelf:
                case ActionType.FlipX:
                case ActionType.FlipY:
                case ActionType.Flash:
                case ActionType.CallFunc:
                    return false;
                default:
                    return true;
            }
        }
    }
}
