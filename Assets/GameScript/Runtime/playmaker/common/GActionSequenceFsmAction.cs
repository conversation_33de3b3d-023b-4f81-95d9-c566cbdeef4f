using UnityEngine;
using HutongGames.PlayMaker;
using Fish;
using System.Collections.Generic;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("GAction序列执行动作，按顺序执行多个动作")]
    public class GActionSequenceFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("动作序列")]
        [CompoundArray("Actions", "Action Type", "Duration", "Ease Type")]
        public ActionType[] actionTypes;
        public FsmFloat[] durations;
        public EaseType[] easeTypes;
        
        // Movement parameters
        [HutongGames.PlayMaker.Tooltip("移动目标位置")]
        public FsmVector3[] targetPositions;
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool[] useWorldSpaces;
        
        // Rotation parameters
        [HutongGames.PlayMaker.Tooltip("旋转目标角度")]
        public FsmFloat[] targetAngles;
        [HutongGames.PlayMaker.Tooltip("3D旋转角度")]
        public FsmVector3[] targetRotations3D;
        
        // Scale parameters
        [HutongGames.PlayMaker.Tooltip("缩放目标大小")]
        public FsmVector3[] targetScales;
        
        // Visual effect parameters
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat[] targetAlphas;
        [HutongGames.PlayMaker.Tooltip("目标颜色")]
        public FsmColor[] targetColors;
        [HutongGames.PlayMaker.Tooltip("混合值")]
        public FsmFloat[] blendValues;
        
        // Callback parameters
        [HutongGames.PlayMaker.Tooltip("回调事件")]
        public FsmEvent[] callbackEvents;
        
        // Events
        [HutongGames.PlayMaker.Tooltip("每个动作完成时触发的事件")]
        public FsmEvent onActionCompleteEvent;
        
        [HutongGames.PlayMaker.Tooltip("整个序列完成时触发的事件")]
        public FsmEvent finishedEvent;
        
        // Debug
        [HutongGames.PlayMaker.Tooltip("显示当前执行的动作索引")]
        public FsmInt currentActionIndex;
        
        private GAction sequenceAction;
        private GameObject targetGameObject;
        
        public enum ActionType
        {
            // Instant actions
            Show,
            Hide,
            RemoveSelf,
            FlipX,
            FlipY,
            Flash,
            CallFunc,
            
            // Timed actions
            DelayTime,
            MoveTo,
            MoveBy,
            RotateTo,
            RotateBy,
            RotateBy3D,
            ScaleTo,
            ScaleBy,
            FadeTo,
            TintTo,
            BlendTo,
            CanvasGroupAlphaFadeTo
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionTypes = new ActionType[0];
            durations = new FsmFloat[0];
            easeTypes = new EaseType[0];
            targetPositions = new FsmVector3[0];
            useWorldSpaces = new FsmBool[0];
            targetAngles = new FsmFloat[0];
            targetRotations3D = new FsmVector3[0];
            targetScales = new FsmVector3[0];
            targetAlphas = new FsmFloat[0];
            targetColors = new FsmColor[0];
            blendValues = new FsmFloat[0];
            callbackEvents = new FsmEvent[0];
            onActionCompleteEvent = null;
            finishedEvent = null;
            currentActionIndex = null;
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null || actionTypes.Length == 0)
            {
                LogError("Target object is null or no actions defined!");
                Finish();
                return;
            }
            
            sequenceAction = CreateSequenceAction();
            
            if (sequenceAction != null)
            {
                sequenceAction.StartWithTarget(targetGameObject);
                
                // Initialize current action index
                if (currentActionIndex != null)
                    currentActionIndex.Value = 0;
            }
            else
            {
                LogError("Failed to create sequence action!");
                Finish();
            }
        }
        
        public override void OnUpdate()
        {
            if (sequenceAction != null)
            {
                sequenceAction.Step(Time.deltaTime);
                
                if (sequenceAction.IsDone())
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (sequenceAction != null)
            {
                sequenceAction.Stop();
                sequenceAction = null;
            }
        }
        
        private GAction CreateSequenceAction()
        {
            var actions = new List<GAction>();
            
            // Create individual actions
            for (int i = 0; i < actionTypes.Length; i++)
            {
                var action = CreateSingleAction(i);
                if (action != null)
                {
                    actions.Add(action);
                }
            }
            
            if (actions.Count == 0)
                return null;
            
            return GAction.Sequence(actions.ToArray());
        }
        
        private GAction CreateSingleAction(int index)
        {
            if (index >= actionTypes.Length)
                return null;
                
            var actionType = actionTypes[index];
            var duration = GetValueAtIndex(durations, index, 1.0f);
            var easeType = GetValueAtIndex(easeTypes, index, EaseType.Linear);
            
            GAction action = null;
            
            try
            {
                switch (actionType)
                {
                    case ActionType.Show:
                        action = GAction.Show();
                        break;
                        
                    case ActionType.Hide:
                        action = GAction.Hide();
                        break;
                        
                    case ActionType.RemoveSelf:
                        action = GAction.RemoveSelf();
                        break;
                        
                    case ActionType.FlipX:
                        action = GAction.FlipX();
                        break;
                        
                    case ActionType.FlipY:
                        action = GAction.FlipY();
                        break;
                        
                    case ActionType.Flash:
                        action = GAction.Flash();
                        break;
                        
                    case ActionType.DelayTime:
                        action = GAction.DelayTime(duration);
                        break;
                        
                    case ActionType.CallFunc:
                        var callbackEvent = GetValueAtIndex(callbackEvents, index, null);
                        var actionIndex = index; // Capture for closure
                        action = GAction.CallFunc(() => {
                            if (callbackEvent != null)
                                Fsm.Event(callbackEvent);
                            if (onActionCompleteEvent != null)
                                Fsm.Event(onActionCompleteEvent);
                            if (currentActionIndex != null)
                                currentActionIndex.Value = actionIndex + 1;
                        });
                        break;
                        
                    case ActionType.MoveTo:
                        var targetPos = GetValueAtIndex(targetPositions, index, Vector3.zero);
                        var useWorld = GetValueAtIndex(useWorldSpaces, index, false);
                        action = GAction.MoveTo(duration, targetPos, useWorld);
                        break;
                        
                    case ActionType.MoveBy:
                        var moveOffset = GetValueAtIndex(targetPositions, index, Vector3.zero);
                        var useWorldMove = GetValueAtIndex(useWorldSpaces, index, false);
                        action = GAction.MoveBy(duration, moveOffset, useWorldMove);
                        break;
                        
                    case ActionType.RotateTo:
                        var targetAngle = GetValueAtIndex(targetAngles, index, 0f);
                        action = GAction.RotateTo(duration, targetAngle);
                        break;
                        
                    case ActionType.RotateBy:
                        var rotateAngle = GetValueAtIndex(targetAngles, index, 0f);
                        action = GAction.RotateBy(duration, rotateAngle);
                        break;
                        
                    case ActionType.RotateBy3D:
                        var rotation3D = GetValueAtIndex(targetRotations3D, index, Vector3.zero);
                        action = GAction.RotateBy3d(duration, rotation3D.x, rotation3D.y, rotation3D.z);
                        break;
                        
                    case ActionType.ScaleTo:
                        var targetScale = GetValueAtIndex(targetScales, index, Vector3.one);
                        action = GAction.ScaleTo(duration, targetScale.x, targetScale.y, targetScale.z);
                        break;
                        
                    case ActionType.ScaleBy:
                        var scaleBy = GetValueAtIndex(targetScales, index, Vector3.one);
                        action = GAction.ScaleBy(duration, scaleBy.x, scaleBy.y, scaleBy.z);
                        break;
                        
                    case ActionType.FadeTo:
                        var alpha = GetValueAtIndex(targetAlphas, index, 1.0f);
                        action = GAction.FadeTo(duration, alpha);
                        break;
                        
                    case ActionType.TintTo:
                        var color = GetValueAtIndex(targetColors, index, Color.white);
                        action = GAction.TintTo(duration, color.r, color.g, color.b);
                        break;
                        
                    case ActionType.BlendTo:
                        var blend = GetValueAtIndex(blendValues, index, 1.0f);
                        action = GAction.BlendTo(duration, blend);
                        break;
                        
                    case ActionType.CanvasGroupAlphaFadeTo:
                        var canvasAlpha = GetValueAtIndex(targetAlphas, index, 1.0f);
                        action = GAction.CanvasGroupAlphaFadeTo(duration, canvasAlpha);
                        break;
                }
                
                // Apply easing if the action supports it
                if (action != null && SupportsEasing(actionType))
                {
                    action.Ease(easeType);
                }
                
                // Wrap action with completion callback for tracking
                if (action != null && onActionCompleteEvent != null)
                {
                    var originalAction = action;
                    var actionIndex = index;
                    action = GAction.Sequence(
                        originalAction,
                        GAction.CallFunc(() => {
                            if (onActionCompleteEvent != null)
                                Fsm.Event(onActionCompleteEvent);
                            if (currentActionIndex != null)
                                currentActionIndex.Value = actionIndex + 1;
                        })
                    );
                }
            }
            catch (System.Exception e)
            {
                LogError($"Error creating action {actionType} at index {index}: {e.Message}");
            }
            
            return action;
        }
        
        private T GetValueAtIndex<T>(T[] array, int index, T defaultValue)
        {
            if (array != null && index < array.Length && array[index] != null)
            {
                if (array[index] is FsmFloat fsmFloat)
                    return (T)(object)fsmFloat.Value;
                if (array[index] is FsmVector3 fsmVector3)
                    return (T)(object)fsmVector3.Value;
                if (array[index] is FsmBool fsmBool)
                    return (T)(object)fsmBool.Value;
                if (array[index] is FsmColor fsmColor)
                    return (T)(object)fsmColor.Value;
                if (array[index] is FsmEvent fsmEvent)
                    return (T)(object)fsmEvent;
                    
                return array[index];
            }
            return defaultValue;
        }
        
        private bool SupportsEasing(ActionType actionType)
        {
            switch (actionType)
            {
                case ActionType.Show:
                case ActionType.Hide:
                case ActionType.RemoveSelf:
                case ActionType.FlipX:
                case ActionType.FlipY:
                case ActionType.Flash:
                case ActionType.CallFunc:
                    return false;
                default:
                    return true;
            }
        }
        
        // Public methods for external control
        public int GetTotalActionCount()
        {
            return actionTypes != null ? actionTypes.Length : 0;
        }
        
        public int GetCurrentActionIndex()
        {
            return currentActionIndex != null ? currentActionIndex.Value : -1;
        }
        
        public bool IsSequenceRunning()
        {
            return sequenceAction != null && !sequenceAction.IsDone();
        }
    }
}
