using HutongGames.PlayMaker;
using Fish;
using UnityEngine;
using System.Collections.Generic;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("按顺序执行多个GAction动作")]
    public class GActionSequenceFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("动作序列")]
        [CompoundArray("Actions", "Action Type", "Duration")]
        public ActionType[] actionTypes;
        public FsmFloat[] durations;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType[] easeTypes;
        
        // 移动相关参数
        [HutongGames.PlayMaker.Tooltip("移动目标位置")]
        public FsmVector3[] targetPositions;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool[] useWorldSpaces;
        
        // 旋转相关参数
        [HutongGames.PlayMaker.Tooltip("旋转目标角度")]
        public FsmVector3[] targetRotations;
        
        // 缩放相关参数
        [HutongGames.PlayMaker.Tooltip("缩放目标大小")]
        public FsmVector3[] targetScales;
        
        // 透明度相关参数
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat[] targetAlphas;
        
        private GActionSequence sequence;
        private GameObject targetGameObject;
        
        public enum ActionType
        {
            MoveTo,
            MoveBy,
            RotateTo,
            RotateBy,
            ScaleTo,
            ScaleBy,
            FadeTo,
            Show,
            Hide,
            Delay
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionTypes = new ActionType[0];
            durations = new FsmFloat[0];
            easeTypes = new EaseType[0];
            targetPositions = new FsmVector3[0];
            targetRotations = new FsmVector3[0];
            targetScales = new FsmVector3[0];
            targetAlphas = new FsmFloat[0];
            useWorldSpaces = new FsmBool[0];
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null || actionTypes.Length == 0)
            {
                Finish();
                return;
            }
            
            // 创建动作序列
            sequence = GAction.Sequence();
            
            // 添加所有动作到序列
            for (int i = 0; i < actionTypes.Length; i++)
            {
                GAction action = null;
                
                // 确保数组索引有效
                float duration = (i < durations.Length && durations[i] != null) ? durations[i].Value : 1.0f;
                EaseType easeType = (i < easeTypes.Length) ? easeTypes[i] : EaseType.Linear;
                
                // 根据动作类型创建相应的GAction
                switch (actionTypes[i])
                {
                    case ActionType.MoveTo:
                        if (i < targetPositions.Length && targetPositions[i] != null)
                        {
                            bool useWorld = (i < useWorldSpaces.Length && useWorldSpaces[i] != null) ? useWorldSpaces[i].Value : false;
                            action = GAction.MoveTo(duration, targetPositions[i].Value, useWorld);
                        }
                        break;
                        
                    case ActionType.MoveBy:
                        if (i < targetPositions.Length && targetPositions[i] != null)
                        {
                            bool useWorld = (i < useWorldSpaces.Length && useWorldSpaces[i] != null) ? useWorldSpaces[i].Value : false;
                            action = GAction.MoveBy(duration, targetPositions[i].Value, useWorld);
                        }
                        break;
                        
                    case ActionType.RotateTo:
                        if (i < targetRotations.Length && targetRotations[i] != null)
                        {
                            action = GAction.RotateTo(duration, targetRotations[i].Value);
                        }
                        break;
                        
                    case ActionType.RotateBy:
                        if (i < targetRotations.Length && targetRotations[i] != null)
                        {
                            action = GAction.RotateBy(duration, targetRotations[i].Value);
                        }
                        break;
                        
                    case ActionType.ScaleTo:
                        if (i < targetScales.Length && targetScales[i] != null)
                        {
                            action = GAction.ScaleTo(duration, targetScales[i].Value);
                        }
                        break;
                        
                    case ActionType.ScaleBy:
                        if (i < targetScales.Length && targetScales[i] != null)
                        {
                            action = GAction.ScaleBy(duration, targetScales[i].Value);
                        }
                        break;
                        
                    case ActionType.FadeTo:
                        if (i < targetAlphas.Length && targetAlphas[i] != null)
                        {
                            action = GAction.FadeTo(duration, targetAlphas[i].Value);
                        }
                        break;
                        
                    case ActionType.Show:
                        action = GAction.Show();
                        break;
                        
                    case ActionType.Hide:
                        action = GAction.Hide();
                        break;
                        
                    case ActionType.Delay:
                        action = GAction.Delay(duration);
                        break;
                }
                
                // 如果成功创建了动作，设置缓动类型并添加到序列
                if (action != null)
                {
                    // 对于非即时动作，设置缓动类型
                    if (actionTypes[i] != ActionType.Show && actionTypes[i] != ActionType.Hide)
                    {
                        action.Ease(easeType);
                    }
                    
                    sequence.AddAction(action);
                }
            }
            
            // 开始执行序列
            sequence.StartWithTarget(targetGameObject);
        }
        
        public override void OnUpdate()
        {
            if (sequence != null && !sequence.IsDone())
            {
                sequence.Update(Time.deltaTime);
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (sequence != null)
            {
                sequence.Stop();
                sequence = null;
            }
        }
    }
}