using UnityEngine;
using HutongGames.PlayMaker;
using Fish;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("GAction重复执行动作")]
    public class GActionRepeatFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("要重复的动作类型")]
        public ActionType actionType;
        
        [HutongGames.PlayMaker.Tooltip("重复次数（0为无限重复）")]
        public FsmInt repeatCount = 1;
        
        [HutongGames.PlayMaker.Tooltip("是否无限重复")]
        public FsmBool repeatForever = false;
        
        [HutongGames.PlayMaker.Tooltip("单次动作持续时间")]
        public FsmFloat duration = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType easeType = EaseType.Linear;
        
        // Movement parameters
        [HutongGames.PlayMaker.Tooltip("目标位置/移动偏移")]
        public FsmVector3 targetPosition;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool useWorldSpace = false;
        
        // Rotation parameters
        [HutongGames.PlayMaker.Tooltip("目标角度/角度偏移")]
        public FsmFloat targetAngle;
        
        [HutongGames.PlayMaker.Tooltip("3D旋转角度 (X, Y, Z)")]
        public FsmVector3 targetRotation3D;
        
        // Scale parameters
        [HutongGames.PlayMaker.Tooltip("目标缩放")]
        public FsmVector3 targetScale = new FsmVector3 { Value = Vector3.one };
        
        // Visual effect parameters
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat targetAlpha = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("目标颜色 (RGB)")]
        public FsmColor targetColor = Color.white;
        
        [HutongGames.PlayMaker.Tooltip("混合值")]
        public FsmFloat blendValue = 1.0f;
        
        // Callback parameters
        [HutongGames.PlayMaker.Tooltip("回调事件")]
        public FsmEvent callbackEvent;
        
        // Events
        [HutongGames.PlayMaker.Tooltip("每次重复完成时触发的事件")]
        public FsmEvent onRepeatEvent;
        
        [HutongGames.PlayMaker.Tooltip("所有重复完成时触发的事件")]
        public FsmEvent finishedEvent;
        
        private GAction currentAction;
        private GameObject targetGameObject;
        private int currentRepeatCount = 0;
        
        public enum ActionType
        {
            // Movement actions
            MoveTo,
            MoveBy,
            
            // Rotation actions
            RotateTo,
            RotateBy,
            RotateBy3D,
            
            // Scale actions
            ScaleTo,
            ScaleBy,
            
            // Visual effect actions
            FadeTo,
            TintTo,
            BlendTo,
            CanvasGroupAlphaFadeTo,
            
            // Utility actions
            DelayTime,
            CallFunc,
            Flash
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionType = ActionType.MoveTo;
            repeatCount = 1;
            repeatForever = false;
            duration = 1.0f;
            easeType = EaseType.Linear;
            targetPosition = Vector3.zero;
            useWorldSpace = false;
            targetAngle = 0f;
            targetRotation3D = Vector3.zero;
            targetScale = Vector3.one;
            targetAlpha = 1.0f;
            targetColor = Color.white;
            blendValue = 1.0f;
            callbackEvent = null;
            onRepeatEvent = null;
            finishedEvent = null;
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null)
            {
                LogError("Target object is null!");
                Finish();
                return;
            }
            
            currentRepeatCount = 0;
            StartNextRepeat();
        }
        
        public override void OnUpdate()
        {
            if (currentAction != null)
            {
                currentAction.Step(Time.deltaTime);
                
                if (currentAction.IsDone())
                {
                    currentRepeatCount++;
                    
                    // Trigger repeat event
                    if (onRepeatEvent != null)
                        Fsm.Event(onRepeatEvent);
                    
                    // Check if we should continue repeating
                    if (repeatForever.Value || currentRepeatCount < repeatCount.Value)
                    {
                        StartNextRepeat();
                    }
                    else
                    {
                        // All repeats completed
                        if (finishedEvent != null)
                            Fsm.Event(finishedEvent);
                        Finish();
                    }
                }
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
                currentAction = null;
            }
        }
        
        private void StartNextRepeat()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
            }
            
            currentAction = CreateSingleAction();
            
            if (currentAction != null)
            {
                // Apply easing if the action supports it
                if (SupportsEasing(actionType))
                {
                    currentAction.Ease(easeType);
                }
                
                currentAction.StartWithTarget(targetGameObject);
            }
            else
            {
                LogError("Failed to create action for type: " + actionType);
                Finish();
            }
        }
        
        private GAction CreateSingleAction()
        {
            try
            {
                switch (actionType)
                {
                    case ActionType.MoveTo:
                        return GAction.MoveTo(duration.Value, targetPosition.Value, useWorldSpace.Value);
                        
                    case ActionType.MoveBy:
                        return GAction.MoveBy(duration.Value, targetPosition.Value, useWorldSpace.Value);
                        
                    case ActionType.RotateTo:
                        return GAction.RotateTo(duration.Value, targetAngle.Value);
                        
                    case ActionType.RotateBy:
                        return GAction.RotateBy(duration.Value, targetAngle.Value);
                        
                    case ActionType.RotateBy3D:
                        return GAction.RotateBy3d(duration.Value, targetRotation3D.Value.x, 
                                                targetRotation3D.Value.y, targetRotation3D.Value.z);
                        
                    case ActionType.ScaleTo:
                        return GAction.ScaleTo(duration.Value, targetScale.Value.x, 
                                             targetScale.Value.y, targetScale.Value.z);
                        
                    case ActionType.ScaleBy:
                        return GAction.ScaleBy(duration.Value, targetScale.Value.x, 
                                             targetScale.Value.y, targetScale.Value.z);
                        
                    case ActionType.FadeTo:
                        return GAction.FadeTo(duration.Value, targetAlpha.Value);
                        
                    case ActionType.TintTo:
                        return GAction.TintTo(duration.Value, targetColor.Value.r, 
                                            targetColor.Value.g, targetColor.Value.b);
                        
                    case ActionType.BlendTo:
                        return GAction.BlendTo(duration.Value, blendValue.Value);
                        
                    case ActionType.CanvasGroupAlphaFadeTo:
                        return GAction.CanvasGroupAlphaFadeTo(duration.Value, targetAlpha.Value);
                        
                    case ActionType.DelayTime:
                        return GAction.DelayTime(duration.Value);
                        
                    case ActionType.CallFunc:
                        return GAction.CallFunc(() => {
                            if (callbackEvent != null)
                                Fsm.Event(callbackEvent);
                        });
                        
                    case ActionType.Flash:
                        return GAction.Flash();
                        
                    default:
                        return null;
                }
            }
            catch (System.Exception e)
            {
                LogError("Error creating action: " + e.Message);
                return null;
            }
        }
        
        private bool SupportsEasing(ActionType type)
        {
            switch (type)
            {
                case ActionType.CallFunc:
                case ActionType.Flash:
                    return false;
                default:
                    return true;
            }
        }
        
        // Public methods for external control
        public void StopRepeating()
        {
            repeatForever.Value = false;
            repeatCount.Value = currentRepeatCount;
        }
        
        public void SetRepeatCount(int count)
        {
            repeatCount.Value = count;
        }
        
        public int GetCurrentRepeatCount()
        {
            return currentRepeatCount;
        }
    }
}
