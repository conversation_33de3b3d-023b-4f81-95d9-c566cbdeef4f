using UnityEngine;
using HutongGames.PlayMaker;
using Fish;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("执行完整的GAction动作系统，支持所有GAction功能")]
    public class ComprehensiveGActionFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("动作类型")]
        public GActionType actionType;
        
        [HutongGames.PlayMaker.Tooltip("持续时间")]
        public FsmFloat duration = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType easeType = EaseType.Linear;
        
        // Movement parameters
        [HutongGames.PlayMaker.Tooltip("目标位置/移动偏移")]
        public FsmVector3 targetPosition;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool useWorldSpace = false;
        
        // Rotation parameters
        [HutongGames.PlayMaker.Tooltip("目标角度/角度偏移")]
        public FsmFloat targetAngle;
        
        [HutongGames.PlayMaker.Tooltip("3D旋转角度 (X, Y, Z)")]
        public FsmVector3 targetRotation3D;
        
        // Scale parameters
        [HutongGames.PlayMaker.Tooltip("目标缩放")]
        public FsmVector3 targetScale = new FsmVector3 { Value = Vector3.one };
        
        // Visual effect parameters
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat targetAlpha = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("目标颜色 (RGB)")]
        public FsmColor targetColor = Color.white;
        
        [HutongGames.PlayMaker.Tooltip("混合值")]
        public FsmFloat blendValue = 1.0f;
        
        // Bezier parameters
        [HutongGames.PlayMaker.Tooltip("贝塞尔曲线控制点")]
        public FsmVector3 bezierP0, bezierP1, bezierP2, bezierP3;
        
        // Callback parameters
        [HutongGames.PlayMaker.Tooltip("回调事件")]
        public FsmEvent callbackEvent;
        
        // Repeat parameters
        [HutongGames.PlayMaker.Tooltip("重复次数")]
        public FsmInt repeatTimes = 1;
        
        [HutongGames.PlayMaker.Tooltip("是否无限重复")]
        public FsmBool repeatForever = false;
        
        // Composite action parameters
        [HutongGames.PlayMaker.Tooltip("子动作列表（用于Sequence和Spawn）")]
        public ComprehensiveGActionFsmAction[] childActions;
        
        private GAction currentAction;
        private GameObject targetGameObject;
        
        public enum GActionType
        {
            // Basic actions
            Show,
            Hide,
            RemoveSelf,
            FlipX,
            FlipY,
            DelayTime,
            CallFunc,
            
            // Movement actions
            MoveTo,
            MoveBy,
            
            // Rotation actions
            RotateTo,
            RotateBy,
            RotateBy3D,
            
            // Scale actions
            ScaleTo,
            ScaleBy,
            
            // Visual effect actions
            FadeTo,
            TintTo,
            BlendTo,
            Flash,
            CanvasGroupAlphaFadeTo,
            
            // Advanced actions
            BezierTo,
            
            // Composite actions
            Sequence,
            Spawn,
            Repeat,
            RepeatForever
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionType = GActionType.MoveTo;
            duration = 1.0f;
            easeType = EaseType.Linear;
            targetPosition = Vector3.zero;
            useWorldSpace = false;
            targetAngle = 0f;
            targetRotation3D = Vector3.zero;
            targetScale = Vector3.one;
            targetAlpha = 1.0f;
            targetColor = Color.white;
            blendValue = 1.0f;
            callbackEvent = null;
            repeatTimes = 1;
            repeatForever = false;
            childActions = new ComprehensiveGActionFsmAction[0];
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null)
            {
                Finish();
                return;
            }
            
            currentAction = CreateGAction();
            
            if (currentAction != null)
            {
                // Apply easing if applicable
                if (SupportsEasing(actionType))
                {
                    currentAction.Ease(easeType);
                }
                
                currentAction.StartWithTarget(targetGameObject);
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnUpdate()
        {
            if (currentAction != null && !currentAction.IsDone())
            {
                currentAction.Step(Time.deltaTime);
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
                currentAction = null;
            }
        }
        
        private GAction CreateGAction()
        {
            switch (actionType)
            {
                case GActionType.Show:
                    return GAction.Show();
                    
                case GActionType.Hide:
                    return GAction.Hide();
                    
                case GActionType.RemoveSelf:
                    return GAction.RemoveSelf();
                    
                case GActionType.FlipX:
                    return GAction.FlipX();
                    
                case GActionType.FlipY:
                    return GAction.FlipY();
                    
                case GActionType.DelayTime:
                    return GAction.DelayTime(duration.Value);
                    
                case GActionType.CallFunc:
                    return GAction.CallFunc(() => {
                        if (callbackEvent != null)
                            Fsm.Event(callbackEvent);
                    });
                    
                case GActionType.MoveTo:
                    return GAction.MoveTo(duration.Value, targetPosition.Value, useWorldSpace.Value);
                    
                case GActionType.MoveBy:
                    return GAction.MoveBy(duration.Value, targetPosition.Value, useWorldSpace.Value);
                    
                case GActionType.RotateTo:
                    return GAction.RotateTo(duration.Value, targetAngle.Value);
                    
                case GActionType.RotateBy:
                    return GAction.RotateBy(duration.Value, targetAngle.Value);
                    
                case GActionType.RotateBy3D:
                    return GAction.RotateBy3d(duration.Value, targetRotation3D.Value.x, 
                                            targetRotation3D.Value.y, targetRotation3D.Value.z);
                    
                case GActionType.ScaleTo:
                    return GAction.ScaleTo(duration.Value, targetScale.Value.x, 
                                         targetScale.Value.y, targetScale.Value.z);
                    
                case GActionType.ScaleBy:
                    return GAction.ScaleBy(duration.Value, targetScale.Value.x, 
                                         targetScale.Value.y, targetScale.Value.z);
                    
                case GActionType.FadeTo:
                    return GAction.FadeTo(duration.Value, targetAlpha.Value);
                    
                case GActionType.TintTo:
                    return GAction.TintTo(duration.Value, targetColor.Value.r, 
                                        targetColor.Value.g, targetColor.Value.b);
                    
                case GActionType.BlendTo:
                    return GAction.BlendTo(duration.Value, blendValue.Value);
                    
                case GActionType.Flash:
                    return GAction.Flash();
                    
                case GActionType.CanvasGroupAlphaFadeTo:
                    return GAction.CanvasGroupAlphaFadeTo(duration.Value, targetAlpha.Value);
                    
                case GActionType.BezierTo:
                    return GAction.BezierTo(duration.Value, bezierP0.Value, bezierP1.Value,
                                          bezierP2.Value, bezierP3.Value);

                case GActionType.Sequence:
                    return CreateSequenceAction();

                case GActionType.Spawn:
                    return CreateSpawnAction();

                case GActionType.Repeat:
                    return CreateRepeatAction();

                case GActionType.RepeatForever:
                    return CreateRepeatForeverAction();

                default:
                    return null;
            }
        }
        
        private bool SupportsEasing(GActionType type)
        {
            switch (type)
            {
                case GActionType.Show:
                case GActionType.Hide:
                case GActionType.RemoveSelf:
                case GActionType.FlipX:
                case GActionType.FlipY:
                case GActionType.CallFunc:
                case GActionType.Flash:
                case GActionType.Sequence:
                case GActionType.Spawn:
                case GActionType.Repeat:
                case GActionType.RepeatForever:
                    return false;
                default:
                    return true;
            }
        }

        private GAction CreateSequenceAction()
        {
            if (childActions == null || childActions.Length == 0)
                return null;

            var actions = new GAction[childActions.Length];
            for (int i = 0; i < childActions.Length; i++)
            {
                if (childActions[i] != null)
                {
                    actions[i] = childActions[i].CreateGAction();
                }
            }

            return GAction.Sequence(actions);
        }

        private GAction CreateSpawnAction()
        {
            if (childActions == null || childActions.Length == 0)
                return null;

            var actions = new GAction[childActions.Length];
            for (int i = 0; i < childActions.Length; i++)
            {
                if (childActions[i] != null)
                {
                    actions[i] = childActions[i].CreateGAction();
                }
            }

            return GAction.Spawn(actions);
        }

        private GAction CreateRepeatAction()
        {
            if (childActions == null || childActions.Length == 0)
                return null;

            var baseAction = childActions[0].CreateGAction();
            if (baseAction == null)
                return null;

            return GAction.Repeat(baseAction, (uint)repeatTimes.Value);
        }

        private GAction CreateRepeatForeverAction()
        {
            if (childActions == null || childActions.Length == 0)
                return null;

            var baseAction = childActions[0].CreateGAction();
            if (baseAction is GActionInterval intervalAction)
            {
                return GAction.RepeatForever(intervalAction);
            }

            return null;
        }
    }
}
