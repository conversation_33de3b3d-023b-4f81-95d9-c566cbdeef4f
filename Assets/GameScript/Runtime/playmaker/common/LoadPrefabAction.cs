using HutongGames.PlayMaker;
using Fish;
using UnityEngine;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Common")]
    [HutongGames.PlayMaker.Tooltip("加载预制体并存储到变量")]
    public class LoadPrefabAction : FsmStateAction
    {
        [HutongGames.PlayMaker.Tooltip("资源包名")]
        public FsmString bundleName;
        
        [HutongGames.PlayMaker.Tooltip("预制体名")]
        public FsmString prefabName;

        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("父节点")]
        public FsmOwnerDefault parent;
        
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("存储到哪个变量")]
        public FsmGameObject storeVariable;
        
        public override void OnEnter()
        {
            var parentObj = Fsm.GetOwnerDefaultTarget(parent);
            GameObject obj = Asset.LoadPrefab(parentObj.transform, bundleName.Value, prefabName.Value);
            
            if (obj != null)
            {
                storeVariable.Value = obj;
            }
            
            Finish();
        }
    }
}