using HutongGames.PlayMaker;
using Fish;
using UnityEngine;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("执行GAction动作")]
    public class GActionFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("动作类型")]
        public ActionType actionType;
        
        [HutongGames.PlayMaker.Tooltip("持续时间")]
        public FsmFloat duration;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType easeType = EaseType.Linear;
        
        // 根据不同动作类型的参数
        [HutongGames.PlayMaker.Tooltip("移动目标位置")]
        public FsmVector3 targetPosition;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool useWorldSpace;
        
        // 其他参数...
        
        private GAction action;
        private GameObject targetGameObject;
        
        public enum ActionType
        {
            MoveTo,
            MoveBy,
            RotateTo,
            RotateBy,
            ScaleTo,
            ScaleBy,
            FadeTo,
            Show,
            Hide,
            // 添加其他GAction类型...
        }
        
        public override void Reset()
        {
            targetObject = null;
            actionType = ActionType.MoveTo;
            duration = 1.0f;
            easeType = EaseType.Linear;
            targetPosition = null;
            useWorldSpace = false;
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null)
            {
                Finish();
                return;
            }
            
            // 根据选择的动作类型创建相应的GAction
            switch (actionType)
            {
                case ActionType.MoveTo:
                    action = GAction.MoveTo(duration.Value, targetPosition.Value, useWorldSpace.Value);
                    break;
                case ActionType.MoveBy:
                    action = GAction.MoveBy(duration.Value, targetPosition.Value, useWorldSpace.Value);
                    break;
                case ActionType.Show:
                    action = GAction.Show();
                    break;
                case ActionType.Hide:
                    action = GAction.Hide();
                    break;
                // 添加其他case...
            }
            
            // 设置缓动类型
            action.Ease(easeType);
            
            // 开始执行动作
            action.StartWithTarget(targetGameObject);
        }
        
        public override void OnUpdate()
        {
            if (action != null && !action.IsDone())
            {
                action.Update(Time.deltaTime);
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (action != null)
            {
                action.Stop();
                action = null;
            }
        }
    }
}