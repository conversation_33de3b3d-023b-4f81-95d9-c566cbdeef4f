using UnityEngine;
using HutongGames.PlayMaker;
using Fish;

namespace Fish.PlayMaker
{
    [ActionCategory("Fish/Actions")]
    [HutongGames.PlayMaker.Tooltip("快速GAction动作，提供最常用的动作类型")]
    public class GActionQuickFsmAction : FsmStateAction
    {
        [RequiredField]
        [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;
        
        [HutongGames.PlayMaker.Tooltip("快速动作类型")]
        public QuickActionType quickActionType;
        
        [HutongGames.PlayMaker.Tooltip("持续时间")]
        public FsmFloat duration = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("缓动类型")]
        public EaseType easeType = EaseType.Linear;
        
        [HutongGames.PlayMaker.Tooltip("目标值")]
        public FsmVector3 targetValue;
        
        [HutongGames.PlayMaker.Tooltip("目标透明度")]
        public FsmFloat targetAlpha = 1.0f;
        
        [HutongGames.PlayMaker.Tooltip("是否使用世界坐标")]
        public FsmBool useWorldSpace = false;
        
        [HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
        public FsmEvent finishedEvent;
        
        private GAction currentAction;
        private GameObject targetGameObject;
        
        public enum QuickActionType
        {
            // 显示/隐藏
            Show,
            Hide,
            
            // 移动
            MoveTo,
            MoveBy,
            MoveToLocal,
            MoveByLocal,
            
            // 旋转
            RotateTo,
            RotateBy,
            
            // 缩放
            ScaleTo,
            ScaleBy,
            ScaleToZero,
            ScaleToOne,
            
            // 透明度
            FadeIn,
            FadeOut,
            FadeTo,
            
            // 组合动作
            PopIn,          // 从小到大弹出
            PopOut,         // 从大到小消失
            SlideInLeft,    // 从左侧滑入
            SlideInRight,   // 从右侧滑入
            SlideInUp,      // 从上方滑入
            SlideInDown,    // 从下方滑入
            
            // 实用动作
            DelayTime,
            RemoveSelf
        }
        
        public override void Reset()
        {
            targetObject = null;
            quickActionType = QuickActionType.MoveTo;
            duration = 1.0f;
            easeType = EaseType.Linear;
            targetValue = Vector3.zero;
            targetAlpha = 1.0f;
            useWorldSpace = false;
            finishedEvent = null;
        }
        
        public override void OnEnter()
        {
            targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
            
            if (targetGameObject == null)
            {
                LogError("Target object is null!");
                Finish();
                return;
            }
            
            currentAction = CreateQuickAction();
            
            if (currentAction != null)
            {
                // Apply easing if the action supports it
                if (SupportsEasing(quickActionType))
                {
                    currentAction.Ease(easeType);
                }
                
                currentAction.StartWithTarget(targetGameObject);
                
                // For instant actions, finish immediately
                if (IsInstantAction(quickActionType))
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                LogError("Failed to create quick action for type: " + quickActionType);
                Finish();
            }
        }
        
        public override void OnUpdate()
        {
            if (currentAction != null)
            {
                currentAction.Step(Time.deltaTime);
                
                if (currentAction.IsDone())
                {
                    if (finishedEvent != null)
                        Fsm.Event(finishedEvent);
                    Finish();
                }
            }
            else
            {
                Finish();
            }
        }
        
        public override void OnExit()
        {
            if (currentAction != null)
            {
                currentAction.Stop();
                currentAction = null;
            }
        }
        
        private GAction CreateQuickAction()
        {
            try
            {
                switch (quickActionType)
                {
                    case QuickActionType.Show:
                        return GAction.Show();
                        
                    case QuickActionType.Hide:
                        return GAction.Hide();
                        
                    case QuickActionType.MoveTo:
                        return GAction.MoveTo(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.MoveBy:
                        return GAction.MoveBy(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.MoveToLocal:
                        return GAction.MoveTo(duration.Value, targetValue.Value, false);
                        
                    case QuickActionType.MoveByLocal:
                        return GAction.MoveBy(duration.Value, targetValue.Value, false);
                        
                    case QuickActionType.RotateTo:
                        return GAction.RotateTo(duration.Value, targetValue.Value.z);
                        
                    case QuickActionType.RotateBy:
                        return GAction.RotateBy(duration.Value, targetValue.Value.z);
                        
                    case QuickActionType.ScaleTo:
                        return GAction.ScaleTo(duration.Value, targetValue.Value.x, targetValue.Value.y, targetValue.Value.z);
                        
                    case QuickActionType.ScaleBy:
                        return GAction.ScaleBy(duration.Value, targetValue.Value.x, targetValue.Value.y, targetValue.Value.z);
                        
                    case QuickActionType.ScaleToZero:
                        return GAction.ScaleTo(duration.Value, 0f, 0f, 0f);
                        
                    case QuickActionType.ScaleToOne:
                        return GAction.ScaleTo(duration.Value, 1f, 1f, 1f);
                        
                    case QuickActionType.FadeIn:
                        return GAction.FadeTo(duration.Value, 1.0f);
                        
                    case QuickActionType.FadeOut:
                        return GAction.FadeTo(duration.Value, 0.0f);
                        
                    case QuickActionType.FadeTo:
                        return GAction.FadeTo(duration.Value, targetAlpha.Value);
                        
                    case QuickActionType.PopIn:
                        // Start from scale 0 and animate to scale 1
                        targetGameObject.transform.localScale = Vector3.zero;
                        return GAction.ScaleTo(duration.Value, 1f, 1f, 1f);
                        
                    case QuickActionType.PopOut:
                        // Animate from current scale to 0
                        return GAction.ScaleTo(duration.Value, 0f, 0f, 0f);
                        
                    case QuickActionType.SlideInLeft:
                        // Move from left side to target position
                        var leftStartPos = targetGameObject.transform.position + Vector3.left * Screen.width;
                        targetGameObject.transform.position = leftStartPos;
                        return GAction.MoveTo(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.SlideInRight:
                        // Move from right side to target position
                        var rightStartPos = targetGameObject.transform.position + Vector3.right * Screen.width;
                        targetGameObject.transform.position = rightStartPos;
                        return GAction.MoveTo(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.SlideInUp:
                        // Move from top to target position
                        var upStartPos = targetGameObject.transform.position + Vector3.up * Screen.height;
                        targetGameObject.transform.position = upStartPos;
                        return GAction.MoveTo(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.SlideInDown:
                        // Move from bottom to target position
                        var downStartPos = targetGameObject.transform.position + Vector3.down * Screen.height;
                        targetGameObject.transform.position = downStartPos;
                        return GAction.MoveTo(duration.Value, targetValue.Value, useWorldSpace.Value);
                        
                    case QuickActionType.DelayTime:
                        return GAction.DelayTime(duration.Value);
                        
                    case QuickActionType.RemoveSelf:
                        return GAction.RemoveSelf();
                        
                    default:
                        return null;
                }
            }
            catch (System.Exception e)
            {
                LogError("Error creating quick action: " + e.Message);
                return null;
            }
        }
        
        private bool SupportsEasing(QuickActionType type)
        {
            switch (type)
            {
                case QuickActionType.Show:
                case QuickActionType.Hide:
                case QuickActionType.RemoveSelf:
                    return false;
                default:
                    return true;
            }
        }
        
        private bool IsInstantAction(QuickActionType type)
        {
            switch (type)
            {
                case QuickActionType.Show:
                case QuickActionType.Hide:
                case QuickActionType.RemoveSelf:
                    return true;
                default:
                    return false;
            }
        }
    }
}
