{"UNITY_ANIMATION:UnityEngine.Animations.AimConstraint": ["constraintActive", "weight", "aimVector", "upVector", "worldUpType", "worldUpVector", "worldUpObject", "locked", "rotationAtRest", "rotationOffset", "rotationAxis"], "UNITY_ANIMATION:Animation": ["clip", "playAutomatically", "animatePhysics", "cullingType"], "UNITY_ANIMATION:Animator": ["runtimeAnimatorController", "avatar", "applyRootMotion", "updateMode", "cullingMode"], "UNITY_PHYSICS_2D:AreaEffector2D": ["useColliderMask", "colliderMask", "useGlobalAngle", "forceAngle", "forceMagnitude", "forceVariation", "forceTarget", "drag", "angularDrag"], "UNITY_2020_3_OR_NEWER && UNITY_PHYSICS:ArticulationBody": ["mass", "useGravity", "immovable", "linearDamping", "angularDamping", "collisionDetectionMode"], "UNITY_AUDIO:AudioChorusFilter": ["dryMix", "wetMix1", "wetMix2", "wetMix3", "delay", "rate", "depth"], "UNITY_AUDIO:AudioDistortionFilter": ["distortionLevel"], "UNITY_AUDIO:AudioEchoFilter": ["delay", "decayRatio", "dryMix", "wetMix"], "UNITY_AUDIO:AudioHighPassFilter": ["cutoffFrequency", "highpassResonanceQ"], "UNITY_AUDIO:AudioLowPassFilter": ["cutoffFrequency", "lowpassResonanceQ"], "UNITY_AUDIO:AudioReverbFilter": ["reverbPreset", "dryLevel", "room", "roomHF", "roomLF", "decayTime", "decayHFRatio", "reflectionsLevel", "<PERSON><PERSON><PERSON><PERSON>", "reverbLevel", "reverbDelay", "hfReference", "lfReference", "diffusion", "density"], "UNITY_AUDIO:AudioReverbZone": ["minDistance", "maxDistance", "reverbPreset", "room", "roomHF", "roomLF", "decayTime", "decayHFRatio", "reflections", "<PERSON><PERSON><PERSON><PERSON>", "reverb", "reverbDelay", "HFReference", "LFReference", "diffusion", "density"], "UNITY_AUDIO:AudioSource": ["clip", "outputAudioMixerGroup", "mute", "bypassEffects", "bypassListenerEffects", "bypassReverbZones", "playOnAwake", "loop", "priority", "volume", "pitch", "panStereo", "spatialBlend", "reverbZoneMix", "dopplerLevel", "spread", "rolloffMode", "minDistance", "maxDistance"], "UNITY_PHYSICS:BoxCollider": ["isTrigger", "sharedMaterial", "center", "size"], "UNITY_PHYSICS_2D:BoxCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset", "autoTiling", "size", "edgeRadius"], "BillboardRenderer": ["billboard", "shadowCastingMode", "receiveShadows", "lightProbeUsage", "reflectionProbeUsage", "motionVectorGenerationMode", "allowOcclusionWhenDynamic"], "UNITY_PHYSICS_2D:BuoyancyEffector2D": ["useColliderMask", "colliderMask", "density", "surfaceLevel", "linearDrag", "angularDrag", "flowAngle", "flowMagnitude", "flowVariation"], "UNITY_GUI:UnityEngine.UI.Button": ["interactable", "transition", "targetGraphic", "colors", "navigation"], "Camera": ["clearFlags", "backgroundColor", "cullingMask", "orthographic", "fieldOfView", "usePhysicalProperties", "nearClipPlane", "farClipPlane", "rect", "depth", "renderingPath", "targetTexture", "useOcclusionCulling", "allowHDR", "allowMSAA", "allowDynamicResolution", "targetDisplay"], "UNITY_UI:Canvas": ["renderMode", "worldCamera", "pixelPerfect", "sortingOrder", "targetDisplay", "additionalShaderChannels"], "UNITY_UI:CanvasGroup": ["alpha", "interactable", "blocksRaycasts", "ignoreParentGroups"], "UNITY_UI:CanvasRenderer": ["cull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "UNITY_GUI:UnityEngine.UI.CanvasScaler": ["uiScaleMode", "scaleFactor", "referenceResolution", "screenMatchMode", "physicalUnit", "fallbackScreenDPI", "defaultSpriteDPI", "referencePixelsPerUnit"], "UNITY_PHYSICS:CapsuleCollider": ["isTrigger", "sharedMaterial", "center", "radius", "height", "direction"], "UNITY_PHYSICS_2D:CapsuleCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset", "size", "direction"], "UNITY_PHYSICS:CharacterController": ["slopeLimit", "step<PERSON><PERSON><PERSON>", "skinWidth", "minMoveDistance", "center", "radius", "height"], "UNITY_PHYSICS:CharacterJoint": ["connectedBody", "UNITY_2020_2_OR_NEWER:connectedArticulationBody", "anchor", "axis", "autoConfigureConnectedAnchor", "connectedAnchor", "swingAxis", "twistLimitSpring", "lowTwistLimit", "highTwistLimit", "swingLimitSpring", "swing1Limit", "swing2Limit", "enableProjection", "projectionDistance", "projectionAngle", "breakF<PERSON>ce", "breakTorque", "enableCollision", "enablePreprocessing", "massScale", "connectedMassScale"], "UNITY_PHYSICS_2D:CircleCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset", "radius"], "UNITY_CLOTH:Cloth": ["stretchingStiffness", "bendingStiffness", "useTethers", "useGravity", "damping", "externalAcceleration", "randomAcceleration", "worldVelocityScale", "worldAccelerationScale", "friction", "collisionMassScale", "enableContinuousCollision", "useVirtualParticles", "clothSolverFrequency", "sleepT<PERSON><PERSON>old", "capsuleColliders", "sphereColliders"], "UNITY_PHYSICS_2D:CompositeCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset", "geometryType", "generationType", "vertexDistance", "offsetDistance", "edgeRadius"], "UNITY_PHYSICS:ConfigurableJoint": ["connectedBody", "UNITY_2020_2_OR_NEWER:connectedArticulationBody", "anchor", "axis", "autoConfigureConnectedAnchor", "connectedAnchor", "xMotion", "yMotion", "zMotion", "angularXMotion", "angularYMotion", "angularZMotion", "linearLimitSpring", "linearLimit", "angularXLimitSpring", "lowAngularXLimit", "highAngularXLimit", "angularYZLimitSpring", "angularYLimit", "angularZLimit", "targetPosition", "targetVelocity", "xDrive", "yDrive", "zDrive", "targetRotation", "targetAngularVelocity", "rotationDriveMode", "angularXDrive", "angularYZDrive", "slerpDrive", "projectionMode", "projectionDistance", "projectionAngle", "configuredInWorldSpace", "swapBodies", "breakF<PERSON>ce", "breakTorque", "enableCollision", "enablePreprocessing", "massScale", "connectedMassScale"], "UNITY_PHYSICS:ConstantForce": ["force", "relativeForce", "torque", "relativeTorque"], "UNITY_PHYSICS_2D:ConstantForce2D": ["force", "relativeForce", "torque"], "UNITY_GUI:UnityEngine.UI.ContentSizeFitter": ["horizontalFit", "verticalFit"], "UNITY_PHYSICS_2D:DistanceJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "autoConfigureDistance", "distance", "maxDistanceOnly", "breakF<PERSON>ce"], "UNITY_GUI:UnityEngine.UI.Dropdown": ["interactable", "transition", "targetGraphic", "colors", "navigation", "template", "captionText", "captionImage", "itemText", "itemImage", "value", "alphaFadeSpeed", "options"], "UNITY_PHYSICS_2D:EdgeCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset", "edgeRadius", "points", "UNITY_2020_1_OR_NEWER:useAdjacentStartPoint", "UNITY_2020_1_OR_NEWER:useAdjacentEndPoint"], "UNITY_GUI:UnityEngine.EventSystems.EventSystem": ["firstSelectedGameObject", "sendNavigationEvents", "pixelDragThreshold"], "UNITY_PHYSICS_2D:FixedJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "dampingRatio", "frequency", "breakF<PERSON>ce", "breakTorque"], "UNITY_PHYSICS_2D:FrictionJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "max<PERSON><PERSON>ce", "maxTorque", "breakF<PERSON>ce", "breakTorque"], "UNITY_GUI:UnityEngine.UI.GraphicRaycaster": ["ignoreReversedGraphics", "blockingObjects", "UNITY_2020_2_OR_NEWER:blockingMask"], "Grid": ["cellSize", "cellGap", "cellLayout", "cellSwizzle"], "UNITY_GUI:UnityEngine.UI.GridLayoutGroup": ["padding", "cellSize", "spacing", "startCorner", "startAxis", "childAlignment", "constraint"], "UNITY_PHYSICS:HingeJoint": ["connectedBody", "UNITY_2020_2_OR_NEWER:connectedArticulationBody", "anchor", "axis", "autoConfigureConnectedAnchor", "connectedAnchor", "useSpring", "spring", "useMotor", "motor", "useLimits", "limits", "breakF<PERSON>ce", "breakTorque", "enableCollision", "enablePreprocessing", "massScale", "connectedMassScale"], "UNITY_PHYSICS_2D:HingeJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "useMotor", "motor", "useLimits", "limits", "breakF<PERSON>ce", "breakTorque"], "UNITY_GUI:UnityEngine.UI.HorizontalLayoutGroup": ["padding", "spacing", "childAlignment", "childControl<PERSON><PERSON><PERSON>", "childControlHeight", "child<PERSON><PERSON><PERSON><PERSON><PERSON>", "childScaleHeight", "childForceExpandWidth", "childForceExpandHeight"], "UNITY_GUI:UnityEngine.UI.VerticalLayoutGroup": ["padding", "spacing", "childAlignment", "childControl<PERSON><PERSON><PERSON>", "childControlHeight", "child<PERSON><PERSON><PERSON><PERSON><PERSON>", "childScaleHeight", "childForceExpandWidth", "childForceExpandHeight"], "UNITY_GUI:UnityEngine.UI.Image": ["sprite", "material", "color", "raycastTarget", "UNITY_2020_1_OR_NEWER:raycastPadding", "maskable"], "UNITY_GUI:UnityEngine.UI.InputField": ["interactable", "transition", "targetGraphic", "colors", "navigation", "textComponent", "text", "characterLimit", "contentType", "lineType", "placeholder", "caretBlinkRate", "caretWidth", "customCaretColor", "selectionColor", "shouldHideMobileInput", "readOnly", "UNITY_2020_3_OR_NEWER:shouldActivateOnSelect"], "UNITY_GUI:UnityEngine.UI.LayoutElement": ["ignoreLayout", "min<PERSON><PERSON><PERSON>", "minHeight", "preferredWidth", "preferredHeight", "flexibleWidth", "flexibleHeight", "layoutPriority"], "LensFlare": ["flare", "color", "brightness", "fadeSpeed"], "Light": ["type", "range", "spotAngle", "shape", "color", "intensity", "bounceIntensity", "shadows", "cookie", "flare", "renderMode", "cullingMask"], "LightProbeProxyVolume": ["refreshMode", "qualityMode", "UNITY_2020_2_OR_NEWER:dataFormat", "boundingBoxMode", "resolutionMode", "probeDensity", "probePositionMode"], "LineRenderer": ["loop", "startColor", "endColor", "numCornerVertices", "numCapVertices", "alignment", "textureMode", "<PERSON><PERSON><PERSON>", "generateLightingData", "useWorldSpace", "sharedMaterials", "shadowCastingMode", "receiveShadows", "lightProbeUsage", "reflectionProbeUsage", "motionVectorGenerationMode", "allowOcclusionWhenDynamic", "sortingLayerID", "sortingOrder"], "UNITY_ANIMATION:UnityEngine.Animations.LookAtConstraint": ["constraintActive", "weight", "useUpObject", "roll", "worldUpObject", "locked", "rotationAtRest", "rotationOffset"], "UNITY_GUI:UnityEngine.UI.Mask": ["showMaskGraphic"], "UNITY_PHYSICS:MeshCollider": ["convex", "isTrigger", "cookingOptions", "sharedMaterial", "<PERSON><PERSON>esh"], "MeshFilter": ["<PERSON><PERSON>esh"], "MeshRenderer": ["sharedMaterials", "shadowCastingMode", "receiveShadows", "lightProbeUsage", "reflectionProbeUsage", "probeAnchor", "motionVectorGenerationMode", "allowOcclusionWhenDynamic"], "UNITY_AI:UnityEngine.AI.NavMeshAgent": ["agentTypeID", "baseOffset", "speed", "angularSpeed", "acceleration", "stoppingDistance", "autoBraking", "radius", "height", "obstacleAvoidanceType", "avoidancePriority", "autoTraverseOffMeshLink", "autoRepath", "areaMask"], "UNITY_AI:UnityEngine.AI.NavMeshObstacle": ["shape", "center", "size", "carving"], "OcclusionArea": ["size", "center"], "OcclusionPortal": ["open"], "UNITY_AI:UnityEngine.AI.OffMeshLink": ["startTransform", "endTransform", "costOverride", "biDirectional", "activated", "autoUpdatePositions", "area"], "UNITY_GUI:UnityEngine.UI.Outline": ["effectColor", "effectDistance", "useGraphicAlpha"], "UNITY_ANIMATION:UnityEngine.Animations.ParentConstraint": ["constraintActive", "weight", "locked", "translationAtRest", "rotationAtRest", "translationOffsets", "rotationOffsets", "translationAxis", "rotationAxis"], "UNITY_PARTICLE_SYSTEM:ParticleSystemForceField": ["shape", "startRange", "endRange", "directionX", "directionY", "directionZ", "gravity", "gravityFocus", "rotationSpeed", "rotationAttraction", "rotationRandomness", "drag", "multiplyDragByParticleSize", "multiplyDragByParticleVelocity", "vectorField", "vectorFieldSpeed", "vectorFieldAttraction", "UNITY_2021_3_OR_NEWER:enabled"], "UNITY_GUI:UnityEngine.EventSystems.Physics2DRaycaster": ["eventMask", "maxRayIntersections"], "UNITY_GUI:UnityEngine.EventSystems.PhysicsRaycaster": ["eventMask", "maxRayIntersections"], "UNITY_PHYSICS_2D:PlatformEffector2D": ["useColliderMask", "colliderMask", "rotationalOffset", "useOneWay", "useOneWayGrouping", "surfaceArc", "useSideFriction", "useSideBounce", "sideArc"], "UNITY_DIRECTOR:UnityEngine.Playables.PlayableDirector": ["playableAsset", "timeUpdateMode", "playOnAwake", "extrapolationMode", "initialTime", "time"], "UNITY_PHYSICS_2D:PointEffector2D": ["useColliderMask", "colliderMask", "forceMagnitude", "forceVariation", "distanceScale", "forceSource", "forceTarget", "forceMode", "drag", "angularDrag"], "UNITY_PHYSICS_2D:PolygonCollider2D": ["sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "autoTiling", "offset", "pathCount", "points"], "UNITY_ANIMATION:UnityEngine.Animations.PositionConstraint": ["constraintActive", "weight", "locked", "translationAtRest", "translationOffset", "translationAxis"], "Projector": ["nearClipPlane", "farClipPlane", "fieldOfView", "aspectRatio", "orthographic", "orthographicSize", "material", "ignoreLayers"], "UNITY_GUI:UnityEngine.UI.RawImage": ["texture", "color", "raycastTarget", "UNITY_2020_1_OR_NEWER:raycastPadding", "maskable", "uvRect"], "UNITY_GUI:UnityEngine.UI.RectMask2D": ["padding", "softness"], "RectTransform": ["localPosition", "<PERSON><PERSON><PERSON><PERSON>", "anchorMin", "anchorMax", "localRotation", "localScale"], "ReflectionProbe": ["mode", "importance", "intensity", "boxProjection", "blendDistance", "size", "center", "resolution", "hdr", "shadowDistance", "clearFlags", "backgroundColor", "cullingMask", "nearClipPlane", "farClipPlane"], "UNITY_PHYSICS_2D:RelativeJoint2D": ["enableCollision", "connectedBody", "max<PERSON><PERSON>ce", "maxTorque", "correctionScale", "autoConfigureOffset", "linearOffset", "angularOffset", "breakF<PERSON>ce", "breakTorque"], "UNITY_PHYSICS:Rigidbody": ["mass", "drag", "angularDrag", "useGravity", "isKinematic", "interpolation", "collisionDetectionMode", "constraints"], "UNITY_PHYSICS_2D:Rigidbody2D": ["bodyType", "simulated", "useAutoMass", "mass", "drag", "angularDrag", "gravityScale", "collisionDetectionMode", "sleepMode", "interpolation", "constraints"], "UNITY_ANIMATION:UnityEngine.Animations.RotationConstraint": ["constraintActive", "weight", "locked", "rotationAtRest", "rotationOffset", "rotationAxis"], "UNITY_ANIMATION:UnityEngine.Animations.ScaleConstraint": ["constraintActive", "weight", "locked", "scaleAtRest", "scaleOffset", "scalingAxis"], "UNITY_GUI:UnityEngine.UI.Scrollbar": ["interactable", "transition", "targetGraphic", "colors", "navigation", "handleRect", "direction", "value", "size", "numberOfSteps"], "UNITY_GUI:UnityEngine.UI.ScrollRect": ["content", "horizontal", "vertical", "movementType", "elasticity", "inertia", "decelerationRate", "scrollSensitivity", "viewport", "horizontalScrollbar", "verticalScrollbar"], "UNITY_GUI:UnityEngine.UI.Shadow": ["effectColor", "effectDistance", "useGraphicAlpha"], "SkinnedMeshRenderer": ["UNITY_2021_2_OR_NEWER:bounds", "quality", "updateWhenOffscreen", "<PERSON><PERSON>esh", "rootBone", "sharedMaterials", "shadowCastingMode", "receiveShadows", "lightProbeUsage", "reflectionProbeUsage", "motionVectorGenerationMode", "allowOcclusionWhenDynamic"], "Skybox": ["material"], "UNITY_GUI:UnityEngine.UI.Slider": ["interactable", "transition", "targetGraphic", "colors", "navigation", "fillRect", "handleRect", "direction", "minValue", "maxValue", "wholeNumbers", "value"], "UNITY_PHYSICS_2D:SliderJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "autoConfigureAngle", "angle", "useMotor", "motor", "useLimits", "limits", "breakF<PERSON>ce", "breakTorque"], "UnityEngine.Rendering.SortingGroup": ["sortingLayerID", "sortingOrder"], "UNITY_PHYSICS:SphereCollider": ["isTrigger", "sharedMaterial", "center", "radius"], "UNITY_PHYSICS:SpringJoint": ["connectedBody", "UNITY_2020_2_OR_NEWER:connectedArticulationBody", "anchor", "axis", "autoConfigureConnectedAnchor", "connectedAnchor", "spring", "damper", "minDistance", "maxDistance", "tolerance", "breakF<PERSON>ce", "breakTorque", "enableCollision", "enablePreprocessing", "massScale", "connectedMassScale"], "UNITY_PHYSICS_2D:SpringJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "autoConfigureDistance", "distance", "dampingRatio", "frequency", "breakF<PERSON>ce"], "SpriteMask": ["sprite", "<PERSON><PERSON><PERSON><PERSON>", "isCustomRangeActive", "frontSortingOrder"], "SpriteRenderer": ["sprite", "color", "flipX", "flipY", "drawMode", "maskInteraction", "spriteSortPoint", "sharedMaterials", "sortingLayerID", "sortingOrder"], "UNITY_UI:UnityEngine.U2D.SpriteShapeRenderer": ["color", "maskInteraction", "sharedMaterials", "sortingLayerID", "sortingOrder"], "UNITY_GUI:UnityEngine.EventSystems.StandaloneInputModule": ["horizontalAxis", "verticalAxis", "submitButton", "cancelButton", "inputActionsPerSecond", "repeatDelay"], "StreamingController": ["streamingMipmapBias"], "UNITY_PHYSICS_2D:SurfaceEffector2D": ["useColliderMask", "colliderMask", "speed", "speedVariation", "forceScale", "useContactForce", "useFriction", "useBounce"], "UNITY_PHYSICS_2D:TargetJoint2D": ["anchor", "target", "autoConfigureTarget", "max<PERSON><PERSON>ce", "dampingRatio", "frequency", "breakF<PERSON>ce"], "UNITY_TERRAIN_PHYSICS:TerrainCollider": ["isTrigger", "sharedMaterial", "terrainData"], "UNITY_GUI:UnityEngine.UI.Text": ["text", "font", "fontStyle", "fontSize", "lineSpacing", "supportRichText", "alignment", "alignByGeometry", "horizontalOverflow", "verticalOverflow", "resizeTextForBestFit", "color", "raycastTarget", "UNITY_2020_1_OR_NEWER:raycastPadding", "maskable"], "UNITY_TMPRO:TMPro.TextContainer": ["anchorPosition", "width", "height", "margins"], "TextMesh": ["text", "offsetZ", "characterSize", "lineSpacing", "anchor", "alignment", "tabSize", "fontSize", "fontStyle", "richText", "font", "color"], "UNITY_TMPRO:TMPro.TextMeshPro": ["text", "font", "fontStyle", "fontSize", "autoSizeTextContainer", "color", "colorGradientPreset", "colorGradient", "overrideColorTags", "characterSpacing", "wordSpacing", "lineSpacing", "paragraphSpacing", "alignment", "enableWordWrapping", "overflowMode", "horizontalMapping", "verticalMapping", "margin", "sortingLayerID", "sortingOrder", "geometrySortingOrder", "isTextObjectScaleStatic", "isOrthographic", "richText", "parseCtrlCharacters", "useMaxVisibleDescender", "spriteAsset", "styleSheet", "enableKerning", "extraPadding"], "UNITY_TMPRO:TMPro.TextMeshProUGUI": ["text", "font", "fontStyle", "fontSize", "autoSizeTextContainer", "color", "colorGradientPreset", "colorGradient", "overrideColorTags", "characterSpacing", "wordSpacing", "lineSpacing", "paragraphSpacing", "alignment", "enableWordWrapping", "overflowMode", "horizontalMapping", "verticalMapping", "margin", "geometrySortingOrder", "isTextObjectScaleStatic", "isOrthographic", "richText", "parseCtrlCharacters", "useMaxVisibleDescender", "spriteAsset", "styleSheet", "enableKerning", "extraPadding"], "UNITY_TILEMAP:UnityEngine.Tilemaps.Tilemap": ["animationFrameRate", "color", "tileAnchor", "orientation"], "UNITY_TILEMAP:UnityEngine.Tilemaps.TilemapCollider2D": ["maximumTileChangeCount", "extrusionFactor", "sharedMaterial", "isTrigger", "usedByEffector", "!UNITY_2023_1_OR_NEWER:usedByComposite", "UNITY_2023_1_OR_NEWER:compositeOperation", "offset"], "UNITY_TILEMAP:UnityEngine.Tilemaps.TilemapRenderer": ["sortOrder", "mode", "detectChunkCullingBounds", "chunkCullingBounds", "maskInteraction", "sharedMaterials", "sortingLayerID", "sortingOrder"], "UNITY_TMPRO:TMPro.TMP_Dropdown": ["template", "captionText", "captionImage", "placeholder", "itemText", "itemImage", "value", "alphaFadeSpeed", "options"], "UNITY_TMPRO:TMPro.TMP_InputField": ["interactable", "transition", "targetGraphic", "colors", "navigation", "textViewport", "textComponent", "text", "fontAsset", "pointSize", "characterLimit", "contentType", "lineType", "placeholder", "verticalScrollbar", "caretBlinkRate", "caretWidth", "customCaretColor", "selectionColor", "onFocusSelectAll", "resetOnDeActivation", "restoreOriginalTextOnEscape", "shouldHideSoftKeyboard", "shouldHideMobileInput", "readOnly", "richText", "isRichTextEditingAllowed"], "UNITY_TMPRO:TMPro.TMP_ScrollbarEventHandler": ["isSelected"], "UNITY_TMPRO:TMPro.TMP_SubMesh": ["fontAsset", "spriteAsset"], "UNITY_TMPRO:TMPro.TMP_SubMeshUI": ["fontAsset", "spriteAsset"], "UNITY_GUI:UnityEngine.UI.Toggle": ["interactable", "transition", "targetGraphic", "colors", "navigation", "isOn", "toggleTransition", "graphic", "group"], "UNITY_GUI:UnityEngine.UI.ToggleGroup": ["allowSwitchOff"], "TrailRenderer": ["time", "minVertexDistance", "autodestruct", "emitting", "colorGradient", "numCornerVertices", "numCapVertices", "alignment", "textureMode", "generateLightingData", "<PERSON><PERSON><PERSON>", "sharedMaterials", "shadowCastingMode", "receiveShadows", "lightProbeUsage", "reflectionProbeUsage", "motionVectorGenerationMode", "allowOcclusionWhenDynamic", "sortingLayerID", "sortingOrder"], "Transform": ["localPosition", "localRotation", "localScale"], "UNITY_2021_2_OR_NEWER && UNITY_UIELEMENTS:UnityEngine.UIElements.UIDocument": ["panelSettings", "visualTreeAsset", "sortingOrder"], "UNITY_VIDEO:UnityEngine.Video.VideoPlayer": ["source", "clip", "url", "playOnAwake", "waitForFirstFrame", "isLooping", "skipOnDrop", "playbackSpeed", "renderMode", "targetCamera", "targetCameraAlpha", "targetCamera3DLayout", "aspectRatio", "audioOutputMode", "controlledAudioTrackCount"], "UNITY_VEHICLES:WheelCollider": ["mass", "radius", "wheelDampingRate", "suspensionDistance", "forceAppPointDistance", "center", "suspensionSpring", "forwardFriction", "sidewaysFriction"], "UNITY_PHYSICS_2D:WheelJoint2D": ["enableCollision", "connectedBody", "autoConfigureConnectedAnchor", "anchor", "connectedAnchor", "suspension", "useMotor", "motor", "breakF<PERSON>ce", "breakTorque"], "UNITY_WIND:WindZone": ["mode", "windMain", "windTurbulence", "windPulseMagnitude", "windPulseFrequency"]}