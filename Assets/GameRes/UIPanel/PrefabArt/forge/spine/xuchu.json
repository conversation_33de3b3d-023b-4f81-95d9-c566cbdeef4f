{"skeleton": {"hash": "P7EDEa6nDbJaBZRaDaa5ehpaJfY", "spine": "3.8.95", "x": -169.82, "y": -271, "width": 391.38, "height": 607.86, "images": "./T/", "audio": "C:/Users/<USER>/Desktop"}, "bones": [{"name": "root"}, {"name": "BIP", "parent": "root", "x": -6.93, "y": -218.28, "scaleX": 0.22, "scaleY": 0.22}, {"name": "z", "parent": "BIP"}, {"name": "bone", "parent": "z", "x": 157.6, "y": 1152.14}, {"name": "xiong2", "parent": "bone", "length": 193.82, "rotation": 82.59, "x": -1, "y": 8.54}, {"name": "xiong", "parent": "xiong2", "length": 148.52, "rotation": 5.5, "x": 188.26, "y": -2.71}, {"name": "jian4", "parent": "xiong", "x": 247.3, "y": -30.12}, {"name": "gebo", "parent": "z", "length": 336.57, "rotation": -50.52, "x": 240.84, "y": 1567.36}, {"name": "gebojia", "parent": "gebo", "length": 259.22, "rotation": 36.15, "x": 324.85, "y": -53.35}, {"name": "shouxin", "parent": "gebojia", "length": 150.82, "rotation": 7.3, "x": 267.64, "y": -2.95}, {"name": "jian6", "parent": "jian4", "length": 363.93, "rotation": -125.08, "x": 8.04, "y": 18.83}, {"name": "jian7", "parent": "jian4", "length": 270.22, "rotation": -47.55, "x": 41.5, "y": 91.73}, {"name": "jian1", "parent": "xiong", "x": 253.24, "y": 299.56}, {"name": "gebo1", "parent": "jian1", "length": 319.68, "rotation": 147.27, "x": -53.05, "y": 60.43}, {"name": "gebo1bi", "parent": "gebo1", "length": 307.77, "rotation": 21.47, "x": 317.87, "y": 2.62}, {"name": "gebo1bi2", "parent": "gebo1bi", "length": 162.03, "rotation": 28.58, "x": 308.23, "y": 5.02}, {"name": "jian3", "parent": "jian1", "length": 338.66, "rotation": 58.82, "x": 38.13, "y": 4.46}, {"name": "jian5", "parent": "jian1", "length": 353.03, "rotation": 67.08, "x": 145.35, "y": 41.53}, {"name": "jian2", "parent": "gebo1", "length": 350.07, "rotation": -17.76, "x": -19.24, "y": -92.73}, {"name": "guan", "parent": "xiong", "length": 661.56, "rotation": 121.19, "x": 138.11, "y": 308.48}, {"name": "datui1", "parent": "bone", "length": 596, "rotation": -106.28, "x": -234.41, "y": -39.55}, {"name": "xiaotui1", "parent": "datui1", "length": 650.32, "rotation": 3.16, "x": 525.41, "y": 1.71}, {"name": "datui2", "parent": "bone", "length": 625.82, "rotation": -85.42, "x": 86.57, "y": -25.3}, {"name": "xiaotui2", "parent": "datui2", "length": 489.98, "rotation": 0.44, "x": 513.7, "y": 1.82}, {"name": "jiao2", "parent": "z", "x": 327.51, "y": 50.64}, {"name": "jiao1", "parent": "z", "x": -403.45, "y": -94.6}, {"name": "wuqi", "parent": "shouxin", "length": 1052.99, "rotation": -82.93, "x": 87.6, "y": 3.98}, {"name": "dang", "parent": "bone", "length": 492.03, "rotation": -85.28, "x": 7.13, "y": -15.48}, {"name": "tou", "parent": "xiong", "length": 83.47, "rotation": 1.91, "x": 216.52, "y": 101.99}, {"name": "wuqi2", "parent": "wuqi", "x": 1208.33, "y": 48.9}, {"name": "wuqi3", "parent": "wuqi", "x": 736.34, "y": 6.61}, {"name": "wuqi4", "parent": "wuqi", "x": 736.34, "y": 6.61}], "slots": [{"name": "shouxin", "bone": "shouxin", "attachment": "images/shouxin"}, {"name": "bei", "bone": "xiong", "attachment": "images/bei"}, {"name": "wuqi", "bone": "wuqi", "attachment": "images/wuqi"}, {"name": "wuqi2", "bone": "wuqi3", "color": "ffffff00", "attachment": "images/wuqi"}, {"name": "wuqi3", "bone": "wuqi4", "color": "ffffff00", "attachment": "images/wuqi"}, {"name": "jian5", "bone": "jian7", "attachment": "images/jian5"}, {"name": "jian4", "bone": "jian6", "attachment": "images/jian4"}, {"name": "gebo", "bone": "gebo", "attachment": "images/gebo"}, {"name": "gebojia", "bone": "gebojia", "attachment": "images/gebojia"}, {"name": "tou", "bone": "tou", "attachment": "images/tou"}, {"name": "xiong2", "bone": "xiong2", "attachment": "images/xiong2"}, {"name": "xiong", "bone": "xiong", "attachment": "images/xiong"}, {"name": "datui2", "bone": "datui2", "attachment": "images/datui2"}, {"name": "datui1", "bone": "datui1", "attachment": "images/datui1"}, {"name": "dang", "bone": "dang", "attachment": "images/dang"}, {"name": "guan", "bone": "guan", "attachment": "images/guan"}, {"name": "gebo1", "bone": "gebo1", "attachment": "images/gebo1"}, {"name": "gebo1bi", "bone": "gebo1bi", "attachment": "images/gebo1bi"}, {"name": "jian3", "bone": "jian5", "attachment": "images/jian3"}, {"name": "jian2", "bone": "jian2", "attachment": "images/jian2"}, {"name": "jian1", "bone": "jian3", "attachment": "images/jian1"}, {"name": "xiaotui22", "bone": "xiaotui2", "attachment": "images/xiaotui22"}, {"name": "jiao2", "bone": "jiao2", "attachment": "images/jiao2"}, {"name": "xiaotui2", "bone": "xiaotui2", "attachment": "images/xiaotui2"}, {"name": "xiaotui1", "bone": "xiaotui1", "attachment": "images/xiaotui1"}, {"name": "jiao1", "bone": "jiao1", "attachment": "images/jiao1"}], "skins": [{"name": "default", "attachments": {"bei": {"images/bei": {"x": 292.35, "y": 244.84, "rotation": -88.09, "width": 148, "height": 146}}, "dang": {"images/dang": {"type": "mesh", "uvs": [0.4707, 0.11813, 0.4934, 0.11763, 0.5533, 0.1163, 0.56187, 0.08424, 0.63024, 0.08391, 0.63656, 0.00882, 0.71755, 0.00878, 0.73281, 0.08155, 0.86462, 0.08179, 0.87498, 0.14067, 0.88001, 0.16924, 0.88476, 0.19621, 0.8862, 0.23308, 0.96424, 0.42124, 1, 0.6127, 1, 0.62048, 0.97406, 0.6618, 0.85531, 0.74144, 0.71539, 0.83528, 0.71523, 0.97738, 0.65538, 0.98833, 0.64248, 0.80772, 0.52382, 0.81203, 0.53046, 0.9738, 0.46512, 0.99998, 0.45037, 0.98615, 0.3693, 0.84982, 0.04943, 0.71419, 0.00047, 0.69835, 0.01512, 0.44996, 0.09516, 0.30182, 0.11516, 0.22811, 0.11487, 0.14739, 0.17404, 0.07167, 0.31379, 0.07896, 0.31544, 0.05626, 0.35772, 0.00525, 0.4264, 0.00082, 0.85294, 0.24648, 0.78209, 0.23415, 0.72515, 0.18792, 0.86155, 0.42137, 0.83743, 0.60511, 0.71263, 0.68743, 0.85254, 0.70316, 0.74232, 0.5819, 0.75809, 0.45344, 0.75115, 0.33525, 0.69248, 0.22146, 0.60328, 0.33454, 0.61327, 0.45291, 0.60146, 0.57973, 0.56423, 0.64631, 0.5615, 0.75833, 0.52154, 0.78475, 0.71087, 0.75854, 0.71253, 0.64001, 0.56347, 0.69108, 0.85226, 0.21733, 0.86875, 0.19579, 0.87274, 0.16952, 0.86623, 0.14129, 0.85567, 0.12195, 0.83703, 0.11071, 0.80805, 0.11097, 0.79098, 0.13031, 0.78402, 0.15959, 0.79143, 0.18416, 0.80491, 0.21317, 0.82557, 0.22754, 0.80391, 0.17651, 0.81697, 0.20411, 0.83106, 0.21571, 0.80185, 0.13571, 0.79944, 0.15851, 0.8125, 0.11771, 0.42742, 0.80448, 0.36606, 0.2387, 0.34221, 0.19245, 0.49974, 0.94904, 0.4749, 0.56069, 0.4956, 0.52504, 0.51547, 0.46818, 0.51465, 0.42289, 0.49229, 0.39206, 0.46496, 0.37086, 0.45237, 0.1285, 0.78368, 0.42657, 0.74011, 0.66252], "triangles": [8, 64, 7, 63, 64, 8, 75, 64, 63, 62, 63, 8, 86, 37, 0, 65, 7, 64, 73, 65, 64, 75, 73, 64, 62, 8, 9, 61, 62, 9, 74, 65, 73, 66, 7, 65, 66, 65, 74, 60, 61, 9, 60, 9, 10, 73, 62, 74, 62, 70, 74, 67, 66, 74, 67, 74, 70, 5, 6, 4, 40, 7, 66, 40, 66, 67, 36, 34, 35, 86, 78, 34, 36, 37, 86, 86, 34, 36, 61, 60, 70, 61, 70, 62, 63, 73, 75, 62, 73, 63, 59, 71, 60, 60, 71, 70, 71, 67, 70, 68, 67, 71, 72, 71, 59, 58, 72, 59, 59, 60, 10, 11, 59, 10, 4, 6, 7, 40, 48, 7, 48, 4, 7, 69, 71, 72, 68, 71, 69, 31, 32, 33, 39, 40, 67, 39, 67, 68, 77, 78, 86, 58, 12, 38, 59, 12, 58, 11, 12, 59, 2, 4, 48, 4, 2, 3, 49, 2, 48, 39, 48, 40, 47, 48, 39, 49, 48, 47, 39, 68, 69, 39, 87, 47, 1, 85, 86, 77, 86, 85, 0, 1, 86, 49, 84, 85, 41, 38, 12, 41, 12, 13, 85, 1, 49, 49, 1, 2, 83, 84, 49, 87, 38, 41, 69, 72, 58, 69, 58, 38, 69, 38, 39, 39, 38, 87, 50, 49, 47, 46, 50, 47, 83, 49, 50, 87, 46, 47, 82, 83, 50, 84, 82, 85, 82, 84, 83, 81, 80, 85, 82, 81, 85, 51, 82, 50, 81, 82, 51, 45, 50, 46, 51, 50, 45, 42, 87, 41, 87, 45, 46, 87, 88, 45, 13, 42, 41, 56, 51, 45, 52, 81, 51, 80, 81, 52, 15, 16, 14, 88, 56, 45, 87, 42, 88, 13, 14, 42, 16, 42, 14, 43, 56, 88, 57, 80, 52, 44, 42, 16, 88, 42, 44, 76, 27, 29, 28, 29, 27, 78, 31, 33, 78, 33, 34, 17, 44, 16, 79, 76, 80, 52, 56, 43, 56, 52, 51, 43, 57, 52, 55, 57, 43, 53, 57, 55, 53, 54, 57, 80, 76, 77, 80, 77, 85, 57, 54, 80, 21, 53, 55, 22, 54, 53, 54, 79, 80, 22, 53, 21, 55, 43, 17, 88, 17, 43, 44, 17, 88, 18, 55, 17, 21, 55, 18, 77, 29, 30, 31, 78, 77, 77, 30, 31, 76, 29, 77, 26, 27, 76, 22, 79, 54, 25, 26, 76, 79, 22, 23, 18, 20, 21, 79, 25, 76, 18, 19, 20, 24, 25, 79, 24, 79, 23], "vertices": [124.19, -42.51, 124.92, -30.9, 126.86, -0.29, 113.17, 5.24, 115.9, 40.14, 83.24, 46.09, 86.64, 87.42, 119.19, 92.57, 124.85, 159.81, 151.11, 162.97, 163.85, 164.5, 175.88, 165.94, 192.11, 165.35, 277.9, 198.35, 363.37, 209.66, 366.78, 209.38, 383.81, 194.65, 413.72, 131.17, 448.97, 56.37, 511.28, 51.14, 513.56, 20.2, 433.82, 20.17, 430.7, -40.53, 501.92, -43.01, 510.65, -77.3, 503.96, -84.32, 440.76, -120.75, 367.8, -279.05, 358.79, -303.46, 250.49, -286.99, 188.9, -240.78, 157.43, -227.91, 122.02, -225.13, 91.31, -192.2, 100.39, -121.15, 90.51, -119.49, 69.92, -96.07, 70.88, -60.86, 196.58, 147.89, 188.19, 112.18, 165.52, 84.8, 273.63, 145.94, 353.19, 126.98, 384.02, 60.32, 396.82, 131.14, 339, 79.29, 283.33, 91.99, 231.21, 92.73, 178.85, 66.92, 224.67, 17.31, 277, 18.11, 332.11, 7.5, 359.74, -13.92, 408.74, -19.36, 418.65, -40.71, 415.13, 56.85, 363.23, 61.99, 379.34, -15.93, 183.77, 148.59, 175.02, 157.79, 163.67, 160.78, 151.01, 158.48, 142.09, 153.79, 136.37, 144.69, 135.27, 129.89, 143.03, 120.48, 155.57, 115.87, 166.66, 118.76, 179.95, 124.59, 187.12, 134.61, 163.83, 125.4, 176.48, 131.07, 182.16, 137.84, 145.85, 125.83, 155.75, 123.77, 138.41, 131.92, 423.33, -89.45, 172.64, -100.27, 151.36, -110.76, 489.77, -57.79, 318.43, -56.4, 303.67, -44.54, 279.57, -32.34, 259.68, -31.12, 245.21, -41.41, 234.77, -54.59, 127.96, -52.23, 272.63, 106.02, 374.26, 75.24], "hull": 38, "edges": [0, 74, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 42, 44, 32, 34, 34, 36, 0, 2, 2, 4, 110, 106, 20, 22, 16, 18, 18, 20, 50, 152, 160, 158, 154, 152, 50, 52, 78, 174, 174, 176], "width": 512, "height": 440}}, "datui1": {"images/datui1": {"x": 277.69, "y": -18.99, "rotation": 106.28, "width": 561, "height": 664}}, "datui2": {"images/datui2": {"type": "mesh", "uvs": [0.50787, 0, 0.65675, 0.08554, 0.69593, 0.15325, 0.72365, 0.18258, 0.83743, 0.24197, 0.9665, 0.46253, 0.93542, 0.52042, 0.99757, 0.62608, 0.95773, 0.69286, 0.96972, 0.71235, 0.99938, 0.76449, 0.96076, 0.82588, 0.92617, 0.84666, 0.862, 0.82907, 0.83724, 0.79034, 0.75679, 0.79054, 0.74064, 0.9452, 0.62524, 0.9993, 0.30912, 0.99124, 0.08522, 0.94966, 0.05044, 0.92413, 0.01104, 0.77743, 0.00303, 0.65188, 0.05837, 0.63767, 0.05984, 0.62719, 0.05963, 0.58325, 0.18426, 0.46445, 0.20229, 0.4354, 0.2428, 0.17415, 0.29996, 0.13156, 0.33093, 0.07333, 0.33073, 0.06286, 0.31134, 0.03174, 0.36096, 0.01294, 0.47767, 0.00342, 0.68987, 0.39532, 0.57844, 0.19982, 0.50015, 0.10207, 0.77117, 0.54832, 0.77117, 0.58232, 0.80731, 0.7254, 0.80731, 0.39532, 0.88495, 0.51965, 0.91183, 0.53285, 0.86625, 0.57353, 0.92468, 0.67193, 0.88378, 0.69667, 0.93403, 0.80826], "triangles": [31, 32, 33, 34, 30, 31, 37, 34, 0, 37, 0, 1, 34, 31, 33, 37, 30, 34, 29, 30, 37, 2, 36, 37, 2, 37, 1, 36, 2, 3, 4, 35, 36, 4, 36, 3, 41, 35, 4, 29, 37, 36, 28, 29, 36, 41, 4, 5, 42, 41, 5, 6, 42, 5, 43, 42, 6, 38, 35, 41, 38, 41, 42, 44, 38, 42, 44, 42, 43, 39, 38, 44, 27, 35, 38, 27, 28, 36, 27, 36, 35, 43, 6, 7, 44, 43, 7, 39, 24, 25, 45, 44, 7, 8, 45, 7, 45, 39, 44, 46, 45, 8, 46, 39, 45, 40, 39, 46, 21, 22, 23, 47, 14, 46, 46, 8, 9, 40, 46, 14, 47, 46, 10, 38, 39, 27, 26, 39, 25, 15, 40, 14, 39, 26, 27, 24, 39, 40, 40, 23, 24, 15, 23, 40, 21, 23, 15, 46, 9, 10, 11, 47, 10, 13, 14, 47, 12, 13, 47, 12, 47, 11, 18, 20, 21, 20, 18, 19, 18, 21, 15, 16, 18, 15, 17, 18, 16], "vertices": [-78.89, 73.71, -11.29, 120.57, 40.02, 130.21, 62.55, 138.14, 109.77, 174.31, 276.96, 206.23, 319.02, 191.94, 399.12, 207.34, 447.53, 189.48, 462.32, 192.5, 501.81, 199.75, 546.27, 182.63, 560.71, 169.32, 545.87, 147.98, 516.45, 141.64, 514.35, 113.57, 628.6, 98.74, 665.49, 55.26, 650.68, -54.55, 613.58, -130.19, 593.68, -140.81, 483.78, -145.83, 390.44, -141.17, 381.45, -121.01, 373.72, -119.88, 341.13, -117.34, 256.51, -66.8, 235.47, -58.78, 42.85, -29.12, 12.86, -6.64, -29.45, 7.62, -37.23, 8.18, -60.84, 3.26, -73.4, 21.69, -77.2, 62.98, 219.37, 113.71, 71.27, 86.46, -3.41, 64.95, 335.12, 132.98, 360.33, 130.96, 467.46, 135.06, 222.66, 154.68, 317.04, 174.38, 327.58, 182.97, 356.47, 164.65, 431.08, 179.19, 448.28, 163.45, 532.45, 174.35], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 92, 94, 10, 12], "width": 350, "height": 744}}, "gebo": {"images/gebo": {"type": "mesh", "uvs": [0.39617, 0.3829, 0.41505, 0.38705, 0.43924, 0.44556, 0.46911, 0.43018, 0.50996, 0.36321, 0.6447, 0.35375, 0.76297, 0.49489, 0.76941, 0.51487, 0.829, 0.4913, 0.84634, 0.55026, 0.90095, 0.63958, 0.9025, 0.52885, 0.98903, 0.52818, 1, 0.71106, 1, 0.72844, 0.96732, 0.9248, 0.96709, 0.97386, 0.83059, 0.90286, 0.82825, 0.91352, 0.7778, 0.8884, 0.77522, 0.88005, 0.73041, 0.8797, 0.69383, 0.92408, 0.66713, 0.89273, 0.64198, 0.93009, 0.52764, 0.92001, 0.40948, 0.86328, 0.31735, 0.7818, 0.2832, 0.72896, 0.29177, 0.69649, 0.23416, 0.66204, 0.03123, 0.36992, 7e-05, 0.27982, 0.01584, 0.20022, 0.11344, 0.03447, 0.1901, 0, 0.3644, 0.59709, 0.76056, 0.70064, 0.74952, 0.63723, 0.78142, 0.85664, 0.82819, 0.73627, 0.87636, 0.75729, 0.54744, 0.65062], "triangles": [30, 31, 0, 35, 0, 31, 34, 35, 31, 32, 33, 31, 31, 33, 34, 16, 17, 15, 17, 41, 15, 15, 41, 14, 22, 23, 21, 18, 19, 17, 19, 39, 17, 39, 40, 17, 17, 40, 41, 21, 23, 37, 19, 20, 39, 20, 21, 39, 21, 37, 39, 39, 37, 40, 41, 10, 14, 41, 40, 10, 14, 10, 13, 12, 13, 10, 40, 37, 9, 40, 9, 10, 10, 11, 12, 23, 24, 42, 42, 24, 25, 25, 26, 42, 38, 23, 42, 23, 38, 37, 42, 36, 2, 2, 3, 42, 27, 36, 26, 42, 26, 36, 28, 29, 27, 27, 29, 36, 9, 7, 8, 38, 7, 9, 9, 37, 38, 29, 30, 36, 36, 30, 0, 3, 4, 42, 42, 5, 38, 42, 4, 5, 38, 6, 7, 38, 5, 6, 2, 36, 0, 2, 0, 1], "vertices": [2, 8, -16.64, 55.9, 0.432, 7, 295.71, 32.34, 0.568, 2, 8, -4.28, 57.35, 0.432, 7, 304.83, 40.8, 0.568, 1, 8, 41.61, 55.97, 1, 1, 8, 58.98, 66.76, 1, 1, 8, 78.19, 99.26, 1, 1, 8, 162.49, 124.75, 1, 2, 8, 251.28, 89.36, 0.85831, 9, -4.5, 93.64, 0.14169, 2, 8, 257.33, 82.68, 0.77919, 9, 0.66, 86.25, 0.22081, 2, 8, 292.69, 101.45, 0.51223, 9, 38.12, 100.37, 0.48777, 2, 8, 309.5, 81.47, 0.38649, 9, 52.25, 78.41, 0.61351, 2, 8, 352.89, 55.79, 0.03164, 9, 92.03, 47.43, 0.96836, 2, 8, 342.9, 98.85, 5e-05, 9, 87.59, 91.41, 0.99995, 1, 9, 143.63, 98.62, 1, 1, 9, 159.72, 27.09, 1, 1, 9, 160.57, 20.21, 1, 1, 9, 149.03, -60.17, 1, 1, 9, 151.3, -79.62, 1, 1, 9, 59.35, -62.47, 1, 1, 9, 58.36, -66.88, 1, 2, 8, 299.62, -60.34, 0.02583, 9, 24.43, -60.99, 0.97417, 2, 8, 297.17, -57.53, 0.03806, 9, 22.35, -57.89, 0.96194, 2, 8, 268.78, -64.65, 0.30361, 9, -6.71, -61.34, 0.69639, 2, 8, 250.04, -87.73, 0.51169, 9, -28.23, -81.86, 0.48831, 2, 8, 230.04, -79.94, 0.6596, 9, -47.08, -71.59, 0.3404, 2, 8, 217.83, -98.46, 0.7897, 9, -61.54, -88.4, 0.2103, 2, 8, 144.5, -113.09, 0.9663, 9, -136.13, -93.6, 0.0337, 2, 8, 64.15, -110.3, 0.9998, 9, -215.48, -80.62, 0.0002, 1, 8, -2.2, -93.74, 1, 1, 8, -29.04, -78.85, 1, 1, 8, -26.83, -64.91, 1, 2, 8, -84.62, -73.45, 0.58924, 7, 317.13, -112.21, 0.41076, 1, 7, 132.73, -170.06, 1, 1, 7, 92.05, -162.9, 1, 1, 7, 74.08, -134.76, 1, 1, 7, 63.57, -43.52, 1, 1, 7, 84.78, 3.87, 1, 1, 8, 9.27, -14.72, 1, 2, 8, 270.12, 9.44, 0.36844, 9, 4.04, 11.98, 0.63156, 2, 8, 256.86, 32.17, 0.82508, 9, -6.22, 36.2, 0.17492, 2, 8, 298.77, -47.47, 0.02684, 9, 25.22, -48.12, 0.97316, 2, 8, 316.44, 6.63, 0.00615, 9, 49.62, 3.3, 0.99385, 1, 9, 81.87, -1.15, 1, 2, 8, 130.36, -5.75, 0.99979, 9, -136.52, 14.66, 0.00021], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 50, 52, 52, 54, 62, 64, 64, 66, 66, 68, 68, 70, 34, 36, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 60, 62, 56, 58, 58, 60], "width": 653, "height": 399}}, "gebo1": {"images/gebo1": {"type": "mesh", "uvs": [0.53021, 0.00097, 0.65254, 0.00424, 0.77004, 0.02056, 0.87044, 0.04832, 0.94121, 0.07539, 1, 0.10993, 1, 0.11799, 0.90536, 0.24255, 0.70464, 0.37045, 0.67102, 0.38243, 0.63182, 0.4454, 0.57168, 0.44274, 0.56481, 0.471, 0.55697, 0.52579, 0.48265, 0.62237, 0.49532, 0.6321, 0.38489, 0.74441, 0.43906, 0.75685, 0.4821, 0.79394, 0.52812, 0.8676, 0.49017, 0.93822, 0.42312, 0.96337, 0.33268, 0.98644, 0.24949, 0.99942, 0.10838, 0.98256, 0.00236, 0.94732, 0.00641, 0.93508, 0.01531, 0.85133, 0.02315, 0.83979, 0.04956, 0.61888, 0.09456, 0.51945, 0.20734, 0.33393, 0.25754, 0.31651, 0.43119, 0.09453, 0.47615, 0.35107, 0.36539, 0.36901, 0.42985, 0.44213, 0.56169, 0.3827], "triangles": [19, 21, 18, 16, 18, 28, 18, 16, 17, 20, 21, 19, 18, 22, 28, 27, 23, 24, 24, 26, 27, 28, 23, 27, 25, 26, 24, 18, 21, 22, 28, 22, 23, 36, 34, 37, 10, 11, 37, 36, 37, 11, 9, 10, 37, 12, 36, 11, 13, 36, 12, 14, 36, 13, 36, 30, 35, 14, 30, 36, 16, 14, 15, 30, 14, 29, 16, 29, 14, 28, 29, 16, 4, 5, 6, 7, 3, 4, 7, 4, 6, 7, 34, 33, 32, 33, 34, 1, 33, 0, 2, 33, 1, 2, 7, 33, 3, 7, 2, 35, 32, 34, 8, 34, 7, 8, 37, 34, 9, 37, 8, 36, 35, 34, 31, 32, 35, 30, 31, 35], "vertices": [1, 13, 42.83, -145.55, 1, 1, 13, 15.65, -101.58, 1, 1, 13, -1.73, -53.32, 1, 1, 13, -7.42, -5.76, 1, 1, 13, -6.47, 31.2, 1, 1, 13, 2.3, 67.42, 1, 1, 13, 7.64, 71.11, 1, 2, 13, 112.84, 95.24, 0.9998, 14, -156.91, 161.23, 0.0002, 2, 13, 245.69, 84.08, 0.71918, 14, -37.35, 102.22, 0.28082, 2, 13, 261.69, 77.89, 0.5609, 14, -24.73, 90.6, 0.4391, 2, 13, 312.8, 93.1, 0.11951, 14, 28.4, 86.05, 0.88049, 2, 13, 325.47, 71, 0.04292, 14, 32.1, 60.85, 0.95708, 2, 13, 345.83, 81.55, 0.00099, 14, 54.91, 63.21, 0.99901, 1, 14, 98.61, 70.04, 1, 2, 14, 181.46, 57.22, 0.99945, 15, -86.36, 106.48, 0.00055, 2, 14, 187.86, 64.21, 0.9984, 15, -77.39, 109.56, 0.0016, 2, 14, 286.52, 39.44, 0.2351, 15, -2.61, 40.62, 0.7649, 2, 14, 291.06, 63.98, 0.00998, 15, 13.12, 59.99, 0.99002, 1, 15, 46.72, 69.57, 1, 1, 15, 109.05, 72.55, 1, 1, 15, 159.6, 42.01, 1, 1, 15, 171.6, 9.35, 1, 2, 14, 481.24, 62.39, 0.02434, 15, 179.37, -32.38, 0.97566, 2, 14, 499.42, 30.59, 0.07501, 15, 180.13, -69, 0.92499, 2, 14, 499.77, -30.49, 0.21356, 15, 151.22, -122.8, 0.78644, 2, 14, 482.34, -80.52, 0.32275, 15, 111.99, -158.4, 0.67725, 2, 14, 472.36, -81.1, 0.33299, 15, 102.94, -154.13, 0.66701, 2, 14, 405.86, -92.81, 0.59932, 15, 38.94, -132.61, 0.40068, 2, 14, 396.06, -91.7, 0.65538, 15, 30.87, -126.95, 0.34462, 2, 13, 567.37, -29.68, 0.0128, 14, 220.37, -121.38, 0.9872, 2, 13, 490.72, -59.55, 0.13391, 14, 138.11, -121.12, 0.86609, 2, 13, 340.8, -105.29, 0.97037, 14, -18.16, -108.82, 0.02963, 2, 13, 317.22, -95.83, 0.99684, 14, -36.63, -91.39, 0.00316, 1, 13, 128.55, -137.12, 1, 1, 13, 287.67, -4.12, 1, 1, 13, 326.12, -34.36, 1, 1, 14, 45.25, 2.46, 1, 2, 13, 288.1, 40.06, 0.38668, 14, -14, 45.73, 0.61332], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 422, "height": 805}}, "gebo1bi": {"images/gebo1bi": {"type": "mesh", "uvs": [0.58759, 0, 0.71031, 0.17606, 0.75667, 0.24257, 0.91141, 0.14556, 1, 0.14655, 0.78847, 0.47104, 0.79542, 0.60768, 0.41568, 0.90395, 0.21965, 1, 0.20008, 1, 0.07375, 0.86525, 0.10402, 0.71266, 0.09394, 0.67209, 0.13694, 0.54554, 0.10586, 0.44141, 0.18213, 0.30373, 0.37423, 0.09504, 0.47316, 0.10125, 0.49133, 0.0844, 0.57565, 0.00222, 0.64054, 0.42201, 0.92737, 0.15481, 0.67501, 0.54592, 0.33389, 0.82675, 0.15393, 0.90026, 0.54521, 0.49658, 0.5537, 0.38458, 0.28704, 0.77207, 0.16135, 0.81712, 0.12398, 0.80039, 0.28607, 0.64891, 0.3695, 0.68126, 0.30256, 0.53126, 0.39764, 0.41362, 0.41704, 0.29597, 0.46937, 0.23018, 0.51902, 0.22591, 0.59574, 0.15065, 0.67811, 0.16348, 0.54497, 0.249, 0.4784, 0.17032, 0.6296, 0.33024, 0.61831, 0.35675], "triangles": [38, 37, 0, 19, 0, 37, 18, 19, 37, 17, 18, 37, 21, 3, 4, 0, 1, 38, 40, 17, 37, 36, 40, 37, 40, 35, 16, 40, 16, 17, 35, 40, 36, 39, 36, 37, 34, 16, 35, 15, 16, 34, 38, 39, 37, 1, 39, 38, 2, 39, 1, 39, 2, 41, 42, 39, 41, 35, 36, 39, 42, 35, 39, 26, 35, 42, 34, 35, 26, 33, 15, 34, 33, 34, 26, 20, 41, 2, 42, 41, 20, 26, 42, 20, 2, 3, 21, 20, 2, 21, 14, 15, 33, 5, 20, 21, 5, 21, 4, 25, 33, 26, 25, 26, 20, 32, 14, 33, 32, 33, 25, 13, 14, 32, 22, 20, 5, 25, 20, 22, 22, 5, 6, 30, 13, 32, 12, 13, 30, 31, 32, 25, 30, 32, 31, 11, 12, 30, 27, 30, 31, 11, 30, 27, 28, 29, 11, 27, 28, 11, 23, 27, 31, 22, 23, 31, 22, 31, 25, 10, 11, 29, 10, 29, 28, 24, 10, 28, 7, 23, 22, 7, 22, 6, 28, 23, 24, 23, 28, 27, 23, 8, 24, 24, 8, 9, 10, 24, 9, 23, 7, 8], "vertices": [-53.46, -75.64, 12.95, -17.88, 38.03, 3.94, -15.53, 44.64, -21.87, 73.64, 133.93, 37.32, 192.2, 53.35, 348.7, -40.67, 405, -94.93, 406.5, -101.32, 358.15, -156.1, 290.17, -161.59, 273.48, -168.97, 215.73, -167.69, 173.29, -188.32, 108.21, -177.31, 3.74, -135.67, -1.14, -102.77, -9.79, -98.54, -51.59, -79.31, 124.12, -15.86, -12.77, 50.78, 174.82, 7.86, 321.72, -75.12, 367.09, -126.42, 163.49, -39.45, 114.64, -47.96, 301.76, -95.91, 330.75, -132.37, 326.4, -146.25, 248.83, -108.63, 256.39, -78.16, 196.94, -115.1, 139.05, -95.94, 86.94, -101.46, 54.63, -91.02, 49, -75.26, 10.76, -57.81, 9.99, -29.65, 56.96, -64.47, 28.18, -94.11, 85.46, -28.68, 97.73, -29.69], "hull": 20, "edges": [0, 38, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 40, 20, 22, 22, 24, 0, 2, 2, 4, 84, 70, 70, 32, 46, 44], "width": 335, "height": 442}}, "gebojia": {"images/gebojia": {"x": 121.96, "y": 41.22, "rotation": 14.37, "width": 272, "height": 114}}, "guan": {"images/guan": {"x": 442.32, "y": -5.03, "rotation": 150.72, "width": 464, "height": 372}}, "jian1": {"images/jian1": {"type": "mesh", "uvs": [0.5219, 0.00071, 0.60285, 0.03693, 0.745, 0.12956, 0.82212, 0.19426, 0.85886, 0.24386, 0.86884, 0.25467, 0.9194, 0.23961, 0.99683, 0.34647, 0.99883, 0.43461, 0.96793, 0.53937, 0.9225, 0.59547, 0.91186, 0.58784, 0.91344, 0.64203, 0.90684, 0.71839, 0.90099, 0.72683, 0.86336, 0.72336, 0.82867, 0.77419, 0.82816, 0.83046, 0.86798, 0.85788, 0.82741, 0.98796, 0.66878, 0.99836, 0.63884, 0.99813, 0.6118, 0.9367, 0.57129, 0.86897, 0.542, 0.83376, 0.46258, 0.77213, 0.4115, 0.74781, 0.40229, 0.64214, 0.36057, 0.69864, 0.18965, 0.56746, 0.01248, 0.34863, 0, 0.28783, 0.05396, 0.23767, 0.1148, 0.21597, 0.11653, 0.18354, 0.12379, 0.18097, 0.19533, 0.15876, 0.23402, 0.161, 0.23841, 0.16708, 0.32571, 0.14615, 0.34288, 0.13862, 0.3679, 0.1181, 0.47211, 0.17979, 0.50829, 0.14832, 0.4345, 0.06845, 0.45058, 0.05019, 0.48142, 0.01517, 0.75372, 0.38149, 0.90477, 0.56932, 0.75489, 0.34275, 0.77542, 0.36641, 0.91124, 0.55357, 0.52731, 0.12294, 0.41791, 0.59655, 0.60121, 0.66948, 0.35783, 0.65159, 0.39958, 0.58967, 0.19286, 0.51398, 0.05809, 0.36056, 0.73719, 0.41279], "triangles": [52, 0, 1, 46, 0, 52, 45, 46, 52, 43, 45, 52, 44, 45, 43, 49, 2, 3, 49, 3, 4, 58, 30, 31, 58, 32, 33, 58, 31, 32, 50, 49, 4, 2, 52, 1, 47, 49, 50, 2, 49, 52, 49, 43, 52, 47, 43, 49, 59, 43, 47, 42, 43, 59, 36, 33, 35, 36, 38, 33, 38, 36, 37, 5, 7, 8, 7, 5, 6, 8, 51, 5, 9, 51, 8, 35, 33, 34, 57, 33, 38, 57, 58, 33, 29, 58, 57, 30, 58, 29, 5, 50, 4, 5, 51, 50, 48, 50, 51, 47, 50, 48, 10, 11, 51, 48, 51, 11, 42, 40, 41, 53, 56, 42, 42, 39, 40, 56, 39, 42, 9, 10, 51, 59, 53, 42, 27, 56, 53, 39, 57, 38, 55, 56, 27, 39, 56, 57, 55, 57, 56, 29, 57, 55, 54, 53, 59, 59, 47, 48, 28, 55, 27, 29, 55, 28, 48, 12, 15, 12, 48, 11, 15, 59, 48, 13, 15, 12, 54, 59, 15, 14, 15, 13, 27, 25, 26, 53, 25, 27, 53, 54, 25, 16, 54, 15, 54, 22, 23, 24, 25, 54, 23, 24, 54, 22, 54, 16, 17, 22, 16, 22, 20, 21, 19, 17, 18, 22, 17, 20, 19, 20, 17], "vertices": [323.48, -84.85, 274.01, -97.89, 180.09, -110.01, 125.64, -111.2, 95.15, -104.79, 87.51, -104.03, 65.75, -126.2, 0.92, -111.81, -21.45, -79.68, -31.31, -30.59, -22.08, 5.16, -14.88, 5.81, -28.81, 25.45, -44, 56.02, -43.11, 61.07, -23.35, 72.11, -18.23, 102.38, -31.62, 123.48, -58.28, 120.63, -69.42, 182.31, 7.8, 238.14, 22.9, 247.87, 51.39, 233.87, 88.17, 221.95, 111.43, 218.45, 166.29, 221.54, 197.86, 229.22, 228.11, 192.93, 235.38, 227.62, 353.1, 234.82, 495.21, 211.45, 516.22, 192.92, 501.26, 156.59, 475.94, 128.59, 482.93, 115.95, 479.9, 112.62, 449.32, 80.92, 429.33, 69.08, 425.65, 69.91, 386.84, 33.52, 380.03, 25.09, 372.43, 9.27, 305.09, -1.92, 294.54, -25.48, 350.99, -31.02, 347.33, -43.08, 340.32, -66.21, 114.64, -19.15, -6.82, 1.24, 123.44, -33.94, 107.38, -31.87, -6.26, -6.74, 291.13, -41.15, 231.31, 170.86, 121.49, 137.94, 248.17, 211.02, 242.19, 174.3, 364.45, 213.87, 469.39, 200.95, 115.36, -2.09], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 52, 54, 86, 94, 18, 20, 20, 22, 88, 90, 90, 92, 56, 58, 118, 84, 94, 96], "width": 600, "height": 444}}, "jian2": {"images/jian2": {"x": 208.07, "y": 7.97, "rotation": 142.4, "width": 297, "height": 286}}, "jian3": {"images/jian3": {"x": 221, "y": 149.5, "rotation": -155.17, "width": 624, "height": 485}}, "jian4": {"images/jian4": {"type": "mesh", "uvs": [0.39759, 0, 0.42343, 0, 0.44811, 0.00704, 0.51055, 0.07372, 0.61796, 0.07761, 0.79547, 0.11156, 0.95035, 0.25597, 0.99771, 0.36831, 0.99778, 0.5059, 0.91265, 0.72238, 0.91238, 0.76489, 0.951, 0.81805, 0.94279, 0.99659, 0.91505, 0.99948, 0.84433, 0.96225, 0.84233, 0.93443, 0.73603, 0.95671, 0.70321, 0.95171, 0.55897, 0.99883, 0, 0.5307, 0, 0.52459, 0.07567, 0.25805, 0.10853, 0.21596, 0.18895, 0.11293, 0.2832, 0.02088, 0.61505, 0.21856, 0.59737, 0.4508, 0.56555, 0.58491, 0.71994, 0.85858, 0.33808, 0.17495, 0.24973, 0.43674, 0.22373, 0.57144], "triangles": [29, 24, 0, 23, 24, 29, 25, 3, 4, 25, 4, 5, 29, 22, 23, 30, 22, 29, 21, 22, 30, 0, 1, 2, 3, 29, 0, 3, 0, 2, 29, 3, 25, 26, 29, 25, 30, 29, 26, 20, 21, 30, 31, 19, 20, 30, 31, 20, 27, 30, 26, 31, 30, 27, 25, 5, 6, 8, 26, 7, 6, 26, 25, 6, 7, 26, 9, 26, 8, 27, 26, 9, 9, 28, 27, 10, 28, 9, 15, 28, 10, 15, 10, 11, 28, 18, 27, 16, 17, 28, 15, 16, 28, 15, 13, 14, 11, 12, 15, 18, 31, 27, 17, 18, 28, 19, 31, 18, 12, 13, 15], "vertices": [-26.55, 203.5, -17.88, 210.03, -7.68, 213.72, 31.49, 205.31, 68.58, 231.04, 137.41, 263.58, 228.81, 250.35, 275.39, 221.58, 312.99, 171.7, 343.56, 71.68, 355.08, 56.2, 382.56, 46.68, 428.57, -20.14, 420.05, -28.2, 386.16, -32.57, 377.89, -22.98, 348.31, -57.93, 335.93, -64.4, 300.42, -117.94, -14.98, -89.42, -16.65, -87.2, -64.06, 28.58, -64.54, 52.15, -65.7, 109.83, -59.22, 167.03, 106.11, 179.19, 163.61, 90.5, 189.56, 33.83, 316.11, -26.4, 1.28, 125.02, 43.14, 7.76, 71.21, -47.66], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 46, 48, 42, 44, 44, 46, 58, 60, 60, 62], "width": 420, "height": 454}}, "jian5": {"images/jian5": {"x": 103.27, "y": 23.06, "rotation": -40.54, "width": 281, "height": 222}}, "jiao1": {"images/jiao1": {"x": -31.95, "y": -5.52, "width": 310, "height": 279}}, "jiao2": {"images/jiao2": {"x": 17.59, "y": 24.24, "width": 565, "height": 333}}, "shouxin": {"images/shouxin": {"x": 78.31, "y": 9.14, "rotation": 7.07, "width": 118, "height": 175}}, "tou": {"images/tou": {"x": 130.05, "y": -6.75, "rotation": -90, "width": 334, "height": 295}}, "wuqi": {"images/wuqi": {"x": -27.27, "y": 9.92, "rotation": 90, "width": 553, "height": 2579}}, "wuqi2": {"images/wuqi": {"x": -763.6, "y": 3.31, "rotation": 90, "width": 553, "height": 2579}}, "wuqi3": {"images/wuqi": {"x": -763.6, "y": 3.31, "rotation": 90, "width": 553, "height": 2579}}, "xiaotui1": {"images/xiaotui1": {"type": "mesh", "uvs": [0.66239, 0, 0.6999, 0, 0.80815, 0.04627, 0.8307, 0.13325, 0.84435, 0.1557, 0.90843, 0.15443, 0.94926, 0.1731, 0.90884, 0.28115, 0.93384, 0.28855, 0.9847, 0.33841, 0.99478, 0.41078, 1, 0.44437, 1, 0.46256, 0.8733, 0.51973, 0.84495, 0.53394, 0.8451, 0.65392, 0.84622, 0.65804, 0.90417, 0.74959, 0.9042, 0.84057, 0.85604, 0.8579, 0.74748, 0.87468, 0.70573, 0.87159, 0.63258, 0.99808, 0.52635, 0.99796, 0.50392, 0.99589, 0.47739, 0.93658, 0.43159, 0.89959, 0.03314, 0.87551, 0.01686, 0.85094, 0.07296, 0.78726, 0.03359, 0.77118, 0, 0.73574, 0, 0.73323, 0.10508, 0.64698, 0.16984, 0.61551, 0.03475, 0.53571, 0.08666, 0.48269, 0.13134, 0.48692, 0.23031, 0.42116, 0.24117, 0.40387, 0.31473, 0.29044, 0.4042, 0.23135, 0.40626, 0.22813, 0.50232, 0.12229, 0.53476, 0.10529, 0.57788, 0.02279, 0.11171, 0.80161, 0.16483, 0.79643, 0.18057, 0.784, 0.25631, 0.74414, 0.53469, 0.78607, 0.52978, 0.76071, 0.12647, 0.7022, 0.50644, 0.83276], "triangles": [21, 22, 25, 25, 22, 23, 23, 24, 25, 25, 53, 21, 25, 26, 53, 26, 27, 47, 27, 46, 47, 47, 48, 26, 26, 48, 53, 27, 28, 46, 21, 53, 50, 28, 29, 46, 48, 49, 53, 50, 49, 51, 50, 53, 49, 47, 46, 48, 48, 46, 29, 48, 52, 49, 19, 20, 17, 21, 17, 20, 18, 19, 17, 51, 17, 21, 48, 29, 52, 21, 50, 51, 29, 30, 52, 30, 31, 52, 51, 16, 17, 16, 51, 15, 49, 52, 51, 52, 34, 51, 14, 34, 38, 14, 15, 51, 14, 51, 34, 14, 38, 39, 31, 32, 52, 32, 33, 52, 39, 40, 14, 52, 33, 34, 14, 40, 13, 34, 35, 37, 35, 36, 37, 40, 7, 13, 7, 41, 42, 3, 4, 44, 4, 42, 43, 4, 43, 44, 34, 37, 38, 41, 7, 40, 12, 13, 11, 10, 7, 9, 9, 7, 8, 7, 10, 13, 4, 7, 42, 44, 2, 3, 44, 45, 2, 13, 10, 11, 45, 0, 2, 0, 1, 2, 7, 4, 6, 6, 4, 5], "vertices": [1, 21, -171.19, -56.67, 1, 1, 21, -175.36, -38.77, 1, 1, 21, -145.45, 22.66, 1, 1, 21, -69.1, 51.81, 1, 1, 21, -50.26, 63.07, 1, 1, 21, -58.54, 93.38, 1, 1, 21, -46.15, 116.8, 1, 1, 21, 56.32, 120.35, 1, 1, 21, 60.24, 133.85, 1, 1, 21, 99.79, 168.65, 1, 1, 21, 164.29, 188.76, 1, 1, 21, 194.16, 198.35, 1, 1, 21, 210.66, 202.19, 1, 1, 21, 276.59, 153.81, 1, 1, 21, 292.63, 143.29, 1, 1, 21, 401.4, 168.72, 1, 1, 21, 405.01, 170.12, 1, 1, 21, 481.57, 217.12, 1, 1, 21, 564.05, 236.36, 1, 1, 21, 585.13, 217.05, 1, 1, 21, 612.42, 168.78, 1, 1, 21, 614.26, 148.21, 1, 1, 25, 150.01, -47.23, 1, 1, 25, 97.96, -47.12, 1, 1, 25, 86.97, -45.19, 1, 1, 25, 73.97, 10.03, 1, 1, 25, 51.53, 44.47, 1, 1, 25, -143.71, 66.88, 1, 1, 25, -151.69, 89.76, 1, 1, 21, 608.18, -171.58, 1, 1, 21, 597.98, -193.76, 1, 1, 21, 569.58, -217.28, 1, 1, 21, 567.3, -217.81, 1, 1, 21, 477.42, -185.9, 1, 1, 21, 441.68, -161.64, 1, 1, 21, 384.35, -242.97, 1, 1, 21, 330.5, -229.4, 1, 1, 21, 329.36, -207.19, 1, 1, 21, 258.73, -173.86, 1, 1, 21, 241.85, -172.33, 1, 1, 21, 130.82, -161.2, 1, 1, 21, 67.29, -130.99, 1, 1, 21, 64.15, -130.69, 1, 1, 21, -42.51, -107.21, 1, 1, 21, -61.53, -95.33, 1, 1, 21, -141.13, -92.18, 1, 1, 25, -105.21, 135.69, 1, 1, 25, -79.18, 140.51, 1, 1, 25, -71.47, 152.07, 1, 1, 25, -34.35, 189.19, 1, 1, 25, 102.05, 150.15, 1, 1, 21, 533.29, 40.81, 1, 1, 21, 525.1, -164.02, 1, 1, 25, 88.21, 106.68, 1], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 42, 102, 96, 106, 52, 54, 104, 102], "width": 490, "height": 931}}, "xiaotui2": {"images/xiaotui2": {"type": "mesh", "uvs": [0.7697, 0, 0.80474, 0.01454, 0.76711, 0.14694, 0.76099, 0.16637, 0.76562, 0.261, 0.81522, 0.34393, 0.81186, 0.42322, 0.81082, 0.43125, 0.89437, 0.51109, 0.95399, 0.52178, 0.98286, 0.56173, 0.87528, 0.677, 0.92131, 0.6888, 1, 0.76414, 1, 0.76793, 0.95968, 0.81868, 0.87186, 0.84455, 0.84583, 0.85667, 0.73182, 0.91173, 0.54992, 0.99958, 0.26311, 0.95535, 0.08753, 0.90276, 0.02927, 0.74616, 0, 0.63186, 0, 0.58729, 0.10733, 0.40729, 0.1362, 0.30469, 0.12395, 0.29868, 0.10747, 0.22697, 0.15675, 0.17433, 0.33389, 0.1154, 0.51843, 0.14836, 0.55026, 0.12638, 0.62809, 0.03281, 0.75453, 0, 0.67621, 0.15663, 0.70967, 0.05935, 0.67302, 0.28857, 0.57102, 0.45406, 0.66027, 0.76267], "triangles": [36, 33, 34, 34, 1, 36, 1, 34, 0, 2, 36, 1, 35, 33, 36, 35, 36, 2, 32, 33, 35, 3, 35, 2, 35, 31, 32, 4, 37, 35, 4, 35, 3, 37, 31, 35, 26, 27, 28, 29, 26, 28, 37, 4, 5, 6, 37, 5, 6, 38, 37, 38, 31, 37, 7, 38, 6, 26, 29, 30, 38, 23, 24, 7, 8, 38, 10, 11, 8, 10, 8, 9, 38, 22, 23, 31, 26, 30, 38, 26, 31, 8, 39, 38, 11, 39, 8, 15, 12, 13, 14, 15, 13, 16, 39, 11, 16, 11, 12, 16, 12, 15, 17, 39, 16, 39, 21, 22, 25, 26, 38, 18, 39, 17, 39, 22, 38, 24, 25, 38, 19, 20, 39, 20, 21, 39, 18, 19, 39], "vertices": [-112.02, 135.49, -100.76, 151.21, -13.03, 125.56, -0.18, 121.52, 63.83, 118.1, 121.82, 136.66, 175.15, 130.38, 180.53, 129.41, 237.84, 164.22, 247.53, 191.79, 275.67, 203.09, 348.94, 145.36, 358.8, 166.44, 412.89, 199.21, 415.44, 198.99, 447.99, 176.9, 461.79, 133.82, 468.89, 120.78, 501.28, 63.58, 552.97, -27.7, 511.22, -160.79, 468.46, -240.76, 360.43, -259.05, 282.13, -266.13, 252.07, -263.5, 135.14, -202.05, 67.14, -182.31, 62.58, -187.76, 13.54, -191.31, -19.92, -164.87, -52.3, -77.56, -22.41, 7.81, -35.91, 24.17, -95.78, 66.54, -112.65, 128.31, -10.28, 81.97, -74.49, 103.57, 78.57, 72.65, 185.94, 14.59, 397.78, 38.54], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 34, 36, 36, 38], "width": 475, "height": 677}}, "xiaotui22": {"images/xiaotui22": {"x": 454.57, "y": 157.41, "rotation": 84.98, "width": 48, "height": 39}}, "xiong": {"images/xiong": {"type": "mesh", "uvs": [0.26097, 0, 0.42072, 0.03017, 0.61789, 0.09567, 0.69722, 0.05233, 0.82123, 0.05597, 0.87412, 0.18064, 0.9969, 0.31875, 1, 0.42907, 0.98157, 0.56198, 0.87844, 0.82582, 0.8226, 0.87816, 0.79257, 0.62764, 0.72788, 0.61852, 0.56451, 0.80317, 0.40883, 0.83747, 0.44389, 1, 0.40114, 1, 0.1908, 0.87498, 0.17079, 0.81879, 0.05736, 0.93085, 0, 0.85596, 0, 0.70763, 0.04482, 0.29903, 0.1899, 0, 0.89931, 0.50145, 0.71517, 0.48406, 0.60229, 0.47405, 0.45834, 0.4507, 0.37737, 0.42068, 0.32339, 0.384, 0.90442, 0.36789, 0.86808, 0.3648, 0.86378, 0.23933, 0.69797, 0.56474, 0.7274, 0.36467, 0.73344, 0.25612, 0.7737, 0.21324], "triangles": [36, 3, 4, 36, 4, 5, 32, 36, 5, 35, 3, 36, 2, 3, 35, 32, 5, 6, 35, 26, 2, 30, 31, 32, 6, 30, 32, 29, 0, 1, 28, 29, 1, 27, 28, 1, 2, 27, 1, 30, 6, 7, 2, 26, 27, 35, 34, 26, 25, 26, 34, 24, 31, 30, 24, 30, 7, 8, 24, 7, 33, 26, 25, 34, 31, 25, 11, 12, 25, 33, 25, 12, 31, 35, 36, 31, 36, 32, 31, 34, 35, 25, 24, 11, 24, 25, 31, 13, 27, 26, 13, 26, 33, 13, 33, 12, 22, 29, 21, 18, 21, 29, 23, 0, 29, 29, 22, 23, 8, 9, 11, 8, 11, 24, 14, 28, 27, 14, 27, 13, 20, 21, 18, 18, 29, 28, 18, 28, 14, 17, 18, 14, 10, 11, 9, 19, 20, 18, 16, 17, 14, 16, 14, 15], "vertices": [283.68, 354.9, 272.2, 245.66, 243.87, 110.37, 267.37, 57.1, 268.37, -27.36, 207.14, -65.44, 140.77, -151.32, 85.6, -155.27, 18.63, -144.94, -115.82, -79.15, -143.29, -42.02, -18.54, -17.4, -15.43, 26.78, -111.6, 134.89, -132.3, 240.28, -212.89, 213.71, -213.86, 242.8, -156.03, 388.05, -128.35, 402.61, -187.04, 477.94, -150.84, 518.23, -76.57, 520.71, 129.04, 497.02, 282.07, 403.27, 47.07, -87.94, 51.61, 37.68, 54.06, 114.67, 62.48, 213.04, 75.67, 268.65, 92.82, 306, 114.07, -89.19, 114.79, -64.41, 177.52, -59.39, 10.82, 48.04, 111.67, 31.35, 166.16, 29.05, 188.54, 2.36], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 8, 10, 10, 12, 12, 14, 14, 16, 4, 6, 6, 8], "width": 681, "height": 501}}, "xiong2": {"images/xiong2": {"type": "mesh", "uvs": [0.88657, 0.06853, 0.92296, 0.26141, 0.9252, 0.34157, 0.94264, 0.37873, 0.95953, 0.4798, 0.92662, 0.60738, 0.88555, 0.70453, 0.90234, 0.79698, 0.92611, 0.92783, 0.92471, 0.97822, 0.35772, 0.99655, 0.29733, 0.99877, 0.23975, 0.90309, 0.2147, 0.81481, 0.1526, 0.71308, 0.14102, 0.69965, 0, 0.5413, 0, 0.5326, 0.05505, 0.44113, 0.11687, 0.41767, 0.16832, 0.42996, 0.14537, 0.29662, 0.2084, 0.24304, 0.37046, 0.22684, 0.38666, 0.22214, 0.46639, 0.1883, 0.49629, 0.1696, 0.62283, 0.07825, 0.71965, 0.01135, 0.85065, 0.01082, 0.83548, 0.67389, 0.63041, 0.6832, 0.48357, 0.67234, 0.39074, 0.63199, 0.23196, 0.46688, 0.6529, 0.80518, 0.49708, 0.84641, 0.38375, 0.86812], "triangles": [12, 13, 37, 8, 35, 7, 37, 11, 12, 10, 37, 36, 9, 10, 36, 9, 35, 8, 9, 36, 35, 10, 11, 37, 36, 31, 35, 37, 13, 33, 37, 32, 36, 7, 35, 30, 20, 21, 22, 34, 22, 23, 20, 22, 34, 20, 16, 17, 32, 3, 4, 3, 32, 2, 33, 24, 25, 34, 23, 24, 33, 34, 24, 28, 29, 0, 27, 28, 0, 27, 0, 1, 26, 27, 1, 25, 2, 32, 26, 2, 25, 1, 2, 26, 32, 4, 31, 33, 25, 32, 5, 30, 4, 30, 31, 4, 20, 18, 19, 18, 20, 17, 15, 34, 33, 16, 20, 34, 15, 16, 34, 6, 30, 5, 14, 15, 33, 35, 31, 30, 13, 14, 33, 36, 32, 31, 37, 33, 32, 7, 30, 6], "vertices": [1, 4, 182.71, -39.26, 1, 1, 4, 99.33, -65.14, 1, 1, 4, 63.99, -70.66, 1, 1, 4, 48.48, -79.89, 1, 1, 4, 4.67, -92.57, 1, 1, 4, -53.49, -86.52, 1, 1, 4, -98.63, -75.41, 1, 1, 27, 129.32, 50.35, 1, 1, 27, 188.28, 55.25, 1, 1, 27, 210.63, 52.83, 1, 1, 27, 199.64, -179.52, 1, 1, 27, 198.59, -204.27, 1, 1, 27, 154.12, -224.29, 1, 1, 27, 114.03, -231.28, 1, 1, 4, -141.15, 222.1, 1, 1, 4, -135.82, 227.58, 1, 1, 4, -73.24, 294.02, 1, 1, 4, -69.4, 294.52, 1, 1, 4, -26.03, 277.4, 1, 1, 4, -12.39, 253.62, 1, 1, 4, -15.11, 231.99, 1, 1, 4, 42.66, 248.99, 1, 1, 4, 69.69, 226.44, 1, 1, 4, 85.41, 161.48, 1, 1, 4, 88.35, 155.16, 1, 1, 4, 107.53, 124.7, 1, 1, 4, 117.38, 113.61, 1, 1, 4, 164.47, 67.41, 1, 1, 4, 199.18, 31.9, 1, 1, 4, 206.34, -21.33, 1, 1, 4, -87.73, -53.29, 1, 1, 4, -102.68, 29.55, 1, 1, 4, -105.64, 89.88, 1, 1, 4, -92.7, 129.94, 1, 1, 4, -28.07, 203.99, 1, 1, 27, 124.55, -51.88, 1, 1, 27, 137.62, -117.06, 1, 1, 27, 143.44, -164.16, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 10, 12, 12, 14, 14, 16], "width": 410, "height": 446}}}}], "animations": {"animation": {"slots": {"bei": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "dang": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "datui1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "datui2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "gebo": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "gebo1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "gebo1bi": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "gebojia": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "guan": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jian1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jian2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jian3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jian4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jian5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jiao1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "jiao2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "shouxin": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "tou": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "wuqi": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "wuqi2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "color": "ffffff48", "curve": 0.25, "c3": 0.75}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "color": "ffffff64", "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "color": "ffffff00"}]}, "wuqi3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff48", "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "color": "ffffff00"}]}, "xiaotui1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "xiaotui2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "xiaotui22": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "xiong": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "xiong2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": -10.15, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -12.17, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": -10.15, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiong": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 2.65, "curve": 0.25, "c3": 0.411}, {"time": 2, "angle": -3.65, "curve": 0.496, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 5.73, "y": 0.55, "curve": 0.25, "c3": 0.411}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.26, "y": -0.41, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "gebo": {"rotate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "angle": 56.02, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8, "curve": "stepped"}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "x": -58.12, "y": 12.87, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8, "y": -29.03, "curve": 0.328, "c3": 0.662, "c4": 0.34}, {"time": 1.8333, "y": 21.77, "curve": 0.329, "c2": 0.22, "c3": 0.662, "c4": 0.56}, {"time": 1.8667, "curve": "stepped"}, {"time": 4}]}, "gebo1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.93, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": -11.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.96, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jian6": {"rotate": [{"curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 0.4667, "angle": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.22, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "angle": 29.59, "curve": 0.25, "c3": 0.752, "c4": 0.99}, {"time": 1.8, "angle": -4.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jian3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.51, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "gebojia": {"rotate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "angle": -24.43, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8, "curve": "stepped"}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "x": -30.92, "y": 19.65, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8, "curve": "stepped"}, {"time": 4}]}, "datui1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.71, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": -0.89, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.1, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": 22.02, "y": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 4}]}, "xiaotui1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.22, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.47, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -3.37, "y": 0.14, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": 10.9, "y": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.37, "y": 0.14, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.37, "y": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "datui2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.77, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.57, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.77, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": -31.21, "y": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 4}]}, "xiaotui2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.58, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.28, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": 11.04, "y": 2.66, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 4}]}, "dang": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.84, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.33, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -0.41, "y": 4.23, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": 18.39, "y": 19.89, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.41, "y": 4.23, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -1.17, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "gebo1bi": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.02, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.96, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "gebo1bi2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.35, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.96, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jian5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.34, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.34, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiong2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.52, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "angle": 11.1, "curve": 0.25, "c3": 0.411}, {"time": 2, "angle": -3.99, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "x": -2.86, "y": 33.86, "curve": 0.25, "c3": 0.411}, {"time": 2, "x": 17.23, "y": 0.11, "curve": 0.496, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 4}]}, "shouxin": {"rotate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "angle": -19.01, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8, "curve": "stepped"}, {"time": 4}]}, "wuqi": {"rotate": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.126, "c4": 0.93}, {"time": 1.7333, "angle": 2.41, "curve": 0.25, "c3": 0.77, "c4": 0.71}, {"time": 1.8}]}, "z": {"scale": [{"x": 0.52, "y": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.096, "y": 1.096, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.8}, {"time": 1.8333, "x": 0.95, "y": 0.95}, {"time": 1.8667, "x": 1.05, "y": 1.05}, {"time": 1.9333, "curve": "stepped"}, {"time": 4}]}, "wuqi3": {"scale": [{"x": 1.518, "y": 1.518, "curve": "stepped"}, {"time": 0.9, "x": 1.518, "y": 1.518, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": "stepped"}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.726, "y": 1.726}]}, "wuqi4": {"scale": [{"x": 1.518, "y": 1.518, "curve": "stepped"}, {"time": 1.5333, "x": 1.518, "y": 1.518, "curve": 0.25, "c3": 0.75}, {"time": 1.7333}]}}, "deform": {"default": {"dang": {"images/dang": [{"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "vertices": [0.80133, 9.82844, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 0.32916, 3.985, 0.32916, 3.985, -0.90247, -10.92523, -0.90247, -10.92523, -0.90247, -10.92523, -0.90247, -10.92523, -0.90247, -10.92523, -2.51903, -30.49692, -2.99792, -36.29022, -2.99792, -36.29022, -2.62536, -31.78354, -0.22797, -2.75548, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 0.47198, 5.84344, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60357, 7.43385, 0.60357, 7.43385, 0.60357, 7.43385, 0.80133, 9.82844, -0.07856, -0.95214, 0.4154, 5.02858, 0.32916, 3.985, -0.07856, -0.95214, -0.22797, -2.75548, 1.14648, 14.0132, -0.22797, -2.75548, 1.2959, 15.81654, 1.59765, 19.46296, 1.59765, 19.46296, 1.39416, 17.00404, 1.98645, 24.17024, 1.98645, 24.17024, 1.68469, 20.52382, 1.53527, 18.72047, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.14648, 14.0132, 1.53527, 18.72047, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, -0.07856, -0.95214, 0.79712, 9.77277, 0.47198, 5.84344, 0.47198, 5.84344, 2.09894, 16.93127, 1.09808, 18.7818, 1.93915, 19.75873, 2.82483, 21.37, 2.95374, 22.72089, 1.41943, 17.93118, 0.54651, 18.67276, 1.11633, 13.6337, -0.6936, 5.85358, -3.51147, 8.43951], "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "datui2": {"images/datui2": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "offset": 50, "vertices": [1.43781, 17.93898, 2.65009, 33.06383, 2.65009, 33.06383, 2.65009, 33.06383, 1.21229, 15.12485, 0.56104, 6.99966, 0.56104, 6.99966], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "offset": 8, "vertices": [-0.54224, -6.77019, -0.54224, -6.77019, 0, 0, -0.54224, -6.77019, 0, 0, 0, 0, 0, 0, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.31103, 6.67599, 0, 0, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599, -2.31103, 6.67599], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "gebo1bi": {"images/gebo1bi": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "vertices": [5.75657, -3.96602, -1.7995, -6.64128, -1.7995, -6.64128, -1.14502, -10.18516, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.68187, 11.66042, -3.84512, 1.12233, -3.94725, 4.56612, -3.29493, 3.52648, -4.20229, 7.68358, 0, 0, 0, 0, 5.75657, -3.96602, 5.75657, -3.96602, 5.75657, -3.96602, -0.98133, -11.07115, -0.98133, -11.07115, -0.98133, -11.07115, -0.98133, -11.07115, -0.61649, -5.20866, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.35811, 0.08687, -3.52067, 3.04843, 3.21759, -13.96031, -1.7995, -6.64128, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, 3.21759, -13.96031, -1.7995, -6.64128, -1.7995, -6.64128], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [0.73948, 3.35301, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.64223, -1.04668, -0.68187, 11.66042, -3.84512, 1.12233, -3.94725, 4.56612, -3.29493, 3.52648, -4.20229, 7.68358, 0, 0, 0, 0, 0.73948, 3.35301, 0.73948, 3.35301, 0.73948, 3.35301, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.27097, -1.66478, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.35811, 0.08687, -3.52067, 3.04843, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128, -1.7995, -6.64128], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jian1": {"images/jian1": [{"curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "offset": 12, "vertices": [1.62319, 6.0912, 1.62319, 6.0912, 0, 0, -2.13816, -6.1275, -2.06463, -7.57422, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, 0, 0, 0.49715, -3.86557, 0.49715, -3.86557, 0.49715, -3.86557, 0.49715, -3.86557, 0.49715, -3.86557, 0.49715, -3.86557, 0.49715, -3.86557, -1.8128, -6.85226, 0, 0, 0, 0, 0, 0, -1.06906, -3.06378, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.06906, -3.06378, -1.06906, -3.06378, -2.13816, -6.1275, -1.06906, -3.06378, -1.06906, -3.06378, -1.8128, -6.85226, -2.13816, -6.1275, -2.13816, -6.1275, -2.13816, -6.1275, -2.13816, -6.1275, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.8128, -6.85226, -1.2765, -9.56519], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jian4": {"images/jian4": [{"curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [8.61812, 6.49104, 8.61812, 6.49104, 8.61812, 6.49104, 8.61812, 6.49104, 8.61812, 6.49104, 0, 0, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, -2.43954, 3.62321, 0, 0, 6.17857, 10.11426, 6.17857, 10.11426, 6.17857, 10.11426, -2.43954, 3.62321, 6.17857, 10.11426, 6.17857, 10.11426, 6.17857, 10.11426], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiaotui2": {"images/xiaotui2": [{"curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [0.81909, 9.33997, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.81909, 9.33997, 0.27515, 3.13557], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiong": {"images/xiong": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "offset": 4, "vertices": [1.99744, -25.33188, 1.99744, -25.33188, 1.99744, -25.33188, 0, 0, -0.26245, 7.87247, -0.26245, 7.87247, -0.17798, 5.34033, 0, 0, 0, 0, 2.12866, -29.26825, 2.12866, -29.26825, 1.99744, -25.33188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.12866, -29.26825, 2.12866, -29.26825, 1.99744, -25.33188, 1.92224, -23.07578, 1.92224, -23.07578, 1.3208, -5.02588, 2.12866, -29.26825, 2.12866, -29.26825, 2.12866, -29.26825, 2.12866, -29.26825, 2.12866, -29.26825, 2.12866, -29.26825, 2.12866, -29.26825], "curve": 0.25, "c3": 0.411}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "offset": 48, "vertices": [4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 2.84763, 1.1549, 1.04621, 0.03483, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889, 4.56866, 3.68889], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiong2": {"images/xiong2": [{"curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.35}, {"time": 1.6667, "vertices": [1.38166, -10.62785, 0.29029, -2.2328, 0.29029, -2.2328, -1.04555, 8.04318, -1.55848, 11.98859, -1.0695, 8.22712, -1.0695, 8.22712, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.86873, -6.68244, 0.86873, -6.68244, 0.86873, -6.68244, 0.86873, -6.68244, 1.38166, -10.62785, 1.38166, -10.62785, 1.38166, -10.62785, -0.13313, 1.02458, 0.71373, -5.48992, 0.71373, -5.48992, 0.71373, -5.48992, 0, 0, -0.55704, 5.50815, -0.55704, 5.50815], "curve": 0.25, "c3": 0.411}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [-0.31383, 2.41367, -0.06549, 0.50114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21018, 1.61519, 0, 0, 0, 0, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.74315, 5.71506, -0.21018, 1.61519, -0.50642, -5.74083, -0.50642, -5.74083, -0.28473, -3.2267], "curve": 0.25, "c3": 0.75}, {"time": 4}]}}}}}}