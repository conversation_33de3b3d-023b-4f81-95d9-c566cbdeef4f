{"skeleton": {"hash": "V7+J5YKUE8X9bPmbuAFuhqCkYjQ", "spine": "3.8.66", "x": -154.5, "y": -208.5, "width": 309, "height": 410}, "bones": [{"name": "root"}, {"name": "crocodile3", "parent": "root", "length": 16.56, "rotation": 150.79, "x": 50.53, "y": -14.47, "scaleX": 0.85, "scaleY": 0.85}, {"name": "bone", "parent": "crocodile3", "length": 39.36, "rotation": 11.14, "x": 16.91, "y": 1.19}, {"name": "crocodile2", "parent": "crocodile3", "length": 14.5, "rotation": -89.1, "x": 103.85, "y": 117.75}, {"name": "bone2", "parent": "bone", "length": 52.6, "rotation": 12.09, "x": 39.36, "y": 0.04}, {"name": "bone7", "parent": "crocodile2", "length": 21.32, "rotation": -0.46, "x": 18.46, "y": -0.1}, {"name": "crocodile6", "parent": "bone", "length": 61.87, "rotation": 17.89, "x": 81.6, "y": -72.13}, {"name": "crocodile7", "parent": "bone", "length": 48.76, "rotation": 179.55, "x": 39.56, "y": -90.5}, {"name": "bone6", "parent": "crocodile7", "length": 47.91, "rotation": -26.38, "x": 52.95, "y": -1.95}, {"name": "bone8", "parent": "bone7", "length": 23.7, "rotation": -11.14, "x": 22.69, "y": -1.55}, {"name": "crocodile4", "parent": "bone2", "length": 101.77, "rotation": 26.38, "x": 49.82, "y": -51.48}, {"name": "crocodile5", "parent": "bone2", "length": 20.51, "rotation": -50.96, "x": 0.86, "y": -79.72}, {"name": "crocodile_ice1", "parent": "crocodile7", "length": 24.73, "rotation": 46.15, "x": 99.42, "y": -39.83}, {"name": "crocodile_ice2", "parent": "crocodile7", "length": 22.21, "rotation": 72.11, "x": 50.5, "y": 14.15}, {"name": "crocodile_ice3", "parent": "crocodile7", "length": 15.36, "rotation": 49.1, "x": 38.54, "y": -12.02}, {"name": "bone3", "parent": "crocodile5", "length": 46.4, "rotation": 35.62, "x": 20.51, "y": 0.1}, {"name": "bone4", "parent": "bone3", "length": 92.65, "rotation": -7.18, "x": 46.4, "y": 0.18}, {"name": "bone5", "parent": "bone3", "rotation": -158.69, "x": 17.84, "y": -21.54}], "slots": [{"name": "bj", "bone": "root", "attachment": "bj"}, {"name": "crocodile2", "bone": "crocodile2", "attachment": "crocodile2"}, {"name": "crocodile3", "bone": "crocodile7", "attachment": "crocodile3"}, {"name": "crocodile4", "bone": "crocodile5", "attachment": "crocodile4"}, {"name": "crocodile_eye1", "bone": "bone5"}, {"name": "crocodile_ice1", "bone": "crocodile_ice1", "color": "ffffff00", "attachment": "crocodile_ice1", "blend": "additive"}, {"name": "crocodile_ice2", "bone": "crocodile_ice2", "color": "ffffff00", "attachment": "crocodile_ice2", "blend": "additive"}, {"name": "crocodile_ice3", "bone": "crocodile_ice3", "color": "ffffff00", "attachment": "crocodile_ice3", "blend": "additive"}, {"name": "title", "bone": "root", "attachment": "title"}], "skins": [{"name": "default", "attachments": {"bj": {"bj": {"width": 309, "height": 403}}, "title": {"title": {"y": -160, "width": 299, "height": 97}}, "crocodile_ice1": {"crocodile_ice1": {"x": 22.42, "y": -2.87, "rotation": -27.63, "width": 73, "height": 70}}, "crocodile_ice2": {"crocodile_ice2": {"x": 22.96, "y": 6.85, "rotation": -53.59, "width": 86, "height": 82}}, "crocodile_ice3": {"crocodile_ice3": {"x": 5.15, "y": 1.9, "rotation": -30.58, "width": 39, "height": 26}}, "crocodile2": {"crocodile2": {"type": "mesh", "uvs": [0.66357, 0, 0.45097, 0.29451, 0.27887, 0.59181, 0.09158, 0.69992, 0, 0.83056, 0, 0.94317, 0.21813, 1, 0.76481, 1, 0.72431, 0.78551, 0.83567, 0.63235, 1, 0.52424, 1, 0.24947, 0.81543, 0], "triangles": [9, 1, 10, 0, 12, 11, 10, 0, 11, 10, 1, 0, 9, 8, 1, 8, 2, 1, 6, 5, 4, 6, 3, 2, 6, 2, 8, 6, 4, 3, 6, 8, 7], "vertices": [1, 9, 45.42, 30.93, 1, 2, 5, 35.17, 22.66, 0.496, 9, 7.57, 26.16, 0.504, 2, 3, 17.35, 21.62, 0.88797, 5, -1.27, 21.71, 0.11203, 1, 3, -1.64, 32.02, 1, 1, 3, -18.38, 33.09, 1, 1, 3, -29.19, 27.27, 1, 1, 3, -24.61, 5.7, 1, 1, 3, 0.54, -40.98, 1, 2, 3, 19.26, -26.43, 0.90582, 5, 1.01, -26.33, 0.09418, 2, 5, 20.84, -27.77, 0.504, 9, 3.25, -26.08, 0.496, 1, 9, 22.51, -30.76, 1, 1, 9, 45.49, -11.55, 1, 1, 9, 54.87, 19.63, 1], "hull": 13}}, "crocodile3": {"crocodile3": {"type": "mesh", "uvs": [0.36603, 0.18739, 0.30582, 0.19922, 0.24784, 0.21062, 0.20863, 0.21833, 0.03967, 0.31965, 0, 0.39369, 0, 0.52228, 0.04977, 0.59242, 0.12487, 0.6197, 0.18552, 0.60411, 0.26928, 0.66062, 0.35015, 0.75804, 0.44979, 0.82623, 0.56966, 0.85546, 0.67652, 0.8652, 0.74873, 0.81454, 0.77039, 0.82039, 0.72707, 0.88894, 0.70685, 0.96652, 0.75884, 1, 0.95668, 1, 0.97401, 0.96262, 0.96679, 0.89832, 0.9278, 0.82428, 0.95235, 0.72102, 0.92202, 0.56515, 1, 0.46773, 1, 0.41707, 0.95668, 0.3781, 0.88159, 0.35082, 0.9148, 0.12481, 0.87581, 0.02738, 0.82238, 0, 0.7675, 0, 0.66786, 0.13065, 0.54511, 0.10922, 0.48887, 0.14068, 0.42669, 0.17546, 0.16964, 0.30211, 0.23462, 0.28068, 0.23462, 0.34108, 0.29383, 0.32744, 0.36748, 0.34692, 0.43102, 0.35082, 0.46424, 0.37225, 0.47001, 0.28652, 0.52633, 0.26899, 0.57254, 0.31575, 0.27795, 0.5632, 0.43102, 0.53397, 0.53933, 0.46967, 0.59276, 0.38589, 0.74439, 0.30796, 0.81949, 0.44045, 0.84259, 0.62944], "triangles": [47, 45, 46, 47, 46, 35, 47, 44, 45, 37, 45, 43, 41, 39, 1, 38, 3, 39, 45, 37, 46, 37, 36, 46, 46, 36, 35, 1, 0, 42, 37, 43, 0, 1, 39, 2, 39, 3, 2, 34, 47, 35, 51, 47, 52, 53, 51, 52, 50, 53, 54, 54, 53, 25, 26, 25, 53, 26, 53, 28, 28, 27, 26, 32, 31, 30, 33, 30, 34, 52, 47, 34, 30, 33, 32, 34, 30, 52, 28, 53, 29, 53, 52, 29, 29, 52, 30, 53, 50, 51, 54, 25, 24, 20, 19, 22, 22, 19, 16, 20, 22, 21, 22, 16, 23, 18, 17, 19, 19, 17, 16, 15, 14, 50, 50, 14, 13, 23, 16, 24, 24, 16, 54, 15, 50, 54, 16, 15, 54, 11, 49, 12, 50, 13, 49, 13, 12, 49, 11, 10, 49, 49, 10, 48, 10, 9, 48, 48, 42, 49, 50, 44, 51, 44, 43, 45, 0, 43, 42, 49, 43, 44, 42, 43, 49, 8, 7, 9, 48, 9, 40, 9, 7, 6, 40, 9, 6, 38, 5, 4, 40, 5, 38, 48, 41, 42, 48, 40, 41, 40, 6, 5, 40, 39, 41, 40, 38, 39, 38, 4, 3, 44, 47, 51, 49, 44, 50, 41, 1, 42], "vertices": [1, 6, 30.31, -18.09, 1, 1, 6, 50.77, -15.04, 1, 1, 6, 70.48, -12.11, 1, 1, 6, 83.8, -10.12, 1, 1, 10, 104.03, -49.57, 1, 1, 10, 123.17, -36.78, 1, 1, 10, 134.47, -6.41, 1, 1, 10, 124.77, 16.06, 1, 1, 10, 103.24, 31.4, 1, 2, 10, 82.54, 34.91, 0.55847, 4, 108.27, 16.46, 0.44153, 3, 10, 60.81, 58.18, 0.248, 4, 78.46, 27.66, 0.75129, 2, 110.28, 43.54, 0.00071, 3, 4, 48.56, 49.22, 0.93093, 1, 80.72, 73.22, 0.0034, 2, 76.52, 58.34, 0.06567, 3, 4, 13.08, 62.79, 0.67355, 1, 42.76, 71.68, 0.08843, 2, 38.99, 64.17, 0.23802, 3, 4, -28.22, 65.88, 0.23782, 1, 3.59, 58.22, 0.53088, 2, -2.04, 58.53, 0.23129, 3, 4, -64.61, 64.54, 0.0316, 1, -29.32, 42.63, 0.93321, 2, -37.34, 49.59, 0.03519, 2, 4, -87.7, 49.29, 0.00139, 1, -44.51, 19.51, 0.99861, 2, 10, -84.84, 155.31, 0, 1, -51.66, 17.2, 1, 3, 10, -65.02, 166.37, 1e-05, 4, -82.32, 68.71, 2e-05, 1, -47.24, 39.46, 0.99997, 3, 10, -51.76, 182.29, 1e-05, 4, -77.52, 88.87, 2e-05, 1, -50.78, 59.88, 0.99997, 3, 10, -65.38, 196.36, 0, 4, -95.97, 95.42, 1e-05, 1, -70.32, 58.62, 0.99999, 1, 1, -129.03, 25.79, 1, 1, 1, -129.58, 14.69, 1, 1, 1, -119.53, 1.75, 1, 2, 10, -134.66, 174.89, 0.00022, 1, -98.85, -8.07, 0.99978, 3, 10, -151.56, 153.41, 0.00347, 1, -93.44, -34.85, 0.99642, 2, -115.23, -14.04, 0.00012, 2, 7, 133.12, -41.29, 0.5, 8, 89.31, 0.39, 0.5, 2, 7, 150.47, -9.6, 0.5, 8, 90.76, 36.49, 0.5, 2, 7, 146.41, 2.51, 0.5, 8, 81.75, 45.53, 0.5, 2, 7, 129.33, 7.14, 0.5, 8, 64.38, 42.09, 0.5, 2, 7, 102.94, 5.56, 0.5, 8, 41.44, 28.94, 0.5, 2, 7, 95.56, 63.15, 0.5, 8, 9.24, 77.25, 0.5, 2, 7, 75.2, 82.22, 0.5, 8, -17.48, 85.28, 0.5, 2, 7, 55.78, 83, 0.5, 8, -35.22, 77.35, 0.5, 2, 7, 38.08, 77.07, 0.5, 8, -48.44, 64.18, 0.5, 2, 7, 16.41, 35.1, 0.5, 8, -49.19, 16.94, 0.5, 1, 6, -30.51, -37.99, 1, 1, 6, -11.42, -30, 1, 1, 6, 9.7, -21.16, 1, 1, 10, 61.07, -38.31, 1, 2, 10, 38.48, -35.67, 0.448, 6, 74.91, 5.56, 0.552, 1, 10, 43.78, -21.4, 1, 1, 10, 23.72, -17.6, 1, 1, 10, 1.96, -4.27, 1, 2, 10, -17.95, 4.18, 0.92559, 1, 106.8, -29.77, 0.07441, 2, 10, -26.65, 13.18, 0.57132, 1, 94.31, -30.57, 0.42868, 1, 6, -5.12, 6.78, 1, 3, 7, -18.15, -13.24, 0.38, 8, -58.67, -41.72, 0.38, 6, -24.26, 2.29, 0.24, 3, 2, 39.22, -71.07, 0.192, 7, 0.49, -19.43, 0.404, 8, -39.22, -38.97, 0.404, 2, 10, 49.49, 36.2, 0.536, 4, 78.08, 2.93, 0.464, 3, 10, -1.86, 47.44, 0.504, 4, 27.09, -9.8, 0.44049, 2, 67.9, -3.86, 0.05551, 4, 10, -42.02, 45.09, 0.184, 4, -7.86, -29.74, 0.32723, 1, 60.04, -21.6, 0.04347, 2, 37.92, -30.69, 0.4453, 3, 10, -66.41, 31.63, 0.12, 4, -23.73, -52.63, 0.45056, 2, 27.2, -56.4, 0.42944, 3, 2, -15.72, -91.06, 0.12, 7, 55.28, 0.99, 0.44, 8, 0.78, 3.66, 0.44, 3, 2, -50.35, -67.24, 0.08, 7, 90.09, -22.57, 0.46, 8, 42.43, -1.96, 0.46, 3, 2, -72.59, -24.4, 0.128, 7, 112.66, -65.23, 0.436, 8, 81.62, -30.15, 0.436], "hull": 38}}, "crocodile4": {"crocodile4": {"type": "mesh", "uvs": [0, 0.38844, 0, 0.3262, 0, 0.21555, 0.06881, 0, 0.20442, 0, 0.33769, 0, 0.41668, 0.14566, 0.46395, 0.23284, 0.65333, 0.14639, 0.67672, 0.22246, 0.92222, 0.14639, 0.93624, 0.26396, 0.84272, 0.37461, 0.93624, 0.45068, 0.96196, 0.59244, 1, 0.69963, 1, 1, 0.95495, 1, 0.85207, 0.78608, 0.77725, 0.8172, 0.65801, 0.86906, 0.41485, 0.78953, 0.27924, 0.76187, 0.03842, 0.66851, 0, 0.52329, 0.0805, 0.30545, 0.15298, 0.31582, 0.23949, 0.36423, 0.33302, 0.40227, 0.42186, 0.42647, 0.48499, 0.51983, 0.65801, 0.69272, 0.73283, 0.73075, 0.84973, 0.68926, 0.90117, 0.57861, 0.5715, 0.34003, 0.47798, 0.31582, 0.65567, 0.41956, 0.78894, 0.506, 0.58495, 0.61971, 0.72348, 0.46354, 0.74686, 0.34694], "triangles": [20, 31, 32, 34, 38, 13, 34, 13, 14, 33, 38, 34, 33, 32, 38, 33, 15, 18, 32, 33, 18, 14, 33, 34, 15, 33, 14, 19, 32, 18, 20, 32, 19, 16, 17, 18, 16, 18, 15, 39, 30, 37, 9, 10, 11, 41, 9, 11, 35, 9, 41, 12, 41, 11, 37, 35, 41, 40, 37, 41, 40, 41, 12, 38, 40, 12, 38, 12, 13, 39, 37, 40, 40, 38, 31, 40, 31, 39, 32, 31, 38, 20, 21, 31, 21, 22, 30, 21, 39, 31, 7, 8, 9, 3, 25, 2, 4, 25, 3, 26, 25, 4, 5, 26, 4, 5, 27, 26, 35, 36, 7, 1, 2, 25, 9, 35, 7, 5, 6, 27, 28, 27, 6, 7, 28, 6, 0, 1, 25, 7, 29, 28, 36, 29, 7, 35, 29, 36, 30, 35, 37, 30, 29, 35, 26, 24, 0, 24, 27, 23, 25, 26, 0, 27, 24, 26, 28, 23, 27, 30, 22, 28, 30, 28, 29, 22, 23, 28, 39, 21, 30], "vertices": [1, 16, 116.35, 48.73, 1, 1, 16, 120.56, 40.97, 1, 1, 16, 128.06, 27.16, 1, 1, 16, 129.97, -6.63, 1, 1, 16, 104.95, -20.22, 1, 1, 16, 80.36, -33.58, 1, 1, 16, 55.91, -23.32, 1, 2, 16, 41.28, -17.18, 0.97849, 15, 85.2, -22.03, 0.02151, 2, 16, 12.19, -46.95, 0.50842, 15, 52.62, -47.93, 0.49158, 2, 16, 2.72, -39.81, 0.37035, 15, 44.12, -39.65, 0.62965, 1, 15, 0.02, -68.46, 1, 2, 15, -8.8, -53.98, 0.99731, 11, 44.82, -48.89, 0.00269, 3, 16, -38.23, -37.46, 0.00986, 15, 3.79, -32.2, 0.86302, 11, 42.36, -23.86, 0.12712, 2, 15, -18.44, -29.28, 0.3833, 11, 22.59, -34.44, 0.6167, 2, 15, -30.79, -12.49, 0.07285, 11, 2.77, -27.99, 0.92715, 2, 15, -43.76, -1.22, 0.00085, 11, -14.34, -26.39, 0.99915, 2, 15, -59.28, 38.51, 2e-05, 11, -50.1, -3.14, 0.99998, 2, 15, -50.46, 41.95, 2e-05, 11, -44.94, 4.79, 0.99998, 2, 15, -19.29, 21.51, 0.01177, 11, -7.7, 6.34, 0.98823, 3, 16, -56.14, 24.33, 0.01628, 15, -6.26, 31.34, 0.24975, 11, -2.84, 21.92, 0.73397, 2, 16, -37.66, 42.75, 0.41403, 11, 4.64, 46.93, 0.58597, 3, 16, 12.61, 57.2, 0.99071, 15, 66.06, 55.36, 0.008, 11, 41.95, 83.58, 0.00129, 1, 16, 39.51, 67.34, 1, 1, 16, 90.27, 79.83, 1, 1, 16, 107.21, 65.56, 1, 1, 16, 107.11, 30.31, 1, 1, 16, 93.04, 24.34, 1, 1, 16, 73.79, 21.71, 1, 1, 16, 53.96, 17.08, 1, 1, 16, 35.92, 11.2, 1, 2, 16, 17.94, 16.52, 0.91129, 15, 66.27, 14.33, 0.08871, 3, 16, -25.7, 20.75, 0.22416, 15, 23.49, 23.98, 0.66651, 11, 25.63, 33.28, 0.10933, 3, 16, -42.09, 17.99, 0.04418, 15, 6.89, 23.3, 0.48985, 11, 12.54, 23.05, 0.46597, 1, 11, 4.1, -0.74, 1, 2, 15, -18.18, -9.68, 0.18481, 11, 11.38, -18.36, 0.81519, 2, 16, 14.16, -14.59, 0.81942, 15, 58.63, -16.06, 0.18058, 2, 16, 33.06, -8.24, 0.98924, 15, 78.17, -12.12, 0.01076, 2, 16, -6.76, -13.1, 0.10603, 15, 38.06, -11.97, 0.89397, 2, 15, 7.52, -10.72, 0.7938, 11, 32.87, -4.22, 0.2062, 3, 16, -7.27, 18.96, 0.40747, 15, 41.56, 19.9, 0.56217, 11, 42.69, 40.49, 0.03036, 1, 15, 22.52, -11.33, 1, 3, 16, -18.66, -31.3, 0.07078, 15, 23.97, -28.54, 0.90626, 11, 56.62, -9.13, 0.02296], "hull": 25}}, "crocodile_eye1": {"crocodile_eye1": {"x": 0.02, "width": 19, "height": 12}, "crocodile_eye2": {"x": 0.03, "width": 19, "height": 12}}}}], "animations": {"dark": {"slots": {"crocodile_ice1": {"color": [{"color": "ffffff00"}]}, "crocodile_ice2": {"color": [{"color": "ffffff00"}]}, "crocodile3": {"color": [{"color": "787878ff"}]}, "bj": {"color": [{"color": "787878ff"}]}, "title": {"color": [{"color": "787878ff"}]}, "crocodile4": {"color": [{"color": "787878ff"}]}, "crocodile_ice3": {"color": [{"color": "ffffff00"}]}, "crocodile2": {"color": [{"color": "787878ff"}]}}}, "start": {"slots": {"crocodile_ice1": {"color": [{"time": 0.2333, "color": "ffffff00"}, {"time": 0.8, "color": "ffffff40"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.8, "color": "ffffff40"}, {"time": 3.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 4.2333, "color": "ffffff00"}, {"time": 4.8, "color": "ffffff40"}, {"time": 5.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.2333, "color": "ffffff00"}, {"time": 6.8, "color": "ffffff40"}, {"time": 7.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 8, "color": "ffffff00"}]}, "crocodile_ice2": {"color": [{"time": 0.0667, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffff32"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffff32"}, {"time": 3.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 4.0667, "color": "ffffff00"}, {"time": 4.6333, "color": "ffffff32"}, {"time": 5.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 6.0667, "color": "ffffff00"}, {"time": 6.6333, "color": "ffffff32"}, {"time": 7.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 8, "color": "ffffff00"}]}, "crocodile_ice3": {"color": [{"color": "ffffff00"}, {"time": 0.5667, "color": "ffffff40"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5667, "color": "ffffff40"}, {"time": 3.2, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.5667, "color": "ffffff40"}, {"time": 5.2, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00"}, {"time": 6.5667, "color": "ffffff40"}, {"time": 7.2, "color": "ffffff00", "curve": "stepped"}, {"time": 8, "color": "ffffff00"}]}, "crocodile_eye1": {"attachment": [{"time": 1.4667, "name": "crocodile_eye1"}, {"time": 1.5, "name": "crocodile_eye2"}, {"time": 1.6667, "name": "crocodile_eye1"}, {"time": 1.7, "name": null}, {"time": 5.3667, "name": "crocodile_eye1"}, {"time": 5.4, "name": "crocodile_eye2"}, {"time": 5.5667, "name": "crocodile_eye1"}, {"time": 5.6, "name": null}]}}, "bones": {"crocodile4": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": 7.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2333, "angle": 9.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 7.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "angle": 9.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 7.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5, "angle": 9.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5667, "angle": 7.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6333, "angle": 9.59, "curve": "stepped"}, {"time": 0.8333, "angle": 9.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2, "angle": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 6, "angle": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.88, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -0.88, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone3": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -0.86, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.2333, "angle": -2.39, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.3, "angle": -0.86, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.3667, "angle": -2.39, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.4333, "angle": -0.86, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.5, "angle": -2.39, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.5667, "angle": -0.86, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.6333, "angle": -2.39, "curve": "stepped"}, {"time": 0.8333, "angle": -2.39, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2, "angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 6, "angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone4": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -4.24, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.2333, "angle": -4.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.3, "angle": -4.19, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.3667, "angle": -4.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.4333, "angle": -4.19, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.5, "angle": -4.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.5667, "angle": -4.19, "curve": 0.323, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.6333, "angle": -4.82, "curve": "stepped"}, {"time": 0.8333, "angle": -4.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 2, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 6, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.2, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 6, "angle": -4.2, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.22, "y": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 1.22, "y": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone7": {"rotate": [{}, {"time": 2, "angle": 0.78}, {"time": 4}, {"time": 6, "angle": 0.78}, {"time": 8}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 4}, {"time": 6, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 8}]}}, "deform": {"default": {"crocodile4": {"crocodile4": [{"offset": 72, "vertices": [0.56129, 4.10811, 1.07904, 3.98854, -1.43707, 3.86469], "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.1333, "offset": 68, "vertices": [-1.10042, -1.43507, -0.54034, -1.62675, -4.08739, 10.04772, -1.93749, 10.80601, -7.57131, 8.16364, 0.28822, 4.29033], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "offset": 68, "vertices": [-1.26499, -1.64969, -0.62115, -1.87004, -5.90233, 10.23329, -3.61425, 11.3301, -9.20813, 7.69747, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "offset": 68, "vertices": [-1.76142, -2.78284, -0.67365, -3.10604, -9.58942, 13.41853, -6.55876, 15.21215, -13.7983, 9.33174, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "offset": 68, "vertices": [-1.26499, -1.64969, -0.62115, -1.87004, -5.90233, 10.23329, -3.61425, 11.3301, -9.20813, 7.69747, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "offset": 68, "vertices": [-1.76142, -2.78284, -0.67365, -3.10604, -9.58942, 13.41853, -6.55876, 15.21215, -13.7983, 9.33174, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "offset": 68, "vertices": [-1.26499, -1.64969, -0.62115, -1.87004, -5.90233, 10.23329, -3.61425, 11.3301, -9.20813, 7.69747, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.5, "offset": 68, "vertices": [-1.76142, -2.78284, -0.67365, -3.10604, -9.58942, 13.41853, -6.55876, 15.21215, -13.7983, 9.33174, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "offset": 68, "vertices": [-1.26499, -1.64969, -0.62115, -1.87004, -5.90233, 10.23329, -3.61425, 11.3301, -9.20813, 7.69747, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "offset": 68, "vertices": [-1.76142, -2.78284, -0.67365, -3.10604, -9.58942, 13.41853, -6.55876, 15.21215, -13.7983, 9.33174, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "offset": 68, "vertices": [-2.24834, -1.95914, -1.38635, -2.64015, -10.69263, 13.0784, -7.76089, 15.06014, -14.7647, 8.35916, 0.33132, 4.93197], "curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 68, "vertices": [-1.82577, 4.91482, -4.10629, 3.06654, -4.41565, 15.11218, -1.22836, 16.96204, -9.93835, 13.85583, -1.37263, 10.40155], "curve": 0.25, "c3": 0.75}, {"time": 4, "offset": 72, "vertices": [0.56129, 4.10811, 1.07904, 3.98854, -1.43707, 3.86469], "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 68, "vertices": [-1.82577, 4.91482, -4.10629, 3.06654, -4.41565, 15.11218, -1.22836, 16.96204, -9.93835, 13.85583, -1.37263, 10.40155], "curve": 0.25, "c3": 0.75}, {"time": 8, "offset": 72, "vertices": [0.56129, 4.10811, 1.07904, 3.98854, -1.43707, 3.86469]}]}, "crocodile3": {"crocodile3": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 22, "vertices": [3.52402, 1.546, 1.8348, 3.38268, 1.08593, 3.69184, 1.09095, 7.52346, -1.42149, 7.46801, -0.50833, 7.58513, 2.65294, 9.02522, -0.43446, 9.39686, 0.70458, 9.38059, 1.30336, 6.34026, -0.83492, 6.41863, -0.05297, 6.4726, -2.25988, -1.01662, -1.80491, -1.69788, -1.99695, -1.46721], "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 22, "vertices": [3.52402, 1.546, 1.8348, 3.38268, 1.08593, 3.69184, 1.09095, 7.52346, -1.42149, 7.46801, -0.50833, 7.58513, 2.65294, 9.02522, -0.43446, 9.39686, 0.70458, 9.38059, 1.30336, 6.34026, -0.83492, 6.41863, -0.05297, 6.4726, -2.25988, -1.01662, -1.80491, -1.69788, -1.99695, -1.46721], "curve": 0.25, "c3": 0.75}, {"time": 8}]}}}}}}