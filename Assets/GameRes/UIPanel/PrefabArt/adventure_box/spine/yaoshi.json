{"skeleton": {"hash": "3IIjp6bEIELUOLZZco/nhT9Q4W4", "spine": "3.8.95", "x": -531.93, "y": -333.63, "width": 532.71, "height": 550, "images": "", "audio": "C:/Users/<USER>/Desktop"}, "bones": [{"name": "root"}, {"name": "BIP", "parent": "root"}, {"name": "2", "parent": "BIP"}, {"name": "3", "parent": "BIP"}, {"name": "1", "parent": "BIP", "x": -16.28}, {"name": "5", "parent": "BIP"}, {"name": "6", "parent": "BIP"}, {"name": "7", "parent": "1", "length": 265.2, "rotation": 93.03, "x": -191.75, "y": -39.87}, {"name": "8", "parent": "7", "x": 228.8, "y": 20.02}, {"name": "9", "parent": "7", "x": -266.11, "y": 36.17}, {"name": "10", "parent": "7", "x": 73.14, "y": -375.26}, {"name": "4_2", "parent": "1", "length": 308.93, "rotation": 14.17, "x": -141.05, "y": -26.11}, {"name": "4_5", "parent": "4_2", "x": -236.87, "y": 10.53}, {"name": "4_3", "parent": "4_5", "x": -19.57, "y": 79.26}, {"name": "4_4", "parent": "4_5", "x": 268.27, "y": -92.24}, {"name": "11", "parent": "7", "x": -26.15, "y": 125.34}, {"name": "5_6", "parent": "4_2", "x": -159.76, "y": 5.07}, {"name": "4_7", "parent": "5_6", "x": 53.3, "y": 52.57}, {"name": "4_8", "parent": "5_6", "x": 208.45, "y": -67.36}, {"name": "6_9", "parent": "4_2", "x": -111.36, "y": 7.6}, {"name": "4_10", "parent": "6_9", "x": 81.34, "y": 35.66}, {"name": "4_11", "parent": "6_9", "x": 161.43, "y": -42.79}, {"name": "12", "parent": "7", "x": 59.2, "y": -360.87}, {"name": "13", "parent": "7", "x": -33.79, "y": 110.9}, {"name": "14", "parent": "7", "x": 137.89, "y": -9.99}, {"name": "15", "parent": "7", "x": -156.4, "y": 12.9}, {"name": "16", "parent": "7", "x": 75.72, "y": -407.78}, {"name": "17", "parent": "7", "x": -24.47, "y": 68.59}, {"name": "18", "parent": "7", "x": 104.82, "y": -58.38}, {"name": "19", "parent": "7", "x": -91.99, "y": -16.75}], "slots": [{"name": "BIP", "bone": "BIP", "attachment": "BIP"}, {"name": "4_2", "bone": "4_2", "color": "ffffff00", "attachment": "images/4_2"}, {"name": "4_1", "bone": "7", "color": "ffffff00", "attachment": "images/4_1"}, {"name": "5_2", "bone": "4_2", "color": "ffffff00", "attachment": "images/5_2"}, {"name": "5_1", "bone": "7", "color": "ffffff00", "attachment": "images/5_1"}, {"name": "6_2", "bone": "4_2", "color": "ffffff00", "attachment": "images/6_2"}, {"name": "6_1", "bone": "7", "color": "ffffff00", "attachment": "images/6_1"}, {"name": "BIP2", "bone": "BIP", "color": "ffffff00", "attachment": "images/zhe"}], "skins": [{"name": "default", "attachments": {"4_1": {"images/4_1": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.66667, 0.66667, 0.33333, 0.66667, 0.66667, 0.33333, 0.33333, 0.33333], "triangles": [0, 1, 11, 1, 12, 11, 11, 12, 10, 12, 14, 10, 10, 14, 9, 14, 8, 9, 12, 13, 14, 13, 15, 14, 14, 15, 8, 1, 2, 12, 2, 13, 12, 2, 3, 13, 3, 4, 13, 13, 4, 15, 15, 7, 8, 4, 5, 15, 15, 5, 7, 5, 6, 7], "vertices": [3, 9, -47.02, -394.21, 0.50695, 10, -386.28, 17.22, 0.49305, 15, -286.99, -483.38, 0, 4, 8, -532.35, -196.99, 2e-05, 9, -37.43, -213.13, 0.65738, 10, -376.69, 198.3, 0.33891, 15, -277.4, -302.3, 0.0037, 2, 9, -27.84, -32.05, 0.97153, 10, -367.1, 379.38, 0.02847, 2, 9, -18.25, 149.03, 0.83769, 15, -258.22, 59.85, 0.16231, 2, 9, 164.82, 139.33, 0.25449, 15, -75.15, 50.16, 0.74551, 3, 8, -147.02, 145.78, 0.36017, 10, 8.65, 541.06, 8e-05, 15, 107.93, 40.46, 0.63975, 3, 8, 36.06, 136.08, 0.90097, 10, 191.72, 531.37, 0, 15, 291.01, 30.77, 0.09903, 4, 8, 26.47, -45, 0.9309, 9, 521.39, -61.14, 1e-05, 10, 182.13, 350.29, 0.06873, 15, 281.42, -150.31, 0.00035, 4, 8, 16.88, -226.08, 0.38097, 9, 511.8, -242.22, 6e-05, 10, 172.54, 169.21, 0.61466, 15, 271.83, -331.39, 0.0043, 2, 8, 7.29, -407.16, 0.07717, 10, 162.96, -11.87, 0.92283, 4, 8, -175.78, -397.46, 0.0006, 9, 319.13, -413.6, 0.01371, 10, -20.12, -2.17, 0.98402, 15, 79.16, -502.78, 0.00167, 4, 8, -358.86, -387.76, 0, 9, 136.05, -403.91, 0.3363, 10, -203.2, 7.52, 0.65223, 15, -103.91, -493.08, 0.01146, 4, 8, -349.27, -206.69, 0.035, 9, 145.64, -222.83, 0.42118, 10, -193.61, 188.6, 0.4525, 15, -94.32, -312, 0.09131, 4, 8, -339.68, -25.61, 0.03396, 9, 155.23, -41.75, 0.4466, 10, -184.02, 369.68, 0.10792, 15, -84.73, -130.92, 0.41151, 4, 8, -166.19, -216.38, 0.21551, 9, 328.72, -232.52, 0.08208, 10, -10.53, 178.91, 0.61224, 15, 88.75, -321.7, 0.09016, 4, 8, -156.61, -35.3, 0.43742, 9, 338.31, -51.45, 0.04568, 10, -0.94, 359.99, 0.13715, 15, 98.34, -140.62, 0.37974], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 544, "height": 550}}, "4_2": {"images/4_2": {"type": "mesh", "uvs": [0, 0.39405, 0.09715, 0.27545, 0.31063, 0.30183, 0.45904, 0.41183, 0.50339, 0.27073, 0.62689, 0.1625, 0.89959, 0, 0.93694, 0, 0.96995, 0.11421, 1, 0.21819, 1, 0.34497, 0.6626, 0.61899, 0.63843, 0.61026, 0.62302, 0.57402, 0.61389, 0.58127, 0.60933, 0.60664, 0.63232, 0.61897, 0.65021, 0.63297, 0.65584, 0.71775, 0.64433, 0.83937, 0.55875, 0.96782, 0.49185, 1, 0.45996, 1, 0.15005, 0.83598, 0, 0.5716, 0.35826, 0.57208, 0.24681, 0.64222, 0.16649, 0.72511, 0.50485, 0.46688], "triangles": [26, 2, 25, 27, 1, 26, 23, 24, 27, 26, 25, 22, 23, 27, 26, 22, 23, 26, 8, 9, 10, 28, 4, 5, 3, 4, 28, 8, 28, 5, 6, 8, 5, 8, 6, 7, 13, 28, 8, 11, 12, 13, 14, 28, 13, 15, 28, 14, 13, 8, 11, 10, 11, 8, 16, 18, 15, 20, 28, 15, 22, 3, 28, 21, 22, 28, 25, 3, 22, 25, 2, 3, 26, 1, 2, 24, 0, 1, 24, 1, 27, 18, 16, 17, 19, 20, 15, 18, 19, 15, 20, 21, 28], "vertices": [2, 13, -102.78, 17.65, 0.98501, 12, -122.35, 96.91, 0.01499, 1, 13, -26, 26.9, 1, 3, 13, 126.28, -17.9, 0.6921, 11, -130.17, 71.89, 0.10593, 12, 106.7, 61.35, 0.20197, 3, 13, 226.89, -69.85, 0.16995, 11, -29.55, 19.95, 0.51773, 12, 207.32, 9.41, 0.31233, 3, 13, 266.93, -45.9, 0.03994, 11, 10.48, 43.89, 0.58406, 12, 247.35, 33.36, 0.376, 3, 13, 362.09, -43.8, 0.02748, 11, 105.65, 46, 0.71383, 12, 342.52, 35.46, 0.25869, 1, 11, 311.41, 33.28, 1, 1, 11, 338.32, 26.49, 1, 1, 11, 355.56, -5.43, 1, 1, 11, 371.25, -34.48, 1, 1, 11, 363.99, -63.25, 1, 1, 11, 105.23, -64.07, 1, 1, 11, 88.33, -57.69, 1, 1, 11, 79.3, -46.67, 1, 1, 11, 72.31, -46.65, 1, 2, 14, 36.16, 30.13, 0.12309, 11, 67.57, -51.58, 0.87691, 2, 14, 52.02, 23.15, 0.44815, 11, 83.43, -58.56, 0.55185, 2, 14, 64.1, 16.72, 0.67902, 11, 95.51, -64.99, 0.32098, 2, 14, 63.31, -3.54, 0.896, 11, 94.71, -85.25, 0.104, 2, 14, 48.05, -29.04, 0.99823, 11, 79.45, -110.75, 0.00177, 3, 14, -20.96, -42.62, 0.96246, 11, 10.45, -124.33, 0.01806, 12, 247.32, -134.86, 0.01948, 3, 14, -70.99, -37.76, 0.71784, 11, -39.59, -119.47, 0.17702, 12, 197.28, -130, 0.10514, 3, 14, -93.97, -31.96, 0.58401, 11, -62.57, -113.67, 0.24873, 12, 174.3, -124.2, 0.16727, 3, 14, -307.84, 61.6, 0.00017, 13, -19.99, -109.9, 0.08447, 12, -39.56, -30.64, 0.91537, 2, 13, -112.95, -22.63, 0.94649, 12, -132.52, 56.63, 0.05351, 4, 14, -142.73, 83.62, 0.0706, 13, 145.12, -87.88, 0.03108, 11, -111.33, 1.91, 0.44301, 12, 125.54, -8.62, 0.45531, 4, 14, -227.04, 87.97, 0.01693, 13, 60.81, -83.53, 0.04318, 11, -195.63, 6.26, 0.10195, 12, 41.24, -4.27, 0.83795, 3, 14, -289.65, 83.77, 0.00012, 13, -1.8, -87.73, 0.07271, 12, -21.37, -8.47, 0.92716, 3, 14, -31.1, 80.83, 0.00382, 11, 0.3, -0.87, 0.9961, 12, 237.17, -11.41, 8e-05], "hull": 25, "edges": [0, 2, 2, 4, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 14, 16, 16, 18, 56, 16, 8, 10, 10, 12, 6, 8, 4, 6], "width": 743, "height": 234}}, "5_1": {"images/5_1": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0.5, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0, 1, 0.5, 0.66667, 0.5, 0.33333, 0.5], "triangles": [4, 5, 6, 11, 4, 6, 3, 4, 11, 2, 3, 11, 11, 6, 7, 10, 11, 7, 2, 11, 10, 1, 2, 10, 10, 7, 8, 9, 10, 8, 1, 10, 9, 0, 1, 9], "vertices": [2, 25, -41.9, -377.03, 0.30503, 22, -257.49, -3.26, 0.69497, 3, 25, -33.02, -209.26, 0.50347, 24, -327.3, -186.37, 0.04454, 22, -248.61, 164.51, 0.45199, 3, 25, -24.13, -41.5, 0.93194, 24, -318.42, -18.61, 0.01568, 22, -239.73, 332.27, 0.05238, 2, 25, -15.25, 126.27, 0.55463, 23, -137.85, 28.27, 0.44537, 2, 24, -143.27, 140.35, 0.08565, 23, 28.42, 19.46, 0.91435, 2, 24, 23, 131.55, 0.70241, 23, 194.68, 10.66, 0.29759, 3, 25, 308.4, -59.11, 0.01993, 24, 14.11, -36.22, 0.92262, 22, 92.81, 314.66, 0.05745, 3, 25, 299.52, -226.87, 0.06212, 24, 5.23, -203.98, 0.34363, 22, 83.92, 146.9, 0.59425, 2, 24, -3.65, -371.75, 0.00794, 22, 75.04, -20.87, 0.99206, 3, 25, 124.37, -385.83, 0.12952, 24, -169.92, -362.94, 0.00492, 22, -91.23, -12.06, 0.86556, 4, 25, 133.25, -218.07, 0.27984, 24, -161.04, -195.18, 0.17855, 23, 10.65, -316.07, 0.00419, 22, -82.34, 155.7, 0.53742, 4, 25, 142.14, -50.3, 0.34107, 24, -152.15, -27.41, 0.33553, 23, 19.53, -148.3, 0.22305, 22, -73.46, 323.47, 0.10034], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 504, "height": 333}}, "5_2": {"images/5_2": {"type": "mesh", "uvs": [0.95108, 0.13882, 1, 0.3399, 1, 0.43839, 0.87562, 0.53471, 0.83932, 0.45176, 0.81241, 0.4612, 0.79877, 0.44155, 0.59391, 0.54917, 0.58523, 0.61517, 0.57471, 0.65925, 0.55691, 0.65809, 0.55643, 0.77287, 0.4811, 0.96842, 0.28661, 0.98963, 0.10518, 0.92856, 0.00145, 0.89151, 0.00156, 0.804, 0.00186, 0.5645, 0.07057, 0.37558, 0.08361, 0.35906, 0.28562, 0.35878, 0.40796, 0.40789, 0.41471, 0.35674, 0.47966, 0.21692, 0.54539, 0.2308, 0.57295, 0.27568, 0.9157, 0, 0.28064, 0.62335, 0.08253, 0.72494, 0.09857, 0.82874, 0.27086, 0.86861, 0.47288, 0.86861, 0.44202, 0.50672, 0.51398, 0.55237, 0.58994, 0.39982, 0.52896, 0.69732], "triangles": [9, 10, 8, 8, 10, 7, 7, 10, 33, 34, 25, 26, 34, 26, 0, 3, 4, 0, 6, 34, 0, 4, 6, 0, 3, 0, 1, 5, 6, 4, 2, 3, 1, 7, 34, 6, 32, 23, 24, 34, 24, 25, 7, 33, 34, 31, 27, 32, 27, 21, 32, 35, 32, 33, 32, 22, 23, 33, 32, 34, 34, 32, 24, 21, 22, 32, 35, 33, 10, 27, 20, 21, 28, 19, 27, 16, 17, 28, 29, 28, 27, 30, 29, 27, 14, 29, 30, 16, 28, 29, 29, 15, 16, 14, 15, 29, 27, 31, 30, 14, 30, 13, 28, 18, 19, 17, 18, 28, 27, 19, 20, 35, 31, 32, 11, 12, 31, 11, 35, 10, 11, 31, 35, 13, 31, 12, 13, 30, 31], "vertices": [1, 11, 343.25, -6.86, 1, 1, 11, 360.44, -48.73, 1, 1, 11, 356.07, -66.02, 1, 1, 11, 285.48, -66.18, 1, 1, 11, 269.8, -46.74, 1, 1, 11, 255.03, -44.77, 1, 1, 11, 248.62, -39.49, 1, 1, 11, 134.61, -30.8, 1, 1, 11, 127.06, -41.22, 1, 2, 18, 70.8, 14.76, 0.02756, 11, 119.49, -47.54, 0.97244, 2, 18, 61.36, 17.36, 0.19486, 11, 110.05, -44.94, 0.80514, 2, 18, 56.02, -2.72, 0.94212, 16, 264.47, -70.08, 0.05788, 1, 18, 7.19, -26.9, 1, 3, 18, -97.47, -4.44, 0.61508, 17, 57.68, -124.37, 0.04942, 16, 110.98, -71.8, 0.33551, 2, 18, -191.52, 30.69, 0.048, 16, 16.94, -36.67, 0.952, 1, 16, -36.74, -16.2, 1, 1, 16, -32.8, -0.86, 1, 2, 17, -75.33, -11.44, 0.56, 16, -22.03, 41.13, 0.44, 1, 17, -30.33, 12.47, 1, 1, 17, -22.64, 13.61, 1, 3, 17, 85.1, -13.53, 0.55361, 16, 138.4, 39.04, 0.0959, 11, -21.36, 44.11, 0.35049, 2, 17, 148.16, -38.61, 0.248, 11, 41.7, 19.02, 0.752, 2, 17, 154.03, -30.55, 0.248, 11, 47.57, 27.09, 0.752, 1, 11, 88.4, 42.89, 1, 1, 11, 122.84, 31.6, 1, 1, 11, 135.54, 20.02, 1, 1, 11, 330.54, 22.26, 1, 4, 18, -84.43, 60.64, 0.01047, 17, 70.72, -59.29, 0.0621, 16, 124.03, -6.72, 0.67834, 11, -35.74, -1.65, 0.24909, 4, 18, -194.58, 69.48, 0.00022, 17, -39.42, -50.45, 0.04837, 16, 13.88, 2.12, 0.944, 11, -145.88, 7.18, 0.00741, 4, 18, -190.62, 49.1, 0.02531, 17, -35.47, -70.83, 0.02349, 16, 17.83, -18.26, 0.94247, 11, -141.93, -13.19, 0.00873, 3, 18, -100.51, 18.92, 0.37906, 17, 54.64, -101.01, 0.05468, 16, 107.94, -48.45, 0.56626, 3, 18, 7.22, -8.27, 0.87125, 17, 162.37, -128.2, 0.00954, 16, 215.68, -75.64, 0.11921, 2, 18, 6.8, 59.39, 0.01816, 11, 55.49, -2.91, 0.98184, 3, 18, 43.15, 41.69, 0.23367, 17, 198.3, -78.24, 0.03247, 11, 91.84, -20.6, 0.73386, 2, 18, 90.41, 58.24, 0.00938, 11, 139.11, -4.06, 0.99062, 3, 18, 44.72, 14.24, 0.56729, 17, 199.87, -105.69, 0.01121, 11, 93.41, -48.06, 0.4215], "hull": 27, "edges": [2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 2, 30, 32, 32, 34, 64, 54, 0, 68, 68, 64, 18, 20, 20, 22, 12, 14, 14, 16, 16, 18, 8, 10, 10, 12, 52, 0], "width": 550, "height": 181}}, "6_1": {"images/6_1": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.5, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5], "triangles": [6, 7, 8, 17, 6, 8, 5, 6, 17, 4, 5, 17, 17, 8, 9, 16, 17, 9, 4, 17, 16, 3, 4, 16, 16, 9, 10, 15, 16, 10, 3, 16, 15, 2, 3, 15, 15, 10, 11, 14, 15, 11, 2, 15, 14, 1, 2, 14, 14, 11, 12, 13, 14, 12, 1, 14, 13, 0, 1, 13], "vertices": [3, 29, -46.98, -399.59, 0.14965, 28, -243.8, -357.96, 0.03707, 26, -214.7, -8.56, 0.81327, 3, 29, -41.61, -298.13, 0.22866, 28, -238.43, -256.5, 0.09329, 26, -209.32, 92.9, 0.67805, 3, 29, -36.24, -196.67, 0.42892, 28, -233.05, -155.04, 0.17626, 26, -203.95, 194.36, 0.39482, 3, 29, -30.86, -95.22, 0.72215, 28, -227.68, -53.58, 0.14117, 26, -198.58, 295.81, 0.13669, 3, 29, -25.49, 6.24, 0.96477, 27, -93.01, -79.09, 0.03391, 26, -193.21, 397.27, 0.00131, 2, 29, -20.12, 107.7, 0.35097, 27, -87.64, 22.36, 0.64903, 2, 28, -92.61, 142.75, 0.13689, 27, 36.68, 15.78, 0.86311, 2, 28, 31.72, 136.16, 0.57516, 27, 161.01, 9.2, 0.42484, 2, 28, 26.34, 34.71, 0.87481, 27, 155.64, -92.26, 0.12519, 3, 29, 217.79, -108.38, 0.06646, 28, 20.97, -66.75, 0.81812, 26, 50.07, 282.65, 0.11542, 3, 29, 212.41, -209.84, 0.11888, 28, 15.6, -168.21, 0.45193, 26, 44.7, 181.19, 0.42919, 3, 29, 207.04, -311.3, 0.05108, 28, 10.22, -269.67, 0.14564, 26, 39.33, 79.73, 0.80328, 1, 26, 33.95, -21.73, 1, 3, 29, 77.34, -406.17, 0.07342, 28, -119.47, -364.54, 0.02254, 26, -90.37, -15.14, 0.90404, 3, 29, 82.72, -304.72, 0.14284, 28, -114.1, -263.08, 0.12125, 26, -85, 86.31, 0.73591, 3, 29, 88.09, -203.26, 0.27789, 28, -108.73, -161.63, 0.30825, 26, -79.63, 187.77, 0.41386, 4, 29, 93.46, -101.8, 0.39863, 28, -103.36, -60.17, 0.45018, 27, 25.94, -187.14, 0.01165, 26, -74.25, 289.23, 0.13954, 4, 29, 98.84, -0.34, 0.2999, 28, -97.98, 41.29, 0.35793, 27, 31.31, -85.68, 0.33393, 26, -68.88, 390.69, 0.00824], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 508, "height": 249}}, "6_2": {"images/6_2": {"type": "mesh", "uvs": [0.94264, 0, 0.96315, 0.11432, 1, 0.25152, 1, 0.33535, 0.96643, 0.36555, 0.85233, 0.45549, 0.83462, 0.39793, 0.64999, 0.5887, 0.51395, 0.64716, 0.49083, 0.72035, 0.45163, 0.78606, 0.43786, 0.78208, 0.42842, 0.85092, 0.39599, 0.91768, 0.30325, 0.98886, 0.22471, 0.98872, 0.18182, 0.96498, 0.14101, 0.98857, 0.0391, 0.98839, 0, 0.87674, 0, 0.8126, 0.01301, 0.70159, 0.04176, 0.62919, 0.07621, 0.59351, 0.07441, 0.54695, 0.11038, 0.45637, 0.22788, 0.36974, 0.33585, 0.40087, 0.34772, 0.42327, 0.43982, 0.32313, 0.47707, 0.33816, 0.73682, 0.14067, 0.79831, 0.12963, 0.92062, 0, 0.2558, 0.62482, 0.0241, 0.79607, 0.18976, 0.83201, 0.13271, 0.85993, 0.06093, 0.836, 0.23271, 0.85993, 0.30193, 0.85793, 0.37885, 0.80409, 0.43269, 0.71435, 0.43846, 0.65852, 0.36988, 0.5947, 0.41218, 0.61664, 0.3436, 0.51294, 0.51004, 0.44133, 0.4651, 0.47376, 0.41672, 0.50869], "triangles": [34, 26, 27, 41, 45, 42, 41, 44, 45, 43, 9, 10, 10, 11, 42, 10, 42, 43, 9, 43, 8, 42, 45, 43, 45, 49, 43, 43, 48, 8, 43, 49, 48, 48, 47, 8, 8, 47, 7, 44, 34, 49, 34, 27, 46, 45, 44, 49, 28, 49, 46, 7, 47, 6, 46, 49, 34, 46, 27, 28, 49, 29, 48, 49, 28, 29, 48, 30, 47, 48, 29, 30, 4, 6, 1, 4, 5, 6, 1, 33, 0, 32, 33, 1, 32, 47, 31, 6, 47, 1, 47, 30, 31, 32, 1, 47, 3, 4, 2, 4, 1, 2, 40, 34, 44, 15, 16, 39, 39, 36, 34, 16, 36, 39, 16, 37, 36, 35, 23, 34, 38, 37, 17, 34, 38, 35, 20, 21, 35, 35, 22, 23, 35, 21, 22, 17, 37, 16, 34, 37, 38, 18, 38, 17, 19, 35, 18, 18, 35, 38, 19, 20, 35, 36, 37, 34, 34, 23, 25, 23, 24, 25, 34, 25, 26, 40, 44, 41, 41, 42, 11, 39, 40, 14, 14, 41, 13, 14, 40, 41, 13, 41, 12, 12, 41, 11, 15, 39, 14, 40, 39, 34], "vertices": [1, 11, 382.04, 18.55, 1, 1, 11, 387.84, -3.08, 1, 1, 11, 401.1, -30.62, 1, 1, 11, 397.6, -44.52, 1, 1, 11, 379.01, -45.16, 1, 1, 11, 316.4, -45.22, 1, 1, 11, 309.67, -33.37, 1, 1, 11, 206.45, -40.96, 1, 1, 11, 133.83, -32.94, 1, 2, 21, 80.06, -6.88, 0.00866, 11, 118.74, -42.06, 0.99134, 2, 21, 51.48, -12.67, 0.50171, 11, 90.16, -47.86, 0.49829, 2, 21, 43.43, -10.22, 0.59932, 11, 82.11, -45.4, 0.40068, 2, 21, 34.58, -20.4, 0.69588, 11, 73.26, -55.59, 0.30412, 2, 21, 13.05, -27.25, 0.872, 11, 51.73, -62.44, 0.128, 1, 21, -39.22, -26.98, 1, 3, 21, -74.87, -16.73, 0.57324, 20, -6.17, -95.17, 5e-05, 19, 75.18, -59.52, 0.42671, 3, 21, -92.38, -7.21, 0.2559, 20, -23.68, -85.65, 0.00011, 19, 57.66, -50, 0.74399, 2, 21, -112.69, -5.81, 0.104, 19, 37.35, -48.59, 0.896, 3, 21, -164.07, 7.49, 0.00046, 20, -95.37, -70.95, 0.00053, 19, -14.03, -35.29, 0.99901, 1, 19, -29.52, -11.69, 1, 2, 20, -108.18, -36.72, 0.176, 19, -26.84, -1.06, 0.824, 2, 20, -96.82, -20, 0.36458, 19, -15.48, 15.65, 0.63542, 2, 20, -78.97, -11.74, 0.55217, 19, 2.38, 23.91, 0.44783, 2, 20, -59.7, -10.31, 0.68081, 19, 21.64, 25.34, 0.31919, 2, 20, -58.68, -2.36, 0.76528, 19, 22.66, 33.3, 0.23472, 1, 20, -36.33, 7.98, 1, 1, 20, 27.9, 7.04, 1, 2, 20, 82.29, -12.18, 0.23278, 11, 52.27, 31.08, 0.76722, 2, 20, 87.48, -17.44, 0.21818, 11, 57.46, 25.82, 0.78182, 1, 11, 109.15, 30.44, 1, 1, 11, 127.74, 23.09, 1, 1, 11, 269.99, 22.02, 1, 1, 11, 302.17, 15.84, 1, 1, 11, 370.68, 21.41, 1, 1, 11, 1.61, 4.37, 1, 1, 19, -13.71, -1.46, 1, 3, 21, -83.52, 13.8, 0.32608, 20, -14.82, -64.64, 0.002, 19, 66.52, -28.98, 0.67192, 2, 21, -111.59, 16.6, 0.104, 19, 38.46, -26.18, 0.896, 1, 19, 3.61, -12.87, 1, 3, 21, -65.91, 3.58, 0.62241, 20, 2.79, -74.86, 0.00318, 19, 84.13, -39.2, 0.37441, 1, 21, -34.42, -5.1, 1, 2, 21, 8.97, -6.18, 0.872, 11, 47.65, -41.37, 0.128, 3, 21, 46.85, 1.68, 0.31437, 20, 115.54, -76.76, 0.00497, 11, 85.53, -33.5, 0.68066, 3, 21, 54.88, 10.19, 0.07576, 20, 123.57, -68.25, 0.00792, 11, 93.56, -25, 0.91632, 3, 21, 22.76, 29.7, 0.0239, 20, 91.46, -48.74, 0.02127, 11, 61.44, -5.49, 0.95483, 3, 21, 43.62, 20.55, 0.02752, 20, 112.32, -57.89, 0.01448, 11, 82.3, -14.63, 0.958, 2, 20, 81.6, -31.77, 0.01913, 11, 51.58, 11.49, 0.98087, 2, 20, 170.45, -41.56, 0.01989, 11, 140.43, 1.69, 0.98011, 2, 20, 145.91, -41.09, 0.02186, 11, 115.89, 2.17, 0.97814, 2, 20, 119.49, -40.58, 0.02398, 11, 89.47, 2.68, 0.97602], "hull": 34, "edges": [0, 66, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 26, 28, 36, 38, 38, 40, 40, 42, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 70, 68, 0, 2, 2, 4, 34, 36, 28, 30, 30, 32, 32, 34, 18, 20, 24, 26, 20, 22, 22, 24, 12, 14, 14, 16, 48, 50, 42, 44, 44, 46, 46, 48, 2, 94, 96, 94, 68, 98, 98, 96], "width": 532, "height": 171}}, "BIP": {"BIP": {"type": "clipping", "end": "6_1", "vertexCount": 15, "vertices": [-29.57, 55.23, -14.14, 42.28, -6.11, 28.87, -2.64, 19.29, -0.8, 10.99, 0, 0.04, -1.64, -10.91, -6.68, -22.13, -13.61, -32.97, -29.57, -40.78, -28.02, -126.8, -12.11, -808.95, -1290.76, -808.95, -1166.86, 357.36, 15.97, 349.1], "color": "ff000000"}}, "BIP2": {"images/zhe": {"color": "ffffff74", "x": -32.21, "y": 11.15, "width": 66, "height": 106}}}}], "animations": {"1": {"slots": {"6_1": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}]}, "6_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "BIP2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}}, "bones": {"7": {"shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 73.73, "y": 5.7}]}, "8": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -42.44, "y": 39.8}]}, "9": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 43.36, "y": 108.19}]}, "10": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 2, "y": -72.65}]}, "4_2": {"scale": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "y": 0.919}, {"time": 1.1333}], "shear": [{"y": -13.29, "curve": "stepped"}, {"time": 0.9, "y": -13.29}, {"time": 1.1}]}, "11": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -24.1, "y": 81}], "shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 16.04}]}, "1": {"translate": [{"x": -285.76, "y": -47.19, "curve": "stepped"}, {"time": 0.3333, "x": -285.76, "y": -47.19}, {"time": 0.5667, "x": -97.01, "y": -19.4, "curve": "stepped"}, {"time": 0.7, "x": -97.01, "y": -19.4}, {"time": 0.8, "x": 14.93, "y": 3.37}, {"time": 0.8333, "x": 8.57, "y": 2.57}]}, "13": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -3.23, "y": 47.49}]}, "14": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 6.21, "y": -69.45}]}, "15": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 30.09, "y": -6.71}]}, "12": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -14.33, "y": -59.08}]}, "6_9": {"translate": [{"x": 1.08, "y": -4.3, "curve": "stepped"}, {"time": 0.9, "x": 1.08, "y": -4.3}, {"time": 1.1}], "shear": [{"x": -45.09, "y": -36.61, "curve": "stepped"}, {"time": 0.9, "x": -45.09, "y": -36.61}, {"time": 1.1}]}, "4_10": {"translate": [{"x": -51.66, "y": 119.82, "curve": "stepped"}, {"time": 0.9, "x": -51.66, "y": 119.82}, {"time": 1.1}], "shear": [{"x": 30.08, "curve": "stepped"}, {"time": 0.9, "x": 30.08}, {"time": 1.1}]}, "4_11": {"translate": [{"x": -39.21, "y": 17.83, "curve": "stepped"}, {"time": 0.9, "x": -39.21, "y": 17.83}, {"time": 1.1}]}, "16": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -12.17, "y": -46.55}, {"time": 1.1, "x": 25.09, "y": -112.36}]}, "17": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 13.73, "y": -4.97}, {"time": 1.1, "x": 2.54, "y": 3.84}]}, "18": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -26.05, "y": -30.57}, {"time": 1.1, "x": 0.3, "y": -89.68}]}, "19": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 28.17, "y": 4.36}, {"time": 1.1, "x": 31.82, "y": 14.22}]}}}, "2": {"slots": {"5_1": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}]}, "5_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "BIP2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}}, "bones": {"7": {"shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 73.73, "y": 5.7}]}, "8": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -42.44, "y": 39.8}]}, "9": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 43.36, "y": 108.19}]}, "10": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 2, "y": -72.65}]}, "4_2": {"scale": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "y": 0.919}, {"time": 1.1333}], "shear": [{"y": -13.29, "curve": "stepped"}, {"time": 0.9, "y": -13.29}, {"time": 1.1}]}, "4_5": {"translate": [{"x": 59.07, "y": -5.93, "curve": "stepped"}, {"time": 0.9, "x": 59.07, "y": -5.93}, {"time": 1.1}], "shear": [{"y": -57.04, "curve": "stepped"}, {"time": 0.9, "y": -57.04}, {"time": 1.1}]}, "4_3": {"translate": [{"x": -237.67, "y": 338.02, "curve": "stepped"}, {"time": 0.9, "x": -237.67, "y": 338.02}, {"time": 1.1}]}, "4_4": {"translate": [{"x": 237.23, "y": -415.81, "curve": "stepped"}, {"time": 0.9, "x": 237.23, "y": -415.81}, {"time": 1.1}]}, "11": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -24.1, "y": 81}], "shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 16.04}]}, "1": {"translate": [{"x": -285.76, "y": -47.19, "curve": "stepped"}, {"time": 0.3333, "x": -285.76, "y": -47.19}, {"time": 0.5667, "x": -97.01, "y": -19.4, "curve": "stepped"}, {"time": 0.7, "x": -97.01, "y": -19.4}, {"time": 0.8, "x": 14.93, "y": 3.37}, {"time": 0.8333, "x": 8.57, "y": 2.57}]}, "5_6": {"translate": [{"x": -0.95, "y": -7.07, "curve": "stepped"}, {"time": 0.9, "x": -0.95, "y": -7.07}, {"time": 1.1}], "shear": [{"x": -27.25, "y": -36.61, "curve": "stepped"}, {"time": 0.9, "x": -27.25, "y": -36.61}, {"time": 1.1}]}, "4_7": {"translate": [{"x": -28.83, "y": 144.2, "curve": "stepped"}, {"time": 0.9, "x": -28.83, "y": 144.2}, {"time": 1.1}], "shear": [{"x": 21.5, "curve": "stepped"}, {"time": 0.9, "x": 21.5}, {"time": 1.1}]}, "4_8": {"translate": [{"x": -50.6, "y": -16.73, "curve": "stepped"}, {"time": 0.9, "x": -50.6, "y": -16.73}, {"time": 1.1}]}, "13": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -3.23, "y": 47.49}]}, "14": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 6.21, "y": -69.45}]}, "15": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 30.09, "y": -6.71}]}, "12": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -14.33, "y": -59.08}]}, "6_9": {"translate": [{"x": 1.08, "y": -4.3, "curve": "stepped"}, {"time": 0.9, "x": 1.08, "y": -4.3}, {"time": 1.1}], "shear": [{"x": -45.09, "y": -36.61, "curve": "stepped"}, {"time": 0.9, "x": -45.09, "y": -36.61}, {"time": 1.1}]}, "4_10": {"translate": [{"x": -51.66, "y": 119.82, "curve": "stepped"}, {"time": 0.9, "x": -51.66, "y": 119.82}, {"time": 1.1}], "shear": [{"x": 30.08, "curve": "stepped"}, {"time": 0.9, "x": 30.08}, {"time": 1.1}]}, "4_11": {"translate": [{"x": -39.21, "y": 17.83, "curve": "stepped"}, {"time": 0.9, "x": -39.21, "y": 17.83}, {"time": 1.1}]}, "16": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -12.17, "y": -46.55}, {"time": 1.1, "x": 25.09, "y": -112.36}]}, "17": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 13.73, "y": -4.97}, {"time": 1.1, "x": 2.54, "y": 3.84}]}, "18": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -26.05, "y": -30.57}, {"time": 1.1, "x": 0.3, "y": -89.68}]}, "19": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 28.17, "y": 4.36}, {"time": 1.1, "x": 31.82, "y": 14.22}]}}}, "3": {"slots": {"4_1": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}]}, "4_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "BIP2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}}, "bones": {"7": {"shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 73.73, "y": 5.7}]}, "8": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -42.44, "y": 39.8}]}, "9": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 43.36, "y": 108.19}]}, "10": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 2, "y": -72.65}]}, "4_2": {"scale": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "y": 0.919}, {"time": 1.1333}], "shear": [{"y": -13.29, "curve": "stepped"}, {"time": 0.9, "y": -13.29}, {"time": 1.1}]}, "4_5": {"translate": [{"x": 59.07, "y": -5.93, "curve": "stepped"}, {"time": 0.9, "x": 59.07, "y": -5.93}, {"time": 1.1}], "shear": [{"y": -57.04, "curve": "stepped"}, {"time": 0.9, "y": -57.04}, {"time": 1.1}]}, "4_3": {"translate": [{"x": -237.67, "y": 338.02, "curve": "stepped"}, {"time": 0.9, "x": -237.67, "y": 338.02}, {"time": 1.1}]}, "4_4": {"translate": [{"x": 237.23, "y": -415.81, "curve": "stepped"}, {"time": 0.9, "x": 237.23, "y": -415.81}, {"time": 1.1}]}, "11": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -24.1, "y": 81}], "shear": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 16.04}]}, "1": {"translate": [{"x": -285.76, "y": -47.19, "curve": "stepped"}, {"time": 0.3333, "x": -285.76, "y": -47.19}, {"time": 0.5667, "x": -97.01, "y": -19.4, "curve": "stepped"}, {"time": 0.7, "x": -97.01, "y": -19.4}, {"time": 0.8, "x": 14.93, "y": 3.37}, {"time": 0.8333, "x": 8.57, "y": 2.57}]}, "5_6": {"translate": [{"x": -0.95, "y": -7.07, "curve": "stepped"}, {"time": 0.9, "x": -0.95, "y": -7.07}, {"time": 1.1}], "shear": [{"x": -27.25, "y": -36.61, "curve": "stepped"}, {"time": 0.9, "x": -27.25, "y": -36.61}, {"time": 1.1}]}, "4_7": {"translate": [{"x": -28.83, "y": 144.2, "curve": "stepped"}, {"time": 0.9, "x": -28.83, "y": 144.2}, {"time": 1.1}], "shear": [{"x": 21.5, "curve": "stepped"}, {"time": 0.9, "x": 21.5}, {"time": 1.1}]}, "4_8": {"translate": [{"x": -50.6, "y": -16.73, "curve": "stepped"}, {"time": 0.9, "x": -50.6, "y": -16.73}, {"time": 1.1}]}, "13": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -3.23, "y": 47.49}]}, "14": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 6.21, "y": -69.45}]}, "15": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": 30.09, "y": -6.71}]}, "12": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1.1, "x": -14.33, "y": -59.08}]}, "6_9": {"translate": [{"x": 1.08, "y": -4.3, "curve": "stepped"}, {"time": 0.9, "x": 1.08, "y": -4.3}, {"time": 1.1}], "shear": [{"x": -45.09, "y": -36.61, "curve": "stepped"}, {"time": 0.9, "x": -45.09, "y": -36.61}, {"time": 1.1}]}, "4_10": {"translate": [{"x": -51.66, "y": 119.82, "curve": "stepped"}, {"time": 0.9, "x": -51.66, "y": 119.82}, {"time": 1.1}], "shear": [{"x": 30.08, "curve": "stepped"}, {"time": 0.9, "x": 30.08}, {"time": 1.1}]}, "4_11": {"translate": [{"x": -39.21, "y": 17.83, "curve": "stepped"}, {"time": 0.9, "x": -39.21, "y": 17.83}, {"time": 1.1}]}, "16": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -12.17, "y": -46.55}, {"time": 1.1, "x": 25.09, "y": -112.36}]}, "17": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 13.73, "y": -4.97}, {"time": 1.1, "x": 2.54, "y": 3.84}]}, "18": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": -26.05, "y": -30.57}, {"time": 1.1, "x": 0.3, "y": -89.68}]}, "19": {"translate": [{"curve": "stepped"}, {"time": 0.9}, {"time": 1, "x": 28.17, "y": 4.36}, {"time": 1.1, "x": 31.82, "y": 14.22}]}}}}}