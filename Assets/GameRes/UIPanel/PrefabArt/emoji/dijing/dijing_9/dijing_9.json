{"skeleton": {"hash": "aTL0rq1LH6qVtzfi6wU2uCJ+Qak", "spine": "3.8.95", "x": -200, "y": -13.5, "width": 400, "height": 400}, "bones": [{"name": "root"}, {"name": "9_1", "parent": "root", "length": 60.8, "rotation": 94.64, "x": -27.52, "y": 9, "scaleX": 0.5, "scaleY": 0.5}, {"name": "9_2", "parent": "9_1", "length": 48.07, "rotation": -27.71, "x": 98.64, "y": -9.64, "scaleX": 0.5, "scaleY": 0.5, "transform": "noScale"}, {"name": "9_3", "parent": "9_2", "length": 88.27, "rotation": -3.97, "x": 48.07, "scaleX": 0.5, "scaleY": 0.5, "transform": "noScale"}, {"name": "9_35", "parent": "9_3", "length": 24.55, "rotation": -116.3, "x": 113.66, "y": -78.78}, {"name": "9_5", "parent": "9_35", "length": 61.55, "rotation": 14.96, "x": 34.02, "y": 3.65}, {"name": "9_6", "parent": "9_5", "length": 56.06, "rotation": 63.45, "x": 63.59}, {"name": "9_7", "parent": "9_6", "length": 83.86, "rotation": 77.34, "x": 57.84, "y": 1.88}, {"name": "9_8", "parent": "9_7", "length": 86.44, "rotation": 14.64, "x": 83.86}, {"name": "9_10", "parent": "9_8", "length": 44.42, "rotation": -4.27, "x": 102.78, "y": 26.6}, {"name": "9_9", "parent": "9_3", "length": 71.42, "rotation": 30.99, "x": 245.63, "y": 61.01}, {"name": "9_11", "parent": "9_9", "length": 28.28, "rotation": -173.94, "x": -19.69, "y": -11.78}, {"name": "9_12", "parent": "9_11", "length": 42.75, "rotation": -26.71, "x": 28.28}, {"name": "9_4", "parent": "9_9", "x": 26.4, "y": -81.45}, {"name": "9_14", "parent": "9_9", "x": -3.01, "y": -79.42}, {"name": "9_15", "parent": "9_9", "length": 56.11, "rotation": 52.36, "x": 147.3, "y": 64.55}, {"name": "9_16", "parent": "9_9", "length": 44.78, "rotation": -54.14, "x": 138.08, "y": -80.95}, {"name": "9_17", "parent": "9_9", "length": 36.04, "rotation": -3.95, "x": 127.18, "y": -12.88}, {"name": "9_13", "parent": "9_17", "length": 31.19, "rotation": -23.2, "x": 13.92, "y": 37.67}, {"name": "9_18", "parent": "9_13", "length": 42.28, "rotation": 32.11, "x": 31.19}, {"name": "9_20", "parent": "9_17", "length": 28.01, "rotation": -142.12, "x": 32.76, "y": -31.94}, {"name": "9_21", "parent": "9_15", "length": 33.98, "rotation": -40.93, "x": 73.6, "y": 40.21}, {"name": "9_22", "parent": "9_21", "length": 31.86, "rotation": 28.58, "x": 33.98}, {"name": "9_23", "parent": "9_22", "length": 27.81, "rotation": 59.67, "x": 31.86}, {"name": "9_24", "parent": "9_23", "length": 51.43, "rotation": 39.14, "x": 31.77, "y": 2.41}, {"name": "9_25", "parent": "9_24", "length": 44.54, "rotation": -20.54, "x": 51.93, "y": -0.65}, {"name": "9_26", "parent": "9_16", "length": 21.44, "rotation": -33.22, "x": 50.96, "y": -36.07}, {"name": "9_27", "parent": "9_26", "length": 31.52, "rotation": 58.85, "x": 21.44}, {"name": "9_28", "parent": "9_27", "length": 31.13, "rotation": -2, "x": 33.28, "y": -1.89}, {"name": "9_29", "parent": "9_28", "length": 27.86, "rotation": -39.13, "x": 31.87, "y": -1.47}, {"name": "9_30", "parent": "9_29", "length": 43.6, "rotation": -86.3, "x": 27.86}, {"name": "9_31", "parent": "9_30", "length": 38.29, "rotation": 13.52, "x": 43.6}, {"name": "9_19", "parent": "9_21", "length": 37.23, "rotation": 114.27, "x": -15.95, "y": 65.54}, {"name": "9_33", "parent": "9_21", "length": 29.89, "rotation": 174.09, "x": -64.79, "y": 33.11}, {"name": "9_34", "parent": "9_9", "length": 30.48, "rotation": -177.77, "x": -148.39, "y": 2.85}], "slots": [{"name": "biaoqing_frame", "bone": "root", "attachment": "biaoqing_frame"}, {"name": "9_1", "bone": "9_1", "attachment": "9_1"}, {"name": "9_2", "bone": "9_35", "attachment": "9_2"}, {"name": "9_3", "bone": "9_33", "attachment": "9_3"}, {"name": "9_4", "bone": "9_34", "attachment": "9_4"}, {"name": "9_5", "bone": "9_10", "attachment": "9_5"}, {"name": "9_6", "bone": "9_20", "attachment": "9_6"}, {"name": "9_7", "bone": "9_13", "attachment": "9_7"}], "skins": [{"name": "default", "attachments": {"9_1": {"9_1": {"type": "mesh", "uvs": [0.49146, 0.0495, 0.66266, 0.0536, 0.79426, 0.05692, 0.82196, 0.07941, 0.84191, 0.15033, 0.88388, 0.25464, 0.93753, 0.2674, 0.95206, 0.32887, 0.95489, 0.4032, 0.95403, 0.44677, 0.95215, 0.53478, 0.96416, 0.61806, 0.973, 0.71711, 0.97049, 0.78877, 0.93704, 0.82282, 0.9177, 0.88042, 0.90092, 0.91682, 0.90345, 0.95314, 0.87673, 0.99683, 0.10006, 0.99649, 0.06389, 0.97882, 0.03589, 0.95359, 0.00877, 0.90019, 0.00031, 0.86218, 0.00106, 0.82485, 0.01206, 0.78506, 5e-05, 0.7259, 0, 0.65479, 0.01263, 0.62624, 0.03036, 0.60554, 0.07929, 0.57159, 0.24213, 0.39624, 0.30306, 0.24771, 0.30085, 0.16842, 0.31305, 0.12591, 0.3318, 0.1041, 0.35972, 0.0833, 0.4129, 0.06501, 0.44364, 0.05491, 0.07286, 0.71746, 0.10333, 0.67941, 0.17799, 0.64309, 0.30901, 0.63271, 0.46441, 0.65693, 0.59847, 0.69844, 0.73711, 0.77799, 0.84858, 0.86447, 0.3928, 0.35945, 0.3989, 0.5791, 0.52875, 0.53904, 0.58254, 0.36523, 0.80599, 0.68231, 0.85461, 0.48619, 0.37219, 0.49193], "triangles": [35, 32, 33, 37, 32, 36, 32, 35, 36, 35, 33, 34, 32, 37, 47, 50, 0, 1, 31, 32, 47, 47, 38, 0, 47, 37, 38, 0, 50, 47, 8, 52, 7, 5, 50, 4, 7, 52, 5, 7, 5, 6, 9, 52, 8, 10, 52, 9, 49, 47, 50, 1, 4, 50, 2, 4, 1, 3, 4, 2, 50, 52, 49, 5, 52, 50, 11, 51, 52, 11, 52, 10, 51, 49, 52, 51, 11, 12, 53, 31, 47, 53, 47, 49, 48, 53, 49, 42, 31, 53, 42, 53, 48, 41, 30, 31, 42, 41, 31, 43, 48, 49, 44, 49, 51, 43, 49, 44, 42, 48, 43, 40, 30, 41, 29, 30, 40, 40, 28, 29, 39, 28, 40, 27, 28, 39, 26, 27, 39, 45, 44, 51, 25, 26, 39, 12, 14, 51, 13, 14, 12, 46, 45, 51, 14, 46, 51, 15, 46, 14, 25, 23, 24, 22, 23, 25, 16, 46, 15, 39, 22, 25, 20, 21, 22, 22, 19, 20, 41, 19, 39, 19, 22, 39, 18, 46, 16, 18, 16, 17, 45, 46, 18, 19, 44, 45, 19, 43, 44, 41, 39, 40, 41, 42, 19, 43, 19, 42, 45, 18, 19], "vertices": [2, 2, 156.04, 45.1, 0.00054, 3, 104.59, 52.46, 0.99946, 1, 3, 128.39, 3.3, 1, 2, 1, 247.87, -138.37, 0.00025, 3, 146.64, -34.52, 0.99975, 3, 1, 240.86, -146.67, 0.00139, 2, 189.62, -55.19, 0.00027, 3, 145.03, -45.26, 0.99834, 3, 1, 220.48, -151.4, 0.00399, 2, 173.78, -68.86, 0.00212, 3, 130.17, -59.99, 0.99388, 3, 1, 190.19, -162.37, 0.01201, 2, 152.06, -92.66, 0.01042, 3, 110.15, -85.24, 0.97756, 3, 1, 185.23, -179.14, 0.03448, 2, 155.47, -109.81, 0.03643, 3, 114.74, -102.11, 0.92908, 3, 1, 167.64, -182.37, 0.05774, 2, 141.39, -120.85, 0.06336, 3, 101.46, -114.09, 0.87891, 3, 1, 146.74, -181.58, 0.1094, 2, 122.53, -129.86, 0.11531, 3, 83.27, -124.4, 0.77529, 3, 1, 134.56, -180.32, 0.1349, 2, 111.16, -134.41, 0.13659, 3, 72.24, -129.72, 0.72851, 3, 1, 109.96, -177.72, 0.19981, 2, 88.17, -143.55, 0.17926, 3, 49.94, -140.43, 0.62093, 3, 1, 86.33, -179.65, 0.28267, 2, 68.14, -156.24, 0.21374, 3, 30.84, -154.48, 0.50359, 3, 1, 58.36, -180.21, 0.38452, 2, 43.64, -169.74, 0.23292, 3, 7.33, -169.64, 0.38256, 3, 1, 38.35, -177.78, 0.47051, 2, 24.8, -176.9, 0.2333, 3, -10.97, -178.08, 0.29619, 3, 1, 29.68, -166.38, 0.49975, 2, 11.81, -170.83, 0.22952, 3, -24.34, -172.93, 0.27073, 3, 1, 14.04, -158.92, 0.55677, 2, -5.5, -171.49, 0.22214, 3, -41.56, -174.79, 0.22108, 3, 1, 4.28, -152.76, 0.58, 2, -17, -170.58, 0.21738, 3, -53.11, -174.67, 0.20261, 1, 1, -5.96, -152.73, 1, 1, 1, -17.51, -143.25, 1, 1, 1, 2.61, 103.69, 1, 1, 1, 8.49, 114.79, 1, 1, 1, 16.28, 123.12, 1, 1, 1, 31.94, 130.53, 1, 1, 1, 42.8, 132.36, 1, 1, 1, 53.24, 131.27, 1, 2, 1, 64.1, 126.87, 0.78495, 2, -94.05, 104.8, 0.21505, 2, 1, 80.98, 129.35, 0.71842, 2, -80.26, 114.84, 0.28158, 2, 1, 100.9, 127.75, 0.66245, 2, -61.88, 122.68, 0.33755, 2, 1, 108.57, 123.08, 0.64349, 2, -52.92, 122.12, 0.35651, 3, 1, 113.91, 116.98, 0.62421, 2, -45.35, 119.2, 0.37576, 3, -101.45, 112.44, 3e-05, 3, 1, 122.15, 100.65, 0.55554, 2, -30.46, 108.57, 0.44258, 3, -85.86, 102.88, 0.00188, 3, 1, 167.07, 44.89, 0.07206, 2, 35.23, 80.09, 0.63804, 3, -18.36, 79.01, 0.2899, 3, 1, 207.1, 22.14, 0.00036, 2, 81.24, 78.57, 0.17624, 3, 27.66, 80.67, 0.82339, 2, 2, 101.47, 87.94, 0.06195, 3, 47.18, 91.43, 0.93805, 2, 2, 113.98, 89.04, 0.04027, 3, 59.59, 93.39, 0.95973, 2, 2, 121.97, 85.94, 0.02812, 3, 67.77, 90.85, 0.97188, 2, 2, 130.83, 80.04, 0.01822, 3, 77.02, 85.58, 0.98178, 2, 2, 142.21, 66.45, 0.00496, 3, 89.31, 72.8, 0.99504, 2, 2, 148.66, 58.54, 0.00234, 3, 96.3, 65.36, 0.99766, 2, 1, 81.47, 106, 0.7069, 2, -68.97, 94.4, 0.2931, 2, 1, 91.34, 95.45, 0.64927, 2, -55.33, 89.65, 0.35073, 3, 1, 99.58, 70.89, 0.54003, 2, -36.61, 71.74, 0.45769, 3, -89.44, 65.7, 0.00229, 3, 1, 99.11, 28.99, 0.36016, 2, -17.55, 34.42, 0.63644, 3, -67.84, 29.8, 0.0034, 3, 1, 88.33, -19.87, 0.20298, 2, -4.38, -13.85, 0.79079, 3, -51.37, -17.45, 0.00623, 3, 1, 73.24, -61.55, 0.41044, 2, 1.65, -57.77, 0.45547, 3, -42.31, -60.84, 0.1341, 3, 1, 47.39, -103.83, 0.51677, 2, -1.59, -107.22, 0.26702, 3, -42.12, -110.4, 0.21621, 3, 1, 20.29, -137.3, 0.55711, 2, -10.01, -149.45, 0.22434, 3, -47.6, -153.12, 0.21855, 3, 1, 173.49, -3.85, 0.00085, 2, 63.58, 39.92, 0.28762, 3, 12.7, 40.9, 0.71153, 3, 1, 111.81, -0.8, 0.0174, 2, 7.55, 13.95, 0.9804, 3, -41.39, 11.11, 0.0022, 3, 1, 119.69, -43, 0.08986, 2, 34.14, -19.75, 0.57601, 3, -12.53, -20.67, 0.33413, 3, 1, 166.98, -64.05, 0.02186, 2, 85.8, -16.4, 0.13534, 3, 38.77, -13.75, 0.8428, 3, 1, 72.41, -127.9, 0.36819, 2, 31.76, -116.89, 0.26866, 3, -8.18, -117.74, 0.36315, 3, 1, 126.09, -147.81, 0.1511, 2, 88.54, -109.57, 0.19493, 3, 47.95, -106.5, 0.65397, 3, 1, 136.91, 5.71, 0.02179, 2, 26.75, 31.38, 0.74302, 3, -23.45, 29.83, 0.23519], "hull": 39}}, "9_2": {"9_2": {"type": "mesh", "uvs": [0.75494, 0.00631, 0.85503, 0.10419, 0.85955, 0.12982, 0.93151, 0.30105, 0.96279, 0.41041, 0.99457, 0.56728, 0.99451, 0.68159, 0.96872, 0.76113, 0.87211, 0.89414, 0.77685, 0.97434, 0.71964, 0.99388, 0.58939, 0.99406, 0.46931, 0.95933, 0.32048, 0.86939, 0.23444, 0.80267, 0.11636, 0.7528, 0.03344, 0.761, 0.00557, 0.5937, 0.00525, 0.48965, 0.02124, 0.3741, 0.03632, 0.32015, 0.07704, 0.21861, 0.15635, 0.14196, 0.18164, 0.13555, 0.24902, 0.13582, 0.31416, 0.17712, 0.37004, 0.27641, 0.39101, 0.39557, 0.39132, 0.49219, 0.38589, 0.53713, 0.47466, 0.60471, 0.55496, 0.62904, 0.61919, 0.61123, 0.65363, 0.57383, 0.68094, 0.50585, 0.68126, 0.45574, 0.64225, 0.32834, 0.56358, 0.20011, 0.56405, 0.13721, 0.59779, 0.05533, 0.63121, 0.01848, 0.6513, 0.00575], "triangles": [27, 14, 15, 18, 26, 27, 16, 17, 15, 23, 21, 22, 15, 17, 18, 21, 23, 25, 15, 18, 27, 19, 26, 18, 20, 25, 19, 21, 25, 20, 25, 23, 24, 25, 26, 19, 6, 33, 5, 5, 33, 34, 4, 5, 34, 4, 34, 35, 35, 3, 4, 3, 36, 2, 0, 1, 2, 36, 3, 35, 0, 2, 37, 41, 0, 40, 36, 37, 2, 0, 37, 38, 38, 39, 0, 40, 0, 39, 12, 31, 11, 11, 32, 10, 11, 31, 32, 10, 32, 9, 9, 32, 8, 32, 33, 8, 8, 33, 7, 7, 33, 6, 12, 30, 31, 12, 13, 30, 13, 14, 30, 14, 29, 30, 28, 29, 14, 28, 14, 27], "vertices": [1, 7, 103.33, -9.07, 1, 1, 7, 83.99, -23.16, 1, 1, 7, 79.76, -23.06, 1, 1, 7, 49.91, -29.68, 1, 1, 7, 31.4, -31.34, 1, 2, 6, 89.69, 0.22, 0.04306, 7, 5.36, -31.44, 0.95694, 2, 6, 81.83, -16.55, 0.3801, 7, -12.73, -27.45, 0.6199, 2, 6, 72.19, -26.27, 0.71695, 7, -24.32, -20.17, 0.28305, 2, 6, 47.4, -38.46, 0.99917, 7, -41.65, 1.35, 0.00083, 2, 5, 130.74, 4.44, 0.00049, 6, 26.44, -43, 0.99951, 2, 5, 124.45, -4.4, 0.0138, 6, 15.73, -41.32, 0.9862, 2, 5, 102.44, -18.9, 0.23658, 6, -7.08, -28.11, 0.76342, 2, 5, 73.04, -27.84, 0.7738, 6, -28.22, -5.81, 0.2262, 1, 5, 39.29, -32.96, 1, 2, 5, 20.51, -34.05, 0.76, 4, 62.62, -23.95, 0.24, 1, 4, 43.52, -36.09, 1, 1, 4, 35.73, -48.79, 1, 1, 4, 11.01, -36.61, 1, 1, 4, -2.55, -26.59, 1, 1, 4, -15.86, -13.12, 1, 1, 4, -21.26, -5.74, 1, 1, 4, -30.1, 9.93, 1, 1, 4, -31.59, 28.73, 1, 2, 5, -54.01, 44.79, 0.208, 4, -29.72, 32.99, 0.792, 2, 5, -44.53, 52.25, 0.44, 4, -22.48, 42.64, 0.56, 2, 5, -31.24, 54.24, 0.584, 4, -10.15, 48, 0.416, 2, 5, -13.41, 47.85, 0.616, 4, 8.72, 46.42, 0.384, 2, 5, 1.52, 35.05, 0.672, 4, 26.45, 37.9, 0.328, 1, 5, 11.29, 22.81, 1, 2, 5, 15.05, 16.5, 0.99975, 6, -14.47, 65.89, 0.00025, 3, 5, 35.77, 17.79, 0.91244, 6, -4.06, 47.92, 0.08738, 7, 31.35, 70.48, 0.00018, 3, 5, 56.85, 23.62, 0.47614, 6, 10.58, 31.68, 0.50391, 7, 18.72, 52.64, 0.01995, 3, 5, 70.11, 33.03, 0.118, 6, 24.92, 24.02, 0.7438, 7, 14.38, 36.97, 0.13821, 3, 5, 72.76, 41.6, 0.0242, 6, 33.78, 25.48, 0.58471, 7, 17.75, 28.65, 0.39109, 3, 5, 70.14, 53.27, 0.00094, 6, 43.05, 33.03, 0.20547, 7, 27.15, 21.26, 0.79359, 2, 6, 46.54, 40.35, 0.06036, 7, 35.05, 19.45, 0.93964, 2, 6, 48.96, 62, 3e-05, 7, 56.71, 21.84, 0.99997, 1, 7, 80.03, 31.13, 1, 1, 7, 89.96, 28.86, 1, 1, 7, 101.62, 20.11, 1, 1, 7, 106.16, 12.98, 1, 1, 7, 107.4, 9.03, 1], "hull": 42}}, "9_3": {"9_3": {"type": "mesh", "uvs": [0.87747, 0.002, 0.88911, 0.0083, 0.90512, 0.02754, 0.92034, 0.05222, 0.93588, 0.10035, 0.95427, 0.16731, 0.96434, 0.22322, 0.97346, 0.24202, 0.99857, 0.26323, 0.9984, 0.27785, 0.99107, 0.30606, 0.98277, 0.30845, 0.97084, 0.30832, 0.95273, 0.30119, 0.94006, 0.29211, 0.92612, 0.27563, 0.92601, 0.26989, 0.91092, 0.26983, 0.89929, 0.28389, 0.88832, 0.31409, 0.84919, 0.38366, 0.82721, 0.40969, 0.81886, 0.41393, 0.79051, 0.41383, 0.77232, 0.40093, 0.77228, 0.42121, 0.76903, 0.42402, 0.75702, 0.42396, 0.75689, 0.43609, 0.77738, 0.46037, 0.79086, 0.48167, 0.80205, 0.50498, 0.8086, 0.52887, 0.80825, 0.5553, 0.80029, 0.57306, 0.78678, 0.59545, 0.7562, 0.63173, 0.74473, 0.68446, 0.73037, 0.76967, 0.70986, 0.79877, 0.69321, 0.80555, 0.67247, 0.824, 0.65873, 0.83844, 0.64557, 0.86616, 0.6342, 0.92007, 0.6297, 0.95059, 0.62427, 0.96329, 0.6156, 0.97259, 0.57928, 0.98779, 0.52742, 0.99804, 0.49316, 0.99805, 0.47645, 0.99162, 0.46663, 0.98382, 0.45965, 0.97393, 0.43437, 0.91765, 0.40793, 0.84689, 0.3982, 0.83738, 0.37532, 0.82383, 0.34064, 0.79917, 0.33104, 0.78793, 0.32148, 0.75967, 0.30289, 0.66478, 0.29533, 0.65082, 0.26151, 0.61864, 0.24689, 0.6007, 0.23969, 0.58098, 0.23944, 0.55917, 0.24421, 0.53086, 0.26007, 0.49332, 0.24164, 0.497, 0.2174, 0.49704, 0.21259, 0.49566, 0.20547, 0.48647, 0.20542, 0.44972, 0.18465, 0.43646, 0.17441, 0.45292, 0.15631, 0.47409, 0.14976, 0.47509, 0.10651, 0.45582, 0.08894, 0.43758, 0.07398, 0.40319, 0.06903, 0.38539, 0.06784, 0.36381, 0.08555, 0.34285, 0.04095, 0.34282, 0.00848, 0.32997, 0.00378, 0.3261, 0.00163, 0.30783, 0.00169, 0.28383, 0.00811, 0.27637, 0.02828, 0.27958, 0.04176, 0.26986, 0.07847, 0.17118, 0.10631, 0.12107, 0.12678, 0.08898, 0.15913, 0.05086, 0.18131, 0.03302, 0.20111, 0.02598, 0.23229, 0.03361, 0.26632, 0.05903, 0.29813, 0.09803, 0.32794, 0.14446, 0.33696, 0.16007, 0.37914, 0.14369, 0.424, 0.13323, 0.46233, 0.12803, 0.55897, 0.13269, 0.61797, 0.14414, 0.68211, 0.16083, 0.71845, 0.11021, 0.75882, 0.06255, 0.79194, 0.03474, 0.81527, 0.01783, 0.84163, 0.00673, 0.85963, 0.00198, 0.26594, 0.48729, 0.27187, 0.47859, 0.27611, 0.47189, 0.27498, 0.44947, 0.27074, 0.43909, 0.2569, 0.39659, 0.23175, 0.38822, 0.21367, 0.39123, 0.20774, 0.42571, 0.26583, 0.4883, 0.27854, 0.46902, 0.28322, 0.45787, 0.2885, 0.45045, 0.28274, 0.43515, 0.10193, 0.327, 0.13158, 0.31079, 0.15591, 0.30268, 0.17111, 0.30268, 0.19164, 0.35897, 0.16883, 0.36842, 0.15363, 0.37923, 0.18214, 0.41976, 0.32186, 0.18611, 0.30861, 0.21202, 0.29469, 0.25519, 0.28343, 0.32113, 0.27083, 0.35959, 0.26587, 0.40661, 0.27029, 0.41377, 0.4955, 0.41181, 0.49649, 0.34555, 0.50246, 0.37973, 0.56713, 0.3432, 0.5532, 0.38209, 0.55519, 0.41744, 0.60674, 0.50914, 0.62039, 0.4859, 0.65109, 0.47176, 0.68775, 0.46873, 0.72101, 0.49096, 0.73124, 0.53742, 0.72442, 0.58086, 0.71334, 0.60813, 0.67923, 0.62126, 0.63233, 0.62227, 0.61868, 0.54752, 0.62636, 0.57682, 0.62295, 0.67682, 0.60333, 0.72935, 0.57093, 0.77885, 0.55728, 0.79198, 0.49759, 0.77582, 0.49077, 0.71622, 0.47883, 0.6546, 0.46689, 0.62732, 0.41658, 0.61924, 0.39953, 0.59601, 0.39612, 0.55459, 0.41914, 0.52227, 0.44558, 0.51015, 0.4686, 0.50712, 0.71358, 0.20079, 0.73322, 0.23734, 0.74537, 0.28607, 0.75098, 0.32372, 0.76455, 0.35577, 0.64027, 0.52559, 0.66312, 0.51579, 0.68832, 0.51159, 0.71313, 0.52419, 0.72816, 0.55709], "triangles": [69, 70, 72, 69, 115, 68, 72, 70, 71, 125, 116, 117, 124, 115, 116, 69, 119, 115, 72, 119, 69, 115, 119, 116, 123, 119, 73, 123, 120, 119, 120, 123, 121, 123, 122, 121, 119, 72, 73, 116, 118, 117, 116, 119, 118, 117, 118, 125, 73, 74, 123, 118, 119, 128, 120, 142, 119, 123, 136, 122, 120, 121, 141, 122, 133, 121, 76, 77, 75, 75, 77, 78, 75, 78, 135, 78, 79, 135, 75, 136, 74, 75, 135, 136, 79, 80, 135, 135, 83, 129, 83, 135, 80, 74, 136, 123, 135, 134, 136, 122, 134, 133, 122, 136, 134, 80, 81, 83, 81, 82, 83, 129, 130, 135, 135, 130, 134, 130, 131, 134, 134, 132, 133, 134, 131, 132, 133, 132, 140, 129, 83, 91, 129, 91, 130, 131, 130, 92, 138, 132, 131, 85, 90, 84, 83, 84, 91, 84, 90, 91, 90, 85, 87, 85, 86, 87, 90, 88, 89, 88, 90, 87, 130, 91, 92, 92, 93, 131, 99, 94, 95, 95, 98, 99, 95, 96, 98, 96, 97, 98, 93, 94, 138, 100, 94, 99, 10, 11, 9, 9, 11, 12, 7, 9, 12, 7, 12, 13, 13, 14, 7, 7, 14, 16, 17, 18, 4, 9, 7, 8, 14, 15, 16, 7, 16, 6, 16, 17, 6, 17, 5, 6, 17, 4, 5, 4, 110, 111, 4, 111, 112, 4, 112, 113, 1, 114, 0, 114, 2, 113, 3, 4, 113, 3, 113, 2, 2, 114, 1, 21, 22, 180, 180, 22, 23, 20, 21, 180, 149, 27, 152, 25, 26, 24, 149, 147, 27, 27, 147, 179, 179, 147, 107, 176, 179, 107, 176, 107, 108, 178, 179, 176, 176, 177, 178, 24, 26, 27, 27, 179, 180, 24, 27, 180, 180, 23, 24, 20, 180, 19, 180, 179, 19, 179, 178, 19, 19, 178, 18, 4, 18, 177, 4, 177, 110, 18, 178, 177, 176, 109, 177, 177, 109, 110, 176, 108, 109, 142, 143, 119, 144, 143, 145, 137, 145, 140, 138, 137, 139, 139, 137, 140, 145, 143, 140, 143, 141, 140, 145, 137, 103, 103, 137, 102, 145, 103, 104, 143, 142, 141, 142, 120, 141, 121, 133, 141, 141, 133, 140, 140, 132, 139, 139, 132, 138, 131, 93, 138, 138, 94, 100, 138, 100, 137, 137, 101, 102, 137, 100, 101, 145, 104, 105, 147, 148, 145, 148, 146, 145, 145, 106, 147, 145, 105, 106, 147, 106, 107, 36, 156, 35, 150, 151, 181, 33, 155, 32, 155, 30, 31, 30, 155, 154, 30, 154, 29, 181, 151, 152, 182, 152, 153, 183, 153, 154, 154, 28, 29, 154, 153, 28, 151, 149, 152, 153, 152, 27, 28, 153, 27, 155, 31, 32, 33, 34, 155, 34, 35, 185, 37, 162, 158, 184, 154, 155, 185, 184, 155, 184, 156, 183, 183, 182, 153, 182, 181, 152, 158, 162, 159, 37, 158, 157, 37, 157, 36, 157, 156, 36, 159, 161, 158, 158, 161, 182, 157, 158, 156, 156, 158, 182, 35, 156, 185, 156, 184, 185, 161, 160, 181, 34, 185, 155, 161, 181, 182, 160, 150, 181, 183, 156, 182, 184, 183, 154, 161, 169, 160, 168, 170, 169, 170, 61, 171, 170, 60, 61, 61, 62, 171, 161, 168, 169, 160, 175, 150, 175, 160, 169, 62, 63, 172, 124, 172, 67, 172, 124, 125, 62, 172, 171, 127, 172, 125, 175, 149, 150, 175, 144, 149, 149, 146, 148, 149, 144, 146, 170, 173, 169, 173, 174, 169, 169, 174, 175, 170, 171, 173, 125, 126, 127, 67, 172, 63, 124, 67, 68, 63, 64, 66, 67, 63, 66, 171, 172, 173, 64, 65, 66, 172, 127, 173, 174, 173, 144, 175, 174, 144, 150, 149, 151, 127, 144, 173, 124, 68, 115, 125, 124, 116, 125, 118, 126, 126, 118, 127, 118, 128, 127, 127, 128, 144, 119, 143, 128, 128, 143, 144, 149, 148, 147, 144, 145, 146, 168, 161, 159, 162, 37, 38, 165, 54, 166, 43, 165, 164, 43, 164, 42, 55, 56, 166, 41, 42, 163, 56, 167, 166, 168, 167, 170, 167, 60, 170, 57, 167, 56, 42, 164, 163, 41, 163, 40, 40, 163, 162, 60, 167, 57, 60, 57, 58, 165, 166, 164, 58, 59, 60, 163, 164, 167, 164, 166, 167, 163, 167, 162, 167, 168, 162, 40, 162, 38, 162, 168, 159, 39, 40, 38, 49, 50, 53, 48, 49, 53, 51, 53, 50, 48, 44, 47, 53, 51, 52, 48, 53, 165, 165, 53, 54, 45, 46, 47, 44, 45, 47, 48, 165, 44, 44, 165, 43, 54, 55, 166], "vertices": [5, 16, 191.78, 22.53, 0.00735, 27, 141.22, 10.28, 0.01557, 28, 107.45, 15.93, 0.07604, 29, 47.65, 61.19, 0.82652, 30, -59.79, 23.71, 0.07452, 5, 16, 195.01, 15.79, 0.0049, 27, 141.22, 2.81, 0.01125, 28, 107.71, 8.46, 0.06116, 29, 52.57, 55.56, 0.82747, 30, -53.85, 28.25, 0.09522, 5, 16, 196.12, 2.52, 0.0016, 27, 136.48, -9.64, 0.00477, 28, 103.41, -4.14, 0.03429, 29, 57.19, 43.07, 0.79666, 30, -41.09, 32.05, 0.16269, 4, 27, 129.12, -22.78, 0.00063, 28, 96.5, -17.54, 0.01052, 29, 60.28, 28.33, 0.69622, 30, -26.17, 34.19, 0.29263, 2, 29, 58.78, 2.97, 0.31639, 30, -0.96, 31.05, 0.68361, 2, 30, 33.23, 25.03, 0.8391, 31, -4.23, 26.76, 0.1609, 2, 30, 60.32, 17.28, 0.04431, 31, 20.3, 12.89, 0.95569, 1, 31, 30.77, 10.73, 1, 1, 31, 48.32, 14.78, 1, 1, 31, 53.65, 9.92, 1, 1, 31, 61.22, -2.5, 1, 1, 31, 58.89, -6.91, 1, 2, 28, -3.24, -100.38, 7e-05, 31, 54.23, -12.09, 0.99993, 3, 27, 25.35, -91.02, 0.00097, 28, -4.82, -89.35, 0.00348, 31, 44.58, -17.67, 0.99555, 5, 27, 26.34, -82.43, 0.00455, 26, 105.6, -20.09, 0.00012, 28, -4.13, -80.73, 0.01375, 29, 22.1, -84.2, 0.00091, 31, 36.33, -20.24, 0.98067, 7, 13, 105.76, -160.78, 6e-05, 27, 30.35, -71.64, 0.02076, 26, 98.45, -11.09, 0.00273, 28, -0.5, -69.81, 0.0557, 29, 18.02, -73.44, 0.00755, 30, 72.65, -14.56, 0.00058, 31, 24.84, -20.95, 0.91264, 7, 13, 108.58, -160.91, 0.00012, 27, 32.89, -70.41, 0.02913, 26, 98.71, -8.27, 0.00444, 28, 2, -68.49, 0.07647, 29, 19.13, -70.84, 0.01125, 30, 70.13, -13.29, 0.00351, 31, 22.68, -19.13, 0.87508, 7, 13, 109.22, -152.12, 0.00068, 27, 29.26, -62.38, 0.08207, 26, 89.96, -7.23, 0.01719, 28, -1.92, -60.59, 0.19531, 29, 11.11, -67.18, 0.03129, 30, 65.96, -21.05, 0.04085, 31, 16.82, -25.7, 0.63261, 7, 13, 102.77, -144.87, 0.00323, 27, 20.13, -59.09, 0.17015, 26, 82.41, -13.34, 0.05638, 28, -11.15, -57.62, 0.28854, 29, 2.07, -70.71, 0.03476, 30, 68.89, -30.31, 0.03615, 31, 17.5, -35.38, 0.41079, 7, 13, 88.36, -137.45, 0.01309, 27, 3.92, -59.45, 0.26913, 26, 74.34, -27.39, 0.16978, 28, -27.34, -58.54, 0.28478, 29, -9.9, -81.64, 0.01976, 30, 79.03, -42.96, 0.0096, 31, 24.4, -50.05, 0.23386, 6, 13, 55.71, -112.3, 0.08237, 27, -36.77, -52.93, 0.19332, 26, 47.71, -58.85, 0.55448, 28, -68.23, -53.45, 0.1081, 29, -44.84, -103.49, 0.00046, 31, 34.93, -89.89, 0.06126, 6, 13, 43.8, -98.61, 0.12566, 16, 40.56, -113.08, 0.00212, 27, -53.78, -46.59, 0.13394, 26, 33.49, -70.12, 0.63178, 28, -85.45, -47.7, 0.06867, 31, 36.02, -108.01, 0.03783, 7, 14, 71.46, -95.63, 9e-05, 13, 42.05, -93.6, 0.13558, 16, 35.48, -111.57, 0.0036, 27, -57.7, -43.02, 0.12374, 26, 28.41, -71.64, 0.63964, 28, -89.5, -44.28, 0.06283, 31, 34.35, -113.05, 0.03452, 7, 14, 72.65, -79.11, 0.00217, 13, 43.23, -77.09, 0.1949, 16, 22.79, -100.93, 0.01805, 27, -64.55, -27.95, 0.08012, 26, 11.96, -69.7, 0.64269, 28, -96.86, -29.45, 0.04008, 31, 23.34, -125.41, 0.022, 1, 16, 18.7, -89.24, 1, 1, 16, 12.28, -96.91, 1, 1, 16, 9.93, -96.76, 1, 1, 16, 4.56, -92.24, 1, 4, 10, 60.04, -138.2, 0.00026, 13, 33.64, -56.74, 0.90426, 16, 0.68, -96.79, 0.09331, 31, 18.54, -147.38, 0.00217, 4, 14, 50.28, -69.88, 0.064, 13, 20.87, -67.86, 0.89999, 16, 2.2, -113.65, 0.03582, 31, 35.43, -146.37, 0.00019, 4, 14, 39.27, -77.02, 0.10374, 13, 9.85, -74.99, 0.87917, 16, 1.53, -126.76, 0.01709, 31, 48.51, -147.43, 1e-05, 4, 12, -119.49, 112.5, 0.00086, 14, 27.36, -82.75, 0.17711, 13, -2.06, -80.72, 0.81542, 16, -0.8, -139.76, 0.00662, 4, 12, -109.31, 119.55, 0.00347, 14, 15.34, -85.75, 0.23416, 13, -14.07, -83.72, 0.76034, 16, -5.41, -151.26, 0.00203, 4, 12, -96.77, 123.1, 0.01041, 14, 2.36, -84.65, 0.19125, 13, -27.06, -82.62, 0.79809, 16, -13.9, -161.14, 0.00025, 4, 34, -132.94, 166.97, 7e-05, 12, -87.04, 121.16, 0.0213, 14, -6.06, -79.41, 0.1774, 13, -35.47, -77.38, 0.80123, 4, 34, -122.81, 157.93, 0.0011, 12, -74.2, 116.77, 0.05644, 14, -16.53, -70.78, 0.17872, 13, -45.94, -68.75, 0.76374, 5, 34, -106.95, 138.26, 0.02001, 12, -51.94, 104.81, 0.32033, 11, 28.98, 116.97, 0.01287, 14, -33.14, -51.73, 0.19274, 13, -62.56, -49.7, 0.45404, 5, 34, -81.82, 128.8, 0.07653, 12, -25.12, 105.86, 0.68849, 11, 53.42, 105.86, 0.04785, 14, -58.62, -43.26, 0.13943, 13, -88.03, -41.23, 0.0477, 4, 34, -40.96, 115.95, 0.16822, 12, 17.53, 109.9, 0.7669, 11, 93.33, 90.3, 0.01687, 14, -99.95, -32.01, 0.048, 3, 34, -27.98, 102.5, 0.22192, 12, 34.71, 102.55, 0.76931, 11, 105.38, 76.01, 0.00877, 3, 34, -25.71, 92.48, 0.26132, 12, 40.71, 94.2, 0.73296, 11, 106.98, 65.86, 0.00573, 3, 34, -17.97, 79.45, 0.36478, 12, 52.9, 85.21, 0.63398, 11, 113.83, 52.34, 0.00124, 3, 34, -11.75, 70.71, 0.47131, 12, 62.02, 79.57, 0.52867, 11, 119.44, 43.21, 3e-05, 2, 34, 1.01, 61.6, 0.66437, 12, 77.32, 76.14, 0.33563, 3, 34, 26.72, 52.14, 0.88233, 12, 104.69, 77.41, 0.08363, 14, -170.06, 29.13, 0.03404, 3, 34, 41.4, 47.91, 0.95518, 12, 119.86, 79.22, 0.02975, 14, -184.89, 32.78, 0.01508, 3, 34, 47.28, 44.09, 0.9715, 12, 126.76, 77.98, 0.01785, 14, -190.92, 36.38, 0.01065, 3, 34, 51.3, 38.56, 0.98139, 12, 132.61, 74.45, 0.01082, 14, -195.14, 41.74, 0.00779, 2, 34, 56.47, 16.67, 0.99905, 14, -201.16, 63.42, 0.00095, 1, 34, 58.24, -13.99, 1, 3, 34, 56.09, -33.88, 0.9996, 12, 165.18, 9.57, 0.00018, 10, -205.76, 34.52, 0.00021, 3, 34, 51.89, -43.24, 0.99561, 12, 164.95, -0.69, 0.00345, 10, -201.92, 44.04, 0.00094, 3, 34, 47.45, -48.53, 0.99014, 12, 162.91, -7.29, 0.00807, 10, -197.69, 49.49, 0.00179, 3, 34, 42.16, -52.06, 0.98166, 12, 159.41, -12.6, 0.01529, 10, -192.55, 53.23, 0.00305, 3, 34, 12.99, -63.75, 0.84418, 12, 137.08, -34.71, 0.13217, 10, -163.85, 66.05, 0.02365, 3, 34, -23.35, -75.35, 0.44283, 12, 108.1, -59.52, 0.43855, 10, -127.99, 79.05, 0.11862, 3, 34, -28.62, -80.5, 0.35511, 12, 105.24, -66.31, 0.49089, 10, -122.92, 84.4, 0.154, 3, 34, -36.7, -93.06, 0.24155, 12, 102.68, -81.03, 0.54309, 10, -115.33, 97.26, 0.21537, 3, 34, -50.96, -111.89, 0.14977, 12, 96.86, -103.92, 0.56059, 10, -101.81, 116.63, 0.28963, 3, 34, -57.08, -116.87, 0.13383, 12, 93.16, -110.88, 0.55914, 10, -95.9, 121.84, 0.30704, 3, 34, -71.53, -120.93, 0.10412, 12, 81.42, -120.23, 0.54808, 10, -81.62, 126.46, 0.3478, 4, 34, -119.2, -126.69, 0.02501, 12, 39.74, -144.07, 0.4257, 10, -34.2, 134.07, 0.54832, 15, -55.77, 186.18, 0.00097, 4, 34, -126.52, -130.34, 0.01585, 12, 34.41, -150.28, 0.39337, 10, -27.03, 138, 0.58917, 15, -48.28, 182.9, 0.00161, 4, 34, -144.42, -148.28, 0.00305, 12, 24.89, -173.76, 0.3345, 10, -9.84, 156.62, 0.66218, 15, -23.04, 180.66, 0.00027, 3, 34, -154.13, -155.81, 0.00093, 12, 18.87, -184.48, 0.31835, 10, -0.43, 164.53, 0.68072, 4, 34, -164.25, -158.95, 0.00018, 12, 10.77, -191.3, 0.30736, 10, 9.56, 168.05, 0.69134, 15, -2.14, 172.28, 0.00113, 3, 12, 0.51, -194.53, 0.29436, 10, 20.29, 167.45, 0.69976, 15, 3.94, 163.41, 0.00588, 3, 12, -13.65, -195.87, 0.27043, 10, 34.02, 163.72, 0.7068, 15, 9.37, 150.26, 0.02277, 1, 10, 51.85, 153.2, 1, 1, 33, 39.64, -0.89, 1, 1, 33, 37.33, -14.86, 1, 1, 33, 36.21, -17.52, 1, 1, 33, 31.05, -20.87, 1, 1, 33, 13.17, -17.93, 1, 1, 32, 17.79, 47.46, 1, 1, 32, 27.57, 49.89, 1, 1, 32, 42.37, 51.19, 1, 1, 32, 45.63, 49.13, 1, 1, 32, 59.02, 25.69, 1, 1, 32, 61.19, 12.23, 1, 1, 32, 57.09, -6.4, 1, 1, 32, 53.72, -15, 1, 1, 32, 47.46, -23.64, 1, 1, 32, 32.91, -25, 1, 1, 25, 48.88, 15.77, 1, 1, 25, 61.54, 0.3, 1, 1, 25, 62.85, -2.78, 1, 1, 25, 59.11, -11.07, 1, 1, 25, 52.76, -21.06, 1, 1, 25, 47.64, -22.17, 1, 2, 24, 82.89, -27.79, 0.00062, 25, 38.51, -14.55, 0.99938, 2, 24, 74.31, -24.42, 0.02095, 25, 29.3, -14.41, 0.97905, 3, 23, 72.52, -11.85, 0.06433, 24, 22.61, -36.79, 0.86942, 25, -14.78, -44.13, 0.06625, 3, 23, 50.9, -32.03, 0.58959, 24, -6.9, -38.79, 0.41003, 25, -41.71, -56.36, 0.00038, 3, 22, 88.3, 8.17, 0.03764, 23, 35.55, -44.59, 0.87087, 24, -26.73, -38.85, 0.09149, 4, 15, 181.97, -19.08, 0.00222, 22, 88.71, -18.47, 0.19697, 23, 12.77, -58.4, 0.80016, 24, -53.12, -35.18, 0.00065, 3, 15, 176.07, -33.58, 0.00846, 22, 86.05, -33.91, 0.29914, 23, -1.9, -63.9, 0.6924, 3, 15, 168.38, -42.88, 0.01509, 22, 80.52, -44.63, 0.36295, 23, -13.95, -64.55, 0.62196, 3, 15, 151.14, -49.86, 0.03535, 22, 65.18, -55.14, 0.47851, 23, -30.76, -56.6, 0.48614, 3, 15, 127.65, -50.45, 0.09129, 22, 42.36, -60.74, 0.63068, 23, -47.12, -39.74, 0.27803, 4, 17, 108.3, 138.27, 1e-05, 15, 101.53, -44.76, 0.2376, 22, 15.63, -60.77, 0.68175, 23, -60.65, -16.68, 0.08065, 5, 17, 85.41, 120.86, 0.00852, 15, 74.35, -35.37, 0.58502, 21, 50.08, -56.6, 0.01155, 22, -12.94, -57.41, 0.39097, 23, -72.17, 9.67, 0.00393, 1, 15, 65.7, -31.89, 1, 3, 17, 85.79, 90.96, 0.16535, 15, 49.68, -52.27, 0.81, 22, -33.42, -79.2, 0.02465, 3, 17, 90.95, 64.76, 0.36404, 15, 30.74, -71.09, 0.63535, 22, -47.89, -101.64, 0.0006, 3, 17, 93.51, 42.38, 0.55288, 15, 13.54, -85.64, 0.44515, 16, -34.25, 130.01, 0.00197, 3, 17, 91.22, -14.06, 0.73585, 15, -34.7, -115.04, 0.05028, 16, 7.64, 92.11, 0.21387, 3, 17, 85.57, -48.51, 0.43609, 15, -66.49, -129.45, 0.00084, 16, 30.49, 65.72, 0.56307, 1, 16, 54, 35.42, 1, 6, 17, 102.3, -107.19, 0.00107, 16, 86.28, 41, 0.27594, 27, 54.09, 72.56, 0.20077, 26, -12.68, 83.83, 0.328, 28, 18.2, 75.14, 0.16617, 29, -58.94, 50.8, 0.02805, 4, 16, 119.44, 43.96, 0.17867, 27, 85.27, 60.89, 0.22273, 28, 49.76, 64.56, 0.36831, 29, -27.78, 62.51, 0.23029, 4, 16, 143.07, 42.11, 0.08732, 27, 105.77, 49, 0.13008, 28, 70.67, 53.39, 0.31329, 29, -4.52, 67.04, 0.46931, 5, 16, 158.88, 39.79, 0.04898, 27, 119.02, 40.07, 0.07952, 28, 84.22, 44.93, 0.2349, 29, 11.34, 69.03, 0.63145, 30, -69.95, -12.03, 0.00514, 5, 16, 174.21, 34.14, 0.02384, 27, 130.4, 28.35, 0.04224, 28, 96, 33.61, 0.15185, 29, 27.62, 67.68, 0.75666, 30, -67.56, 4.13, 0.02541, 5, 16, 183.78, 29.21, 0.0135, 27, 136.9, 19.76, 0.02574, 28, 102.79, 25.26, 0.10663, 29, 38.16, 65.49, 0.80481, 30, -64.69, 14.51, 0.04932, 1, 33, 37.25, 13.9, 1, 1, 33, 33.59, 18.02, 1, 1, 33, 30.74, 21, 1, 1, 33, 19.73, 22.17, 1, 1, 33, 14.28, 20.57, 1, 1, 33, -7.72, 16.04, 1, 1, 33, -14.2, 2.23, 1, 1, 33, -14.48, -8.43, 1, 1, 33, 1.72, -14.64, 1, 1, 10, 54.09, 149.67, 1, 1, 10, 63.06, 141.62, 1, 1, 10, 68.35, 138.51, 1, 1, 10, 71.79, 135.18, 1, 1, 10, 79.55, 138.02, 1, 1, 32, 20.56, -24.91, 1, 1, 32, 2.13, -20.02, 1, 1, 32, -11.36, -14.03, 1, 1, 32, -18.2, -8.37, 1, 1, 32, -9.73, 20.65, 1, 1, 32, 3.5, 15.74, 1, 1, 32, 13.74, 14.18, 1, 1, 32, 13.67, 40.19, 1, 1, 15, 65.91, -16.31, 1, 1, 15, 65.27, -1.39, 1, 1, 15, 60.22, 20.82, 1, 1, 15, 47.66, 51.52, 1, 1, 15, 43.26, 71.38, 1, 1, 15, 32.82, 92.27, 1, 1, 15, 28.71, 93.78, 1, 4, 12, -112.04, -72.17, 1e-05, 10, 82.48, 13.27, 0.80669, 17, -46.39, 23.01, 0.1112, 15, -80.18, 20.02, 0.0821, 4, 10, 115.03, 10.45, 0.25608, 13, 88.63, 91.9, 0.00022, 17, -13.73, 22.43, 0.58103, 15, -62.55, -7.48, 0.16267, 5, 10, 97.98, 8.13, 0.53855, 13, 71.58, 89.58, 0.00237, 17, -30.58, 18.95, 0.35554, 15, -74.79, 4.6, 0.10209, 16, -95.68, 19.68, 0.00146, 4, 10, 113.35, -30.79, 0.14198, 13, 86.95, 50.67, 0.06364, 17, -12.57, -18.82, 0.62425, 16, -55.14, 9.34, 0.17013, 5, 10, 94.78, -21.35, 0.43826, 13, 68.38, 60.1, 0.08298, 17, -31.74, -10.69, 0.39593, 15, -100.09, -10.87, 0.00177, 16, -73.66, -0.18, 0.08106, 4, 10, 77.31, -21.31, 0.66847, 13, 50.91, 60.14, 0.12124, 17, -49.17, -11.85, 0.15107, 16, -83.93, -14.32, 0.05923, 5, 11, -45.71, 41.52, 0.07612, 10, 30.14, -48.24, 0.3813, 13, 3.74, 33.21, 0.51888, 17, -94.37, -41.96, 0.00536, 16, -89.74, -68.32, 0.01834, 5, 11, -55.6, 51.36, 0.02347, 10, 41.02, -56.98, 0.27216, 13, 14.62, 24.48, 0.64653, 17, -82.92, -49.92, 0.01466, 16, -76.29, -64.62, 0.04317, 5, 11, -59.35, 70.23, 0.00232, 10, 46.74, -75.34, 0.1019, 13, 20.34, 6.11, 0.81368, 17, -75.95, -67.85, 0.01317, 16, -58.05, -70.74, 0.06893, 4, 10, 46.76, -96.81, 0.01894, 13, 20.36, -15.35, 0.90269, 17, -74.45, -89.27, 0.00396, 16, -40.64, -83.3, 0.07442, 3, 14, 37.51, -36.01, 0.184, 13, 8.09, -33.98, 0.78682, 16, -32.74, -104.15, 0.02918, 4, 12, -92.29, 77.49, 0.00271, 14, 14.24, -40.39, 0.744, 13, -15.17, -38.36, 0.25183, 16, -42.81, -125.58, 0.00147, 2, 14, -6.85, -34.94, 0.752, 13, -36.26, -32.92, 0.248, 2, 14, -19.81, -27.56, 0.864, 13, -49.23, -25.53, 0.136, 1, 14, -24.9, -7.24, 1, 6, 34, -119.36, 66.84, 0.00381, 12, -35.62, 34.18, 0.14589, 11, 11.82, 46.54, 0.46177, 10, -26.53, -59.31, 0.00556, 14, -23.51, 20.12, 0.38158, 13, -52.93, 22.15, 0.00139, 5, 11, -25.86, 45.1, 0.15808, 10, 10.78, -53.89, 0.17139, 14, 13.8, 25.53, 0.37601, 13, -15.62, 27.56, 0.2936, 16, -96.5, -87.32, 0.00092, 5, 12, -56.08, 24.4, 0.01335, 11, -10.86, 47, 0.35755, 10, -3.93, -57.37, 0.10561, 14, -0.92, 22.05, 0.37307, 13, -30.33, 24.08, 0.15043, 4, 34, -93.21, 58.5, 0.02094, 12, -8.29, 36.66, 0.43777, 11, 37.35, 36.47, 0.30129, 14, -49.97, 27.43, 0.24, 4, 34, -68.7, 44.33, 0.06043, 12, 19.81, 33.13, 0.69007, 11, 60.86, 20.69, 0.0415, 14, -75.01, 40.64, 0.208, 3, 34, -46.47, 22.89, 0.18531, 12, 48.62, 22.02, 0.73469, 14, -98.06, 61.2, 0.08, 4, 34, -40.89, 14.27, 0.26689, 12, 57.11, 16.24, 0.69292, 10, -106.98, -9.83, 0.00019, 14, -103.97, 69.59, 0.04, 4, 34, -52.56, -19.53, 0.18433, 12, 59.49, -19.44, 0.76375, 11, 72.69, -44.1, 0.00122, 10, -96.63, 24.4, 0.0507, 4, 34, -82.2, -20.33, 0.02361, 12, 32.5, -31.69, 0.7729, 11, 43.06, -42.92, 0.05639, 10, -67.05, 26.35, 0.14711, 5, 34, -113.15, -24, 0.00191, 12, 5.4, -47.1, 0.40181, 11, 11.93, -44.5, 0.16806, 10, -36.26, 31.22, 0.42725, 15, -138.48, 125.01, 0.00096, 5, 34, -127.27, -29.48, 0.00066, 12, -5.47, -57.64, 0.27249, 11, -2.52, -49.03, 0.0984, 10, -22.37, 37.25, 0.62448, 15, -125.22, 117.69, 0.00397, 5, 34, -134.39, -58.27, 0.00474, 12, -0.85, -86.93, 0.29968, 11, -11.55, -77.28, 0.01595, 10, -16.37, 66.29, 0.66628, 15, -98.56, 130.67, 0.01335, 5, 34, -146.85, -66.94, 0.00244, 12, -8.96, -99.76, 0.24801, 11, -24.56, -85.09, 0.0037, 10, -4.26, 75.44, 0.71897, 15, -83.92, 126.67, 0.02688, 5, 34, -167.36, -66.72, 8e-05, 12, -27.94, -107.54, 0.15366, 10, 16.25, 76.02, 0.7821, 15, -70.94, 110.78, 0.06394, 21, -155.43, -41.38, 0.00022, 4, 12, -47.07, -99.24, 0.07541, 10, 31.22, 61.51, 0.83345, 15, -73.29, 90.06, 0.09034, 21, -143.63, -58.57, 0.0008, 4, 12, -57.23, -86.17, 0.03979, 10, 36.12, 45.69, 0.88487, 15, -82.82, 76.53, 0.07501, 21, -141.96, -75.04, 0.00033, 4, 12, -62.52, -73.72, 0.02114, 10, 36.69, 32.18, 0.93169, 15, -93.18, 67.83, 0.04713, 21, -144.09, -88.4, 4e-05, 1, 16, 55.51, 8.52, 1, 1, 16, 52.78, -12.66, 1, 1, 16, 42.86, -35.66, 1, 1, 16, 33.49, -52.02, 1, 1, 16, 29.46, -69.24, 1, 1, 14, 23.72, 12.21, 1, 1, 14, 27.62, -1.43, 1, 1, 14, 28.67, -16.26, 1, 1, 14, 21.48, -30.29, 1, 2, 14, 4.7, -37.92, 0.792, 13, -24.72, -35.9, 0.208], "hull": 115}}, "9_4": {"9_4": {"type": "mesh", "uvs": [0.24067, 0.01541, 0.48109, 0.08613, 0.79782, 0.24209, 0.97311, 0.45118, 0.97499, 0.62033, 0.75899, 0.68537, 0.78363, 0.78823, 0.86549, 0.78973, 0.86563, 0.91378, 0.66433, 0.98417, 0.53994, 0.98406, 0.32359, 0.91938, 0.32339, 0.87062, 0.48178, 0.87019, 0.78259, 0.7904, 0.75561, 0.6784, 0.56927, 0.71006, 0.10718, 0.70994, 0.02618, 0.66159, 0.02626, 0.533, 0.18872, 0.28066, 0.18773, 0.21682, 0.08079, 0.12101, 0.07992, 0.01554], "triangles": [22, 23, 0, 21, 22, 0, 21, 0, 1, 20, 21, 1, 20, 1, 2, 2, 16, 20, 3, 16, 2, 15, 3, 4, 16, 19, 20, 17, 18, 19, 5, 15, 4, 19, 16, 17, 15, 16, 3, 14, 5, 6, 15, 5, 14, 11, 12, 13, 9, 10, 13, 11, 13, 10, 14, 6, 7, 14, 7, 8, 14, 9, 13, 9, 14, 8], "vertices": [-22.39, -5.38, -17.07, 2.99, -6.2, 13.61, 7.39, 18.66, 17.82, 17.6, 20.97, 9.22, 27.41, 9.44, 27.83, 12.45, 35.48, 11.62, 39.01, 3.75, 38.51, -0.82, 33.67, -8.35, 30.66, -8.03, 31.26, -2.21, 27.54, 9.39, 20.53, 9.15, 21.74, 2.08, 19.89, -14.92, 16.59, -17.57, 8.67, -16.72, -6.24, -9.06, -10.18, -8.67, -16.51, -11.96, -23.02, -11.29], "hull": 24}}, "9_5": {"9_5": {"type": "mesh", "uvs": [0.19729, 0.00674, 0.24359, 0.06963, 0.26491, 0.13153, 0.33422, 0.26716, 0.37697, 0.23583, 0.53745, 0.17549, 0.67311, 0.16885, 0.74853, 0.16876, 0.82872, 0.19517, 0.88991, 0.32082, 0.9941, 0.39204, 0.99371, 0.57203, 0.94649, 0.73, 0.83363, 0.86492, 0.79499, 0.86541, 0.74653, 0.91687, 0.67298, 0.96733, 0.57326, 0.9934, 0.51286, 0.99337, 0.38196, 0.9208, 0.26751, 0.82676, 0.23335, 0.77915, 0.19058, 0.70248, 0.07842, 0.4188, 0.01618, 0.21446, 0.00649, 0.18919, 0.00644, 0.14756, 0.0231, 0.09779, 0.09308, 0.02584, 0.13251, 0.00647], "triangles": [24, 25, 26, 27, 24, 26, 27, 1, 24, 23, 24, 3, 1, 28, 29, 1, 29, 0, 28, 1, 27, 2, 24, 1, 3, 22, 23, 2, 3, 24, 11, 9, 10, 12, 9, 11, 21, 22, 3, 20, 21, 3, 8, 9, 7, 12, 14, 9, 7, 9, 6, 12, 13, 14, 6, 14, 5, 9, 14, 6, 4, 14, 3, 14, 20, 3, 19, 20, 14, 5, 14, 4, 15, 19, 14, 16, 19, 15, 17, 18, 19, 16, 17, 19], "vertices": [1, 9, 46.86, -16.39, 1, 2, 8, 136.8, 4.74, 0.0004, 9, 35.55, -19.27, 0.9996, 2, 8, 127.17, 6.02, 0.01421, 9, 25.85, -18.71, 0.98579, 2, 8, 104.5, 5.76, 0.50224, 9, 3.26, -20.65, 0.49776, 2, 8, 105.67, -2.13, 0.83544, 9, 5.02, -28.44, 0.16456, 1, 8, 102.54, -27.92, 1, 1, 8, 94.03, -46.73, 1, 1, 8, 88.83, -56.95, 1, 1, 8, 79.81, -66.02, 1, 1, 8, 59.01, -65.85, 1, 1, 8, 42.42, -75.16, 1, 1, 8, 18.73, -62.99, 1, 1, 8, 1.17, -45.97, 1, 1, 8, -8.81, -21.61, 1, 1, 8, -6.21, -16.35, 1, 1, 8, -9.64, -6.32, 1, 2, 8, -11.21, 7.03, 0.99997, 9, -112.22, -28, 3e-05, 2, 8, -7.75, 22.29, 0.99704, 9, -109.91, -12.53, 0.00296, 2, 8, -3.57, 30.46, 0.99214, 9, -106.35, -4.07, 0.00786, 2, 8, 15.04, 43.3, 0.95085, 9, -88.74, 10.12, 0.04916, 2, 8, 35.35, 52.46, 0.84696, 9, -69.17, 20.77, 0.15304, 2, 8, 43.99, 53.88, 0.78754, 9, -60.66, 22.83, 0.21246, 2, 8, 57.05, 54.51, 0.66207, 9, -47.68, 24.43, 0.33793, 2, 8, 102.19, 50.6, 0.02564, 9, -2.37, 23.89, 0.97436, 1, 9, 29.17, 20.9, 1, 1, 9, 33.19, 20.81, 1, 1, 9, 38.87, 18.43, 1, 1, 9, 44.68, 13.24, 1, 1, 9, 50.38, -0.69, 1, 1, 9, 50.71, -7.32, 1], "hull": 30}}, "9_6": {"9_6": {"type": "mesh", "uvs": [0.22853, 0.01447, 0.31624, 0.01475, 0.31645, 0.07259, 0.40415, 0.38921, 0.46389, 0.54166, 0.54422, 0.68623, 0.70182, 0.79085, 0.96536, 0.86535, 0.96535, 0.90245, 0.79004, 0.98701, 0.75412, 0.9854, 0.54392, 0.86615, 0.20652, 0.57891, 0.08875, 0.40677, 0.00649, 0.12763, 0.04985, 0.09032], "triangles": [2, 13, 15, 14, 15, 13, 0, 2, 15, 2, 0, 1, 3, 12, 13, 3, 13, 2, 12, 3, 4, 5, 12, 4, 11, 5, 6, 11, 12, 5, 6, 9, 10, 6, 7, 9, 11, 6, 10, 8, 9, 7], "vertices": [-27.82, 16.19, -24.74, 20.13, -21.67, 17.76, -1.85, 8.68, 8.3, 5.1, 18.76, 2.76, 29.8, 5.55, 42.97, 14.35, 44.93, 12.82, 43.26, 1.45, 41.92, -0.1, 28.26, -4.65, 1.26, -8.02, -11.96, -6.23, -29.6, 1.55, -30.06, 5.03], "hull": 16}}, "9_7": {"9_7": {"type": "mesh", "uvs": [0.74355, 0.00788, 0.83959, 0.00736, 0.92036, 0.08808, 0.98659, 0.24049, 0.98748, 0.4971, 0.92678, 0.64701, 0.81049, 0.78724, 0.67201, 0.8713, 0.35372, 0.94891, 0.13343, 0.99199, 0.06123, 0.99243, 0.01317, 0.93431, 0.01335, 0.89999, 0.04033, 0.87032, 0.27409, 0.80825, 0.39872, 0.76623, 0.48656, 0.71267, 0.53939, 0.64783, 0.49933, 0.51575, 0.44609, 0.41871, 0.34841, 0.28565, 0.35135, 0.2411, 0.5249, 0.10737], "triangles": [22, 20, 21, 19, 20, 22, 0, 1, 2, 22, 0, 2, 22, 2, 3, 19, 4, 18, 3, 19, 22, 4, 19, 3, 5, 18, 4, 17, 18, 5, 6, 17, 5, 16, 17, 6, 7, 16, 6, 15, 16, 7, 8, 14, 15, 8, 15, 7, 9, 13, 14, 8, 9, 14, 11, 12, 13, 9, 11, 13, 10, 11, 9], "vertices": [91.94, 26, 94.79, 19.5, 88.1, 10.12, 72.94, -1.71, 44.19, -14.1, 25.61, -17.17, 6.49, -16, -6.97, -10.63, -24.95, 7.29, -36.2, 20.21, -38.36, 25.1, -33.24, 31.16, -29.39, 32.8, -25.27, 32.39, -11.5, 19.47, -3.15, 13.01, 5.41, 9.61, 14.22, 9.14, 27.87, 18.21, 37.2, 26.49, 49.27, 39.53, 54.35, 41.47, 74.41, 36.09], "hull": 23}}, "biaoqing_frame": {"biaoqing_frame": {"y": 186.5, "width": 400, "height": 400}}}}], "animations": {"start": {"bones": {"9_5": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 20.46, "curve": "stepped"}, {"time": 2.6667, "angle": 20.46}, {"time": 3}]}, "9_6": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.36, "curve": "stepped"}, {"time": 2.6667, "angle": -13.36}, {"time": 3}]}, "9_7": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.31, "curve": "stepped"}, {"time": 2.6667, "angle": -3.31}, {"time": 3}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 2.94, "y": 8.97, "curve": "stepped"}, {"time": 0.6667, "x": 2.94, "y": 8.97}, {"time": 0.7, "x": 4.03, "y": 8.08}, {"time": 0.7333, "x": 4.62, "y": 6.65}, {"time": 0.7667, "x": 4.75, "y": 5.13}, {"time": 0.8, "x": 4.19, "y": 3.77}, {"time": 0.8333, "x": 3.18, "y": 2.69}, {"time": 0.8667, "x": 1.77, "y": 2.06}, {"time": 0.9, "x": 0.28, "y": 2.07}, {"time": 0.9333, "x": -1.05, "y": 2.54}, {"time": 0.9667, "x": -2.19, "y": 3.57}, {"time": 1, "x": -2.82, "y": 4.93}, {"time": 1.0333, "x": -2.87, "y": 6.42}, {"time": 1.0667, "x": -2.29, "y": 7.81}, {"time": 1.1, "x": -1.26, "y": 8.93}, {"time": 1.1333, "x": 0.11, "y": 9.44}, {"time": 1.1667, "x": 1.57, "y": 9.5}, {"time": 1.2, "x": 2.94, "y": 8.97}, {"time": 1.2333, "x": 4.03, "y": 8.08}, {"time": 1.2667, "x": 4.62, "y": 6.65}, {"time": 1.3, "x": 4.75, "y": 5.13}, {"time": 1.3333, "x": 4.19, "y": 3.77}, {"time": 1.3667, "x": 3.18, "y": 2.69}, {"time": 1.4, "x": 1.77, "y": 2.06}, {"time": 1.4333, "x": 0.28, "y": 2.07}, {"time": 1.4667, "x": -1.05, "y": 2.54}, {"time": 1.5, "x": -2.19, "y": 3.57}, {"time": 1.5333, "x": -2.82, "y": 4.93}, {"time": 1.5667, "x": -2.87, "y": 6.42}, {"time": 1.6, "x": -2.29, "y": 7.81}, {"time": 1.6333, "x": -1.26, "y": 8.93}, {"time": 1.6667, "x": 0.11, "y": 9.44}, {"time": 1.7, "x": 1.57, "y": 9.5}, {"time": 1.7333, "x": 2.94, "y": 8.97}, {"time": 1.7667, "x": 4.03, "y": 8.08}, {"time": 1.8, "x": 4.62, "y": 6.65}, {"time": 1.8333, "x": 4.75, "y": 5.13}, {"time": 1.8667, "x": 4.19, "y": 3.77}, {"time": 1.9, "x": 3.18, "y": 2.69}, {"time": 1.9333, "x": 1.77, "y": 2.06}, {"time": 1.9667, "x": 0.28, "y": 2.07}, {"time": 2, "x": -1.05, "y": 2.54}, {"time": 2.0333, "x": -2.19, "y": 3.57}, {"time": 2.0667, "x": -2.82, "y": 4.93}, {"time": 2.1, "x": -2.87, "y": 6.42}, {"time": 2.1333, "x": -2.29, "y": 7.81}, {"time": 2.1667, "x": -1.26, "y": 8.93}, {"time": 2.2, "x": 0.11, "y": 9.44}, {"time": 2.2333, "x": 1.57, "y": 9.5}, {"time": 2.2667, "x": 2.94, "y": 8.97, "curve": "stepped"}, {"time": 2.6667, "x": 2.94, "y": 8.97}, {"time": 3}]}, "9_8": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 3}]}, "9_10": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 3}], "translate": [{"curve": "stepped"}, {"time": 0.5667}, {"time": 0.6667, "x": 2.77, "y": -1.77}, {"time": 0.7, "x": 1.78, "y": -2.89}, {"time": 0.7333, "x": 0.45, "y": -3.52}, {"time": 0.7667, "x": -1.05, "y": -3.65}, {"time": 0.8, "x": -2.4, "y": -3.1}, {"time": 0.8333, "x": -3.54, "y": -2.1}, {"time": 0.8667, "x": -4.22, "y": -0.73}, {"time": 0.9, "x": -4.23, "y": 0.77}, {"time": 0.9333, "x": -3.74, "y": 2.13}, {"time": 0.9667, "x": -2.78, "y": 3.31}, {"time": 1, "x": -1.45, "y": 3.91}, {"time": 1.0333, "x": 0.08, "y": 4.01}, {"time": 1.0667, "x": 1.45, "y": 3.47}, {"time": 1.1, "x": 2.64, "y": 2.5}, {"time": 1.1333, "x": 3.29, "y": 1.12}, {"time": 1.1667, "x": 3.29, "y": -0.35}, {"time": 1.2, "x": 2.77, "y": -1.77}, {"time": 1.2333, "x": 1.78, "y": -2.89}, {"time": 1.2667, "x": 0.45, "y": -3.52}, {"time": 1.3, "x": -1.05, "y": -3.65}, {"time": 1.3333, "x": -2.4, "y": -3.1}, {"time": 1.3667, "x": -3.54, "y": -2.1}, {"time": 1.4, "x": -4.22, "y": -0.73}, {"time": 1.4333, "x": -4.23, "y": 0.77}, {"time": 1.4667, "x": -3.74, "y": 2.13}, {"time": 1.5, "x": -2.78, "y": 3.31}, {"time": 1.5333, "x": -1.45, "y": 3.91}, {"time": 1.5667, "x": 0.08, "y": 4.01}, {"time": 1.6, "x": 1.45, "y": 3.47}, {"time": 1.6333, "x": 2.64, "y": 2.5}, {"time": 1.6667, "x": 3.29, "y": 1.12}, {"time": 1.7, "x": 3.29, "y": -0.35}, {"time": 1.7333, "x": 2.77, "y": -1.77}, {"time": 1.7667, "x": 1.78, "y": -2.89}, {"time": 1.8, "x": 0.45, "y": -3.52}, {"time": 1.8333, "x": -1.05, "y": -3.65}, {"time": 1.8667, "x": -2.4, "y": -3.1}, {"time": 1.9, "x": -3.54, "y": -2.1}, {"time": 1.9333, "x": -4.22, "y": -0.73}, {"time": 1.9667, "x": -4.23, "y": 0.77}, {"time": 2, "x": -3.74, "y": 2.13}, {"time": 2.0333, "x": -2.78, "y": 3.31}, {"time": 2.0667, "x": -1.45, "y": 3.91}, {"time": 2.1, "x": 0.08, "y": 4.01}, {"time": 2.1333, "x": 1.45, "y": 3.47}, {"time": 2.1667, "x": 2.64, "y": 2.5}, {"time": 2.2, "x": 3.29, "y": 1.12}, {"time": 2.2333, "x": 3.29, "y": -0.35}, {"time": 2.2667, "x": 2.77, "y": -1.77}, {"time": 2.6667, "x": 2.64, "y": -1.67}, {"time": 3}]}, "9_13": {"translate": [{"time": 0.6667}, {"time": 0.7667, "x": 15.06, "y": 1.31}, {"time": 0.8667, "curve": "stepped"}, {"time": 0.9667}, {"time": 1.0667, "x": 15.06, "y": 1.31}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2667}, {"time": 1.3667, "x": 15.06, "y": 1.31}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.7333}, {"time": 1.8333, "x": 15.06, "y": 1.31}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.0333}, {"time": 2.1333, "x": 15.06, "y": 1.31}, {"time": 2.2333}]}, "9_14": {"translate": [{"time": 0.5}, {"time": 0.6, "x": 1.88}, {"time": 0.6667, "x": 4.62}, {"time": 0.7, "x": 2.88, "y": -1.93}, {"time": 0.7333, "x": 1.13, "y": -3.8}, {"time": 0.7667, "x": -0.61, "y": -4.73}, {"time": 0.8, "x": -2.36, "y": -5.24}, {"time": 0.8333, "x": -4.1, "y": -4.46}, {"time": 0.8667, "x": -5.84, "y": -2.71}, {"time": 0.9, "x": -7.59}, {"time": 0.9333, "x": -8.11, "y": 1.69}, {"time": 0.9667, "x": -6.94, "y": 4.46}, {"time": 1, "x": -5.07, "y": 7.05}, {"time": 1.0333, "x": -3.72, "y": 8.3}, {"time": 1.0667, "x": -1.62, "y": 9.86}, {"time": 1.1, "x": -0.38, "y": 9}, {"time": 1.1333, "x": 1.29, "y": 6.46}, {"time": 1.1667, "x": 2.96, "y": 3.59}, {"time": 1.2, "x": 4.62}, {"time": 1.2333, "x": 2.88, "y": -1.93}, {"time": 1.2667, "x": 1.13, "y": -3.8}, {"time": 1.3, "x": -0.61, "y": -4.73}, {"time": 1.3333, "x": -2.36, "y": -5.24}, {"time": 1.3667, "x": -4.1, "y": -4.46}, {"time": 1.4, "x": -5.84, "y": -2.71}, {"time": 1.4333, "x": -7.59}, {"time": 1.4667, "x": -8.11, "y": 1.69}, {"time": 1.5, "x": -6.94, "y": 4.46}, {"time": 1.5333, "x": -5.07, "y": 7.05}, {"time": 1.5667, "x": -3.72, "y": 8.3}, {"time": 1.6, "x": -1.62, "y": 9.86}, {"time": 1.6333, "x": -0.38, "y": 9}, {"time": 1.6667, "x": 1.29, "y": 6.46}, {"time": 1.7, "x": 2.96, "y": 3.59}, {"time": 1.7333, "x": 4.62}, {"time": 1.7667, "x": 2.88, "y": -1.93}, {"time": 1.8, "x": 1.13, "y": -3.8}, {"time": 1.8333, "x": -0.61, "y": -4.73}, {"time": 1.8667, "x": -2.36, "y": -5.24}, {"time": 1.9, "x": -4.1, "y": -4.46}, {"time": 1.9333, "x": -5.84, "y": -2.71}, {"time": 1.9667, "x": -7.59}, {"time": 2, "x": -8.11, "y": 1.69}, {"time": 2.0333, "x": -6.94, "y": 4.46}, {"time": 2.0667, "x": -5.07, "y": 7.05}, {"time": 2.1, "x": -3.72, "y": 8.3}, {"time": 2.1333, "x": -1.62, "y": 9.86}, {"time": 2.1667, "x": -0.38, "y": 9}, {"time": 2.2, "x": 1.29, "y": 6.46}, {"time": 2.2333, "x": 2.96, "y": 3.59}, {"time": 2.2667, "x": 4.62, "curve": "stepped"}, {"time": 2.6667, "x": 4.62}, {"time": 3}]}, "9_34": {"scale": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 1.072, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 1.072, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.072, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.072, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.072, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 2.2667}]}, "9_2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 5.66, "y": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -6.79, "y": -8.13, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.314, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "9_3": {"scale": [{}]}, "9_21": {"rotate": [{"angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -3.05}]}, "9_22": {"rotate": [{"angle": -2.94, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 1.72, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 3, "angle": -2.94}]}, "9_23": {"rotate": [{"angle": -2.72, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.72, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3, "angle": -2.72}]}, "9_24": {"rotate": [{"angle": -2.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 1.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3, "angle": -2.43}]}, "9_25": {"rotate": [{"angle": -2.08, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 1.72, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3, "angle": -2.08}]}, "9_33": {"rotate": [{"angle": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 8.54}]}, "9_26": {"rotate": [{"angle": 6.2, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -3.73, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 3, "angle": 6.2}]}, "9_27": {"rotate": [{"angle": 5.73, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -3.73, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3, "angle": 5.73}]}, "9_28": {"rotate": [{"angle": 5.11, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -3.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3, "angle": 5.11}]}, "9_29": {"rotate": [{"angle": 4.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -3.73, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3, "angle": 4.38}]}, "9_30": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.73, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3, "angle": 3.55}]}, "9_31": {"rotate": [{"angle": 2.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 3.36, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -3.73, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3, "angle": 2.7}]}, "9_19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 21.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 21.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 21.32, "curve": 0.25, "c3": 0.75}, {"time": 3}]}}, "deform": {"default": {"9_3": {"9_3": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "offset": 740, "vertices": [0.43977, -2.13206, 2.94522, -4.91315, 2.94534, -4.91315, -5.55935, -1.38148, 5.68937, -0.66735, 5.72186, -0.27434, 2.94522, -4.91315, 0, 0, 0, 0, 0, 0, 0, 0, 1.95701, -0.95351, 1.95707, -0.95351, 2.01799, -0.8166, 0.43977, -2.13206, 1.9192, 1.0275], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": "stepped"}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "offset": 740, "vertices": [0.43977, -2.13206, 2.94522, -4.91315, 2.94534, -4.91315, -5.55935, -1.38148, 5.68937, -0.66735, 5.72186, -0.27434, 2.94522, -4.91315, 0, 0, 0, 0, 0, 0, 0, 0, 1.95701, -0.95351, 1.95707, -0.95351, 2.01799, -0.8166, 0.43977, -2.13206, 1.9192, 1.0275], "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "offset": 740, "vertices": [0.43977, -2.13206, 2.94522, -4.91315, 2.94534, -4.91315, -5.55935, -1.38148, 5.68937, -0.66735, 5.72186, -0.27434, 2.94522, -4.91315, 0, 0, 0, 0, 0, 0, 0, 0, 1.95701, -0.95351, 1.95707, -0.95351, 2.01799, -0.8166, 0.43977, -2.13206, 1.9192, 1.0275], "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 740, "vertices": [0.43977, -2.13206, 2.94522, -4.91315, 2.94534, -4.91315, -5.55935, -1.38148, 5.68937, -0.66735, 5.72186, -0.27434, 2.94522, -4.91315, 0, 0, 0, 0, 0, 0, 0, 0, 1.95701, -0.95351, 1.95707, -0.95351, 2.01799, -0.8166, 0.43977, -2.13206, 1.9192, 1.0275], "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "offset": 740, "vertices": [0.43977, -2.13206, 2.94522, -4.91315, 2.94534, -4.91315, -5.55935, -1.38148, 5.68937, -0.66735, 5.72186, -0.27434, 2.94522, -4.91315, 0, 0, 0, 0, 0, 0, 0, 0, 1.95701, -0.95351, 1.95707, -0.95351, 2.01799, -0.8166, 0.43977, -2.13206, 1.9192, 1.0275], "curve": 0.25, "c3": 0.75}, {"time": 2.2333}]}, "9_4": {"9_4": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "vertices": [0.16513, 1.52711, 0.92867, 1.44456, 0.68102, -0.8461, 0.9426, -3.60457, 1.00449, -5.41369, 2.98566, -6.14285, 0.9701, -3.35003, 0.17216, -1.20383, 0.17216, -1.20383, 0.59867, -2.02243, 0.9701, -3.35003, 0.9701, -3.35003, 0.9701, -3.35003, 0.31662, -2.24944, 0.5436, -2.53147, 2.13953, -6.82384, 1.38295, -9.05947, 0.71557, -3.32253, -1.65761, -3.83844, -1.32053, -3.0749, 2.52474, -3.26062, 0.0069, -2.31816, 0.59161, 0.70849, 0.33711, 0.73602], "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "vertices": [0.16513, 1.52711, 0.92867, 1.44456, 0.68102, -0.8461, 0.9426, -3.60457, 1.00449, -5.41369, 2.98566, -6.14285, 0.9701, -3.35003, 0.17216, -1.20383, 0.17216, -1.20383, 0.59867, -2.02243, 0.9701, -3.35003, 0.9701, -3.35003, 0.9701, -3.35003, 0.31662, -2.24944, 0.5436, -2.53147, 2.13953, -6.82384, 1.38295, -9.05947, 0.71557, -3.32253, -1.65761, -3.83844, -1.32053, -3.0749, 2.52474, -3.26062, 0.0069, -2.31816, 0.59161, 0.70849, 0.33711, 0.73602], "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [0.16513, 1.52711, 0.92867, 1.44456, 0.68102, -0.8461, 0.9426, -3.60457, 1.00449, -5.41369, 2.98566, -6.14285, 0.9701, -3.35003, 0.17216, -1.20383, 0.17216, -1.20383, 0.59867, -2.02243, 0.9701, -3.35003, 0.9701, -3.35003, 0.9701, -3.35003, 0.31662, -2.24944, 0.5436, -2.53147, 2.13953, -6.82384, 1.38295, -9.05947, 0.71557, -3.32253, -1.65761, -3.83844, -1.32053, -3.0749, 2.52474, -3.26062, 0.0069, -2.31816, 0.59161, 0.70849, 0.33711, 0.73602], "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "vertices": [0.16513, 1.52711, 0.92867, 1.44456, 0.68102, -0.8461, 0.9426, -3.60457, 1.00449, -5.41369, 2.98566, -6.14285, 0.9701, -3.35003, 0.17216, -1.20383, 0.17216, -1.20383, 0.59867, -2.02243, 0.9701, -3.35003, 0.9701, -3.35003, 0.9701, -3.35003, 0.31662, -2.24944, 0.5436, -2.53147, 2.13953, -6.82384, 1.38295, -9.05947, 0.71557, -3.32253, -1.65761, -3.83844, -1.32053, -3.0749, 2.52474, -3.26062, 0.0069, -2.31816, 0.59161, 0.70849, 0.33711, 0.73602], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "vertices": [0.16513, 1.52711, 0.92867, 1.44456, 0.68102, -0.8461, 0.9426, -3.60457, 1.00449, -5.41369, 2.98566, -6.14285, 0.9701, -3.35003, 0.17216, -1.20383, 0.17216, -1.20383, 0.59867, -2.02243, 0.9701, -3.35003, 0.9701, -3.35003, 0.9701, -3.35003, 0.31662, -2.24944, 0.5436, -2.53147, 2.13953, -6.82384, 1.38295, -9.05947, 0.71557, -3.32253, -1.65761, -3.83844, -1.32053, -3.0749, 2.52474, -3.26062, 0.0069, -2.31816, 0.59161, 0.70849, 0.33711, 0.73602], "curve": 0.25, "c3": 0.75}, {"time": 2.2667}]}}}}}}