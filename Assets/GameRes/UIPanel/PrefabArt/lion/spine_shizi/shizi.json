{"skeleton": {"hash": "sorHKL9bN06QvpX6iojSOLgbeZk", "spine": "3.8.95", "x": -253.74, "y": -29.43, "width": 510.74, "height": 423, "images": "", "audio": "E:/途游/spine/狮子吼小游戏/狮子"}, "bones": [{"name": "root"}, {"name": "BIP", "parent": "root", "x": -257, "y": -95.43}, {"name": "BIP2", "parent": "BIP", "x": 236.19, "y": 103.53}, {"name": "shen", "parent": "BIP2", "x": 34.75, "y": 149, "color": "fd00ffff"}, {"name": "shen3", "parent": "shen", "length": 102.36, "rotation": 124.32, "x": -13.99, "y": 1.75, "color": "fd00ffff"}, {"name": "shen4", "parent": "shen", "length": 58.3, "rotation": -16.26, "x": 9.33, "y": -4.08, "color": "fd00ffff"}, {"name": "to<PERSON><PERSON>", "parent": "shen3", "length": 50, "rotation": -124.32, "x": 58.96, "y": 20.4, "color": "ff9d00ff"}, {"name": "toumao3", "parent": "to<PERSON><PERSON>", "length": 41.74, "rotation": -76.96, "x": -5.14, "y": -10.27, "color": "ff9d00ff"}, {"name": "toumao2", "parent": "toumao3", "length": 28.73, "rotation": 15.44, "x": 38.94, "y": -0.67, "color": "ff9d00ff"}, {"name": "toumao5", "parent": "toumao2", "length": 37.24, "rotation": 15.13, "x": 31.28, "y": 2.02, "color": "ff9d00ff"}, {"name": "toumao6", "parent": "to<PERSON><PERSON>", "length": 30.26, "rotation": -45, "x": 22.68, "y": 59.06, "color": "ff9d00ff"}, {"name": "toumao4", "parent": "toumao6", "length": 31.81, "rotation": -2.73, "x": 28.45, "color": "ff9d00ff"}, {"name": "toumao8", "parent": "toumao4", "length": 35.03, "rotation": 9.18, "x": 33.37, "y": -0.83, "color": "ff9d00ff"}, {"name": "chi2", "parent": "shen", "x": 25.11, "y": 36.93, "color": "000affff"}, {"name": "chi4", "parent": "chi2", "length": 76.84, "rotation": 54.56, "x": 2.26, "y": -4.51, "color": "000affff"}, {"name": "chi3", "parent": "chi4", "length": 68.15, "rotation": -30.12, "x": 77.17, "y": -0.46, "color": "000affff"}, {"name": "chi6", "parent": "chi3", "length": 51.79, "rotation": 13.92, "x": 70.67, "y": 0.09, "color": "000affff"}, {"name": "chi5", "parent": "chi6", "length": 50.88, "rotation": 20.23, "x": 55.15, "y": -1.22, "color": "000affff"}, {"name": "chi1", "parent": "shen", "x": -105.17, "y": 23.96, "color": "10ff00ff"}, {"name": "chi8", "parent": "chi1", "length": 64.65, "rotation": 125.77, "x": -2.82, "y": 18.05, "color": "10ff00ff"}, {"name": "chi7", "parent": "chi8", "length": 40.14, "rotation": 30.19, "x": 57.69, "y": -1.94, "color": "10ff00ff"}, {"name": "chi10", "parent": "chi7", "length": 42.3, "rotation": -29.09, "x": 40.94, "y": 0.97, "color": "10ff00ff"}, {"name": "chi9", "parent": "chi10", "length": 35.61, "rotation": -7.46, "x": 44.56, "y": 1.13, "color": "10ff00ff"}, {"name": "tou", "parent": "to<PERSON><PERSON>", "rotation": 77.68, "x": -91.57, "y": 69.78, "color": "ff9d00ff"}, {"name": "re2", "parent": "to<PERSON><PERSON>", "length": 15.46, "rotation": 6.84, "x": 7.76, "y": 84.36, "color": "ff0000ff"}, {"name": "mao1", "parent": "re2", "length": 16.64, "rotation": -66.97, "x": -6.93, "y": 14.44, "color": "faff00ff"}, {"name": "mao3", "parent": "mao1", "length": 15.73, "rotation": 8.78, "x": 16.64, "color": "faff00ff"}, {"name": "mao2", "parent": "mao3", "length": 12.81, "rotation": 5.37, "x": 19.51, "y": -0.67, "color": "faff00ff"}, {"name": "re4", "parent": "re2", "length": 16.07, "rotation": 21.68, "x": 15.61, "y": 1.22, "color": "ff0000ff"}, {"name": "re1", "parent": "to<PERSON><PERSON>", "length": 11.54, "rotation": 151.39, "x": -44.43, "y": 79.45, "color": "ff0000ff"}, {"name": "re5", "parent": "re1", "length": 12.5, "rotation": 3.93, "x": 10.32, "y": -0.32, "color": "ff0000ff"}, {"name": "xia<PERSON>i", "parent": "tou", "rotation": 7.63, "x": -63.8, "y": -68.38, "color": "ff9d00ff"}, {"name": "xiaya", "parent": "xia<PERSON>i", "length": 13.12, "rotation": -173.06, "x": -0.87, "y": -2.42, "color": "ff9d00ff"}, {"name": "xiaba", "parent": "xiaya", "length": 15.47, "rotation": 2.52, "x": 11.83, "y": 0.05, "color": "ff9d00ff"}, {"name": "xiaba3", "parent": "xiaba", "length": 27.06, "rotation": -9.12, "x": 15.73, "y": -0.02, "color": "ff9d00ff"}, {"name": "shangchi", "parent": "tou", "rotation": 11.44, "x": 1.95, "y": -51.71, "color": "ff4f00ff"}, {"name": "zu<PERSON>", "parent": "tou", "length": 43.23, "rotation": -160.85, "x": -32.03, "y": -65.65, "color": "ff0000ff"}, {"name": "tui10", "parent": "shen4", "length": 31.09, "rotation": -17.67, "x": 29.89, "y": 25.02, "color": "fd00ffff"}, {"name": "tui12", "parent": "tui10", "length": 33.86, "rotation": -19.52, "x": 30.3, "y": 1.17, "color": "fd00ffff"}, {"name": "tui11", "parent": "tui12", "length": 25.21, "rotation": -18.12, "x": 31.8, "y": 0.36, "color": "fd00ffff"}, {"name": "tui14", "parent": "tui11", "length": 21.15, "rotation": -14.62, "x": 22.1, "y": -0.44, "color": "fd00ffff"}, {"name": "tui13", "parent": "tui14", "length": 18.91, "rotation": 3.31, "x": 20.12, "y": -1.34, "color": "fd00ffff"}, {"name": "tui16", "parent": "tui13", "length": 16.01, "rotation": 24.7, "x": 17.51, "y": 0.17, "color": "fd00ffff"}, {"name": "tui15", "parent": "tui16", "length": 14.36, "rotation": 19.81, "x": 15.61, "y": 0.25, "color": "fd00ffff"}, {"name": "tui18", "parent": "tui15", "length": 16.58, "rotation": -22.89, "x": 11.35, "y": 0.02, "color": "fd00ffff"}, {"name": "tui17", "parent": "tui18", "length": 11.41, "rotation": -38.2, "x": 15.53, "y": 0.04, "color": "fd00ffff"}, {"name": "tui20", "parent": "tui17", "length": 9.5, "rotation": -23.44, "x": 11.33, "y": 0.46, "color": "fd00ffff"}, {"name": "tui2", "parent": "BIP2", "length": 22.03, "rotation": 69.83, "x": -85.13, "y": -0.84}, {"name": "tui4", "parent": "tui2", "length": 48.44, "rotation": 1.89, "x": 21.2, "y": 1.04}, {"name": "tui3", "parent": "tui4", "length": 81.09, "rotation": 20.66, "x": 52.04, "y": -0.14}, {"name": "tui5", "parent": "BIP2", "length": 19.03, "rotation": 86.19, "x": 7.29, "y": 10.55}, {"name": "tui7", "parent": "tui5", "length": 34.25, "rotation": 0.28, "x": 16.48, "y": 0.25}, {"name": "tui6", "parent": "tui7", "length": 50.02, "rotation": 12.76, "x": 36.3, "y": 0.97}, {"name": "tui1", "parent": "BIP2", "length": 23.69, "rotation": 85.91, "x": 52.02, "y": -7.17}, {"name": "tui9", "parent": "tui1", "length": 46.96, "rotation": -0.04, "x": 21.62, "y": -0.57}, {"name": "tui8", "parent": "tui9", "length": 80.7, "rotation": 16.2, "x": 49.52, "y": -0.24}, {"name": "shen2", "parent": "BIP2", "length": 20.3, "rotation": 86.42, "x": 110.68, "y": 3.8}, {"name": "shen6", "parent": "shen2", "length": 34.6, "rotation": 3.58, "x": 20.3}, {"name": "shen5", "parent": "shen6", "length": 54.39, "rotation": 21.39, "x": 32.92, "y": 0.42}, {"name": "ying", "parent": "BIP", "x": 246.93, "y": 102.07}, {"name": "yan3", "parent": "tou", "length": 30.78, "rotation": 101.31, "x": -42.07, "y": 109.74, "color": "ff9d00ff"}, {"name": "yan1", "parent": "yan3", "rotation": 148.69, "x": -155.15, "y": -7.88, "color": "06ff00ff"}, {"name": "yan2", "parent": "yan3", "rotation": -145.44, "x": -190.77, "y": -11.55, "color": "06ff00ff"}, {"name": "shangzui2", "parent": "tou", "rotation": 11.44, "x": -28.03, "y": -53.23, "color": "5000ffff"}, {"name": "shangzui6", "parent": "tou", "rotation": 11.44, "x": -43.57, "y": -54.27, "color": "fff1daff"}, {"name": "shangzui5", "parent": "tou", "rotation": 11.44, "x": -39.88, "y": -74.48, "color": "fff1daff"}, {"name": "shang<PERSON>i", "parent": "tou", "rotation": 11.44, "x": -13.13, "y": -83.31, "color": "ff9d00ff"}, {"name": "shangzui3", "parent": "tou", "rotation": 11.44, "x": -21.07, "y": -43.61, "color": "ff9d00ff"}, {"name": "tou2", "parent": "yan3", "length": 4.13, "rotation": -55.94, "x": -188.72, "y": -14.15, "color": "ff9d00ff"}, {"name": "tou3", "parent": "yan3", "length": 3.94, "rotation": 130.81, "x": -192.53, "y": -8.74, "color": "ff9d00ff"}, {"name": "toumao7", "parent": "yan3", "length": 3.19, "rotation": -115, "x": -155.88, "y": -8.83, "color": "ff9d00ff"}, {"name": "tou4", "parent": "yan3", "length": 2.23, "rotation": 56.31, "x": -154.51, "y": -6.05, "color": "ff9d00ff"}, {"name": "toumao10", "parent": "to<PERSON><PERSON>", "length": 23.95, "rotation": -78.86, "x": -57.13, "y": -17.87, "color": "ff9d00ff"}, {"name": "toumao9", "parent": "toumao10", "length": 24.68, "rotation": 15.42, "x": 27.3, "y": 0.43, "color": "ff9d00ff"}, {"name": "toumao12", "parent": "toumao9", "length": 23.75, "rotation": 13.57, "x": 24.68, "y": 1.59, "color": "ff9d00ff"}, {"name": "toumao11", "parent": "toumao12", "length": 24.75, "rotation": 9.53, "x": 23.85, "y": 1.78, "color": "ff9d00ff"}, {"name": "toumao14", "parent": "to<PERSON><PERSON>", "length": 29.19, "rotation": -60, "x": 26.89, "y": 14.17, "color": "ff9d00ff"}, {"name": "toumao13", "parent": "toumao14", "length": 28.5, "rotation": -20.66, "x": 29.63, "y": -0.66, "color": "ff9d00ff"}, {"name": "toumao16", "parent": "toumao13", "length": 30.03, "rotation": 0.9, "x": 29.61, "y": 0.18, "color": "ff9d00ff"}, {"name": "toumao17", "parent": "to<PERSON><PERSON>", "x": 6.47, "y": 108.23, "color": "ff9d00ff"}, {"name": "shang<PERSON>i4", "parent": "tou", "x": -31.48, "y": -86.4, "color": "ff0000ff"}], "slots": [{"name": "ying", "bone": "ying", "attachment": "images/ying"}, {"name": "chi1", "bone": "chi9", "attachment": "images/chi1"}, {"name": "tui4", "bone": "tui20", "attachment": "images/tui4"}, {"name": "tui3", "bone": "tui6", "attachment": "images/tui3"}, {"name": "tui2", "bone": "tui3", "attachment": "images/tui2"}, {"name": "shen", "bone": "shen5", "attachment": "images/shen"}, {"name": "tui1", "bone": "tui8", "attachment": "images/tui1"}, {"name": "chi2", "bone": "chi5", "attachment": "images/chi2"}, {"name": "to<PERSON><PERSON>", "bone": "toumao17", "attachment": "images/toumao"}, {"name": "re1", "bone": "re5", "attachment": "images/re1"}, {"name": "re2", "bone": "re4", "attachment": "images/re2"}, {"name": "mao1", "bone": "mao2", "attachment": "images/mao1"}, {"name": "yan2", "bone": "yan2", "attachment": "images/yan2"}, {"name": "yan1", "bone": "yan1", "attachment": "images/yan1"}, {"name": "tou", "bone": "tou", "attachment": "images/tou"}, {"name": "zu<PERSON>", "bone": "zu<PERSON>", "attachment": "images/zuili"}, {"name": "shangya", "bone": "tou", "attachment": "images/shangya"}, {"name": "xiaya", "bone": "xiaya", "attachment": "images/xiaya"}, {"name": "xiaba", "bone": "xiaba3", "attachment": "images/xiaba"}, {"name": "yanguang1", "bone": "yan1", "color": "ffffff00", "attachment": "images/yanguang1", "blend": "additive"}, {"name": "shang<PERSON>i", "bone": "tou", "attachment": "images/shangzui"}, {"name": "chiguang2", "bone": "chi4", "attachment": "images/chiguang2", "blend": "additive"}, {"name": "shipin1", "bone": "tou", "attachment": "images/shipin1"}, {"name": "shipin2", "bone": "shen4", "attachment": "images/shipin2"}, {"name": "guang2", "bone": "shen4", "attachment": "images/guang2", "blend": "additive"}, {"name": "yangguang2", "bone": "yan2", "color": "ffffff00", "attachment": "images/yangguang2", "blend": "additive"}, {"name": "guang1", "bone": "tou", "attachment": "images/guang1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"chi1": {"images/chi1": {"type": "mesh", "uvs": [0.96579, 0.55265, 0.98182, 0.63666, 1, 0.73192, 1, 0.81707, 1, 0.88617, 0.90134, 0.96121, 0.72947, 1, 0.52657, 0.9633, 0.43114, 0.86112, 0.32605, 0.74859, 0.22491, 0.65306, 0.16197, 0.59362, 0.09211, 0.52763, 0.04263, 0.44803, 0.00141, 0.38172, 0.00141, 0.23598, 0.00141, 0.16285, 0.08604, 0.08152, 0.17089, 0, 0.37857, 0, 0.57192, 0.04611, 0.67317, 0.0973, 0.7987, 0.16076, 0.91089, 0.34628, 0.87527, 0.56153, 0.83632, 0.56502, 0.81472, 0.56147, 0.78475, 0.511, 0.7444, 0.43567, 0.69374, 0.39909, 0.60165, 0.37862, 0.51417, 0.34798, 0.4329, 0.26339, 0.34378, 0.18487, 0.27445, 0.0842, 0.24192, 0.06951, 0.22213, 0.13739, 0.24337, 0.24814, 0.30136, 0.35776, 0.34803, 0.44417, 0.42722, 0.53789, 0.48663, 0.61652, 0.54319, 0.67334, 0.60821, 0.73788, 0.75381, 0.82118, 0.84287, 0.84859, 0.91497, 0.82285, 0.59982, 0.49709, 0.81475, 0.7242, 0.90238, 0.72096, 0.67625, 0.57625, 0.72712, 0.64346, 0.47109, 0.40522, 0.29853, 0.20971, 0.28437, 0.15065, 0.38764, 0.31296, 0.53333, 0.4555, 0.109, 0.15733, 0.14861, 0.33486, 0.26174, 0.48532, 0.38195, 0.62209, 0.47101, 0.76309, 0.59598, 0.85243, 0.74687, 0.93065, 0.40216, 0.07832, 0.50338, 0.15283, 0.6166, 0.21834, 0.74012, 0.28668, 0.82251, 0.41915, 0.90855, 0.53059, 0.95616, 0.73234, 0.89122, 0.64676, 0.8875, 0.90828, 0.96639, 0.86041, 0.17748, 0.09795, 0.15005, 0.26549, 0.19465, 0.41366, 0.33359, 0.56163, 0.4262, 0.69494], "triangles": [2, 70, 1, 70, 2, 3, 44, 43, 48, 46, 49, 70, 46, 70, 3, 46, 45, 48, 46, 48, 49, 44, 48, 45, 62, 61, 43, 62, 43, 44, 73, 46, 3, 8, 61, 62, 73, 3, 4, 72, 45, 46, 72, 46, 73, 63, 62, 44, 63, 44, 45, 63, 45, 72, 4, 5, 72, 4, 72, 73, 7, 8, 62, 7, 62, 63, 6, 7, 63, 5, 6, 63, 5, 63, 72, 0, 69, 23, 27, 68, 69, 27, 25, 26, 27, 69, 25, 69, 24, 25, 28, 50, 47, 50, 28, 27, 50, 27, 26, 41, 47, 50, 51, 50, 26, 0, 71, 24, 0, 24, 69, 71, 0, 1, 42, 41, 50, 42, 50, 51, 49, 71, 1, 26, 71, 51, 71, 48, 51, 25, 24, 71, 25, 71, 26, 48, 71, 49, 70, 49, 1, 43, 42, 51, 61, 42, 43, 48, 43, 51, 66, 65, 21, 66, 21, 22, 67, 66, 22, 67, 22, 23, 31, 32, 66, 30, 31, 66, 30, 66, 67, 29, 30, 67, 68, 29, 67, 23, 68, 67, 28, 29, 68, 56, 31, 30, 52, 31, 56, 47, 56, 30, 47, 30, 29, 27, 28, 68, 69, 68, 23, 28, 47, 29, 65, 64, 20, 65, 20, 21, 32, 33, 65, 32, 65, 66, 55, 53, 32, 55, 32, 31, 38, 37, 55, 52, 55, 31, 76, 58, 38, 39, 38, 55, 39, 55, 52, 59, 76, 38, 39, 59, 38, 12, 13, 76, 12, 76, 59, 40, 39, 52, 40, 52, 56, 77, 59, 39, 77, 39, 40, 11, 12, 59, 11, 59, 77, 47, 41, 40, 47, 40, 56, 60, 77, 40, 60, 40, 41, 10, 11, 77, 10, 77, 60, 78, 60, 41, 78, 41, 42, 9, 10, 60, 9, 60, 78, 61, 78, 42, 9, 78, 61, 8, 9, 61, 35, 18, 19, 64, 19, 20, 34, 35, 19, 74, 17, 18, 64, 34, 19, 35, 74, 18, 36, 74, 35, 36, 35, 34, 54, 34, 64, 36, 34, 54, 57, 17, 74, 57, 74, 36, 16, 17, 57, 33, 54, 64, 33, 64, 65, 53, 54, 33, 15, 16, 57, 53, 37, 36, 53, 36, 54, 75, 57, 36, 37, 75, 36, 15, 57, 75, 32, 53, 33, 37, 53, 55, 58, 15, 75, 14, 15, 58, 58, 37, 38, 37, 58, 75, 13, 14, 58, 13, 58, 76], "vertices": [3, 20, -56.98, 0.76, 0.12481, 19, 8.05, -29.93, 0.87338, 18, 16.76, 42.08, 0.00181, 3, 20, -66.71, 15.13, 0.01877, 19, -7.59, -22.4, 0.84307, 18, 19.79, 24.99, 0.13816, 3, 20, -77.75, 31.43, 3e-05, 19, -25.32, -13.86, 0.29037, 18, 23.23, 5.6, 0.7096, 2, 19, -40.04, -3.26, 0.0134, 18, 23.23, -12.53, 0.9866, 2, 21, -144.82, -11.37, 0.00079, 18, 23.23, -27.25, 0.99921, 2, 21, -146.59, 12.9, 0.01741, 18, 4.88, -43.24, 0.98259, 3, 21, -134.02, 43.43, 0.08295, 19, -42.24, 60.34, 0.04884, 18, -27.09, -51.5, 0.86821, 4, 22, -157.22, 47.79, 5e-05, 21, -105.12, 68.93, 0.20295, 19, -13.84, 86.39, 0.17883, 18, -64.83, -43.68, 0.61818, 4, 22, -129.54, 52.57, 0.00472, 21, -77.06, 70.07, 0.3304, 19, 14.2, 88.07, 0.25551, 18, -82.58, -21.91, 0.40937, 4, 22, -99.07, 57.83, 0.03687, 21, -46.16, 71.33, 0.54771, 19, 45.07, 89.92, 0.23071, 18, -102.12, 2.05, 0.18471, 4, 22, -72.1, 64.22, 0.12341, 21, -18.6, 74.17, 0.68452, 19, 72.57, 93.29, 0.12024, 18, -120.94, 22.4, 0.07183, 4, 22, -55.33, 68.2, 0.22081, 21, -1.44, 75.94, 0.68069, 19, 89.69, 95.39, 0.06255, 18, -132.64, 35.06, 0.03595, 4, 22, -36.7, 72.62, 0.36244, 21, 17.6, 77.9, 0.59757, 19, 108.69, 97.72, 0.02482, 18, -145.64, 49.12, 0.01517, 4, 22, -17.41, 72.31, 0.53681, 21, 36.68, 75.09, 0.45164, 19, 127.83, 95.27, 0.0066, 18, -154.84, 66.07, 0.00495, 4, 22, -1.34, 72.06, 0.67695, 21, 52.58, 72.75, 0.32082, 19, 143.77, 93.24, 0.00097, 18, -162.51, 80.2, 0.00126, 2, 22, 25.7, 56.82, 0.91889, 21, 77.42, 54.13, 0.08111, 2, 22, 39.27, 49.17, 0.97983, 21, 89.88, 44.78, 0.02017, 1, 22, 46.63, 26.95, 1, 1, 22, 54.01, 4.67, 1, 4, 22, 35.04, -28.98, 0.76051, 21, 75.54, -32.15, 0.16991, 20, 91.32, -63.85, 0.04295, 19, 168.73, -11.21, 0.02663, 4, 22, 8.83, -55.48, 0.17372, 21, 46.1, -55.03, 0.38936, 20, 54.48, -69.53, 0.27495, 19, 139.74, -34.65, 0.16197, 4, 22, -9.92, -66.54, 0.03857, 21, 26.08, -63.56, 0.26261, 20, 32.84, -67.24, 0.43248, 19, 119.89, -43.55, 0.26635, 4, 22, -33.16, -80.24, 0.00063, 21, 1.26, -74.12, 0.0965, 20, 6.01, -64.41, 0.57432, 19, 95.27, -54.6, 0.32856, 3, 21, -42.88, -67.11, 0.0011, 20, -29.15, -36.83, 0.78663, 19, 51.01, -48.43, 0.21227, 2, 20, -42.24, 9, 0.09454, 19, 16.65, -15.4, 0.90546, 2, 20, -35.9, 12.55, 0.05463, 19, 20.35, -9.14, 0.94537, 2, 20, -31.9, 13.44, 0.03879, 19, 23.35, -6.36, 0.96121, 2, 20, -22.38, 5.74, 0.1452, 19, 35.46, -8.23, 0.8548, 2, 20, -8.91, -6.06, 0.77275, 19, 53.03, -11.66, 0.22725, 2, 20, 2.92, -9.45, 0.48972, 19, 64.96, -8.64, 0.51028, 2, 20, 20.38, -6.58, 0.78925, 19, 78.62, 2.62, 0.21075, 2, 20, 37.94, -6.01, 0.96798, 19, 93.51, 11.94, 0.03202, 3, 21, 24.34, -6.36, 0.90378, 20, 59.12, -16.41, 0.07993, 19, 117.05, 13.6, 0.01629, 4, 22, 3.72, -3.88, 0.74966, 21, 47.74, -3.2, 0.24236, 20, 81.11, -25.03, 0.00534, 19, 140.39, 17.2, 0.00263, 4, 22, 28.84, -3.24, 0.98915, 21, 72.73, -5.83, 0.00849, 20, 101.67, -39.48, 0.0014, 19, 165.42, 15.05, 0.00097, 1, 22, 34.56, 0.48, 1, 1, 22, 23.77, 10.79, 1, 3, 22, 1.21, 18.97, 0.85077, 21, 48.22, 19.78, 0.14921, 18, -117.51, 108.48, 2e-05, 4, 22, -24.48, 21.07, 0.29058, 21, 23.02, 25.2, 0.7035, 19, 115.12, 45.13, 0.00301, 18, -106.73, 85.08, 0.00292, 4, 22, -44.78, 22.55, 0.12862, 21, 3.09, 29.3, 0.82568, 19, 95.11, 48.85, 0.03101, 18, -98.05, 66.67, 0.01469, 5, 22, -69.42, 19.53, 0.04318, 21, -21.74, 29.51, 0.69675, 20, 36.29, 37.33, 0.04399, 19, 70.29, 48.58, 0.16422, 18, -83.32, 46.68, 0.05186, 5, 22, -89.51, 18.18, 0.01716, 21, -41.83, 30.78, 0.486, 20, 19.35, 48.21, 0.03821, 19, 50.17, 49.46, 0.34042, 18, -72.28, 29.85, 0.11821, 5, 22, -105.21, 14.95, 0.00599, 21, -57.82, 29.62, 0.3409, 20, 4.81, 54.97, 0.01362, 19, 34.21, 47.99, 0.43697, 18, -61.76, 17.75, 0.20251, 5, 22, -123.07, 11.13, 0.00098, 21, -76.02, 28.15, 0.21766, 20, -11.81, 62.53, 0.00076, 19, 16.04, 46.17, 0.42231, 18, -49.66, 4.07, 0.35829, 3, 21, -106.37, 17.05, 0.06489, 19, -14.09, 34.49, 0.14905, 18, -22.57, -13.55, 0.78605, 3, 21, -120.94, 7.26, 0.02076, 19, -28.47, 24.43, 0.01183, 18, -6, -19.33, 0.96741, 2, 21, -124.57, -6.78, 0.0023, 18, 7.41, -13.81, 0.9977, 5, 22, -77.84, -12.55, 0.0005, 21, -34.26, -1.2, 0.16314, 20, 10.42, 16.58, 0.337, 19, 58.36, 17.63, 0.48365, 18, -51.24, 55.1, 0.01571, 3, 21, -96.96, -4.15, 0.01556, 19, -4.27, 13.48, 0.47157, 18, -11.26, 6.7, 0.51286, 2, 19, -13.16, -0.21, 0.3747, 18, 5.04, 7.49, 0.6253, 5, 22, -99.71, -16.53, 1e-05, 21, -56.46, -2.31, 0.08111, 20, -9.52, 26.4, 0.00913, 19, 36.19, 16.1, 0.86686, 18, -37.04, 38, 0.0429, 3, 21, -73.5, -1.36, 0.05361, 19, 19.13, 16.72, 0.8311, 18, -27.57, 23.8, 0.11528, 4, 22, -48.91, -1.39, 0.00833, 21, -4.12, 6.11, 0.98082, 19, 88.35, 25.52, 0.00735, 18, -75.17, 74.82, 0.00349, 2, 22, 3.27, 6.03, 0.99918, 21, 48.59, 6.69, 0.00082, 1, 22, 15.56, 2.13, 1, 3, 22, -24.11, 2.45, 0.04572, 21, 20.97, 6.69, 0.95404, 18, -90.69, 94.55, 0.00025, 5, 22, -63.99, -6.17, 0.00293, 21, -19.69, 3.32, 0.38352, 20, 25.35, 13.45, 0.52173, 19, 72.84, 22.43, 0.08167, 18, -63.6, 64.04, 0.01014, 2, 22, 30.43, 31.18, 0.99201, 21, 78.78, 28.09, 0.00799, 4, 22, -6.18, 43.36, 0.67538, 21, 44.06, 44.93, 0.32251, 19, 135.78, 65.25, 0.0009, 18, -135.13, 90.07, 0.00121, 4, 22, -44.46, 40.79, 0.2233, 21, 5.77, 47.35, 0.71513, 19, 97.45, 66.94, 0.04009, 18, -114.09, 57.99, 0.02149, 5, 22, -80.87, 35.65, 0.05316, 21, -31, 46.98, 0.63294, 20, 36.69, 57.1, 0.00797, 19, 60.7, 65.86, 0.20285, 18, -91.74, 28.8, 0.10308, 5, 22, -115.12, 35.94, 0.00929, 21, -64.92, 51.71, 0.38488, 20, 9.34, 77.73, 0.00063, 19, 26.68, 69.95, 0.29989, 18, -75.17, -1.19, 0.30531, 4, 22, -143.09, 25.02, 0.00022, 21, -94.07, 44.52, 0.19693, 19, -2.32, 62.19, 0.22633, 18, -51.93, -20.19, 0.57652, 3, 21, -124.19, 32.01, 0.0735, 19, -32.19, 49.11, 0.04985, 18, -23.85, -36.78, 0.87665, 4, 22, 18.28, -24.56, 0.60998, 21, 59.49, -25.6, 0.28101, 20, 80.49, -50.32, 0.06926, 19, 152.56, -4.96, 0.03975, 4, 22, -4.85, -33.13, 0.1305, 21, 35.44, -31.09, 0.50329, 20, 56.8, -43.42, 0.24652, 19, 128.62, -10.91, 0.11969, 4, 22, -27.42, -44.57, 0.00884, 21, 11.58, -39.51, 0.21037, 20, 31.86, -39.18, 0.49984, 19, 104.93, -19.79, 0.28095, 3, 21, -13.94, -49.08, 0.03244, 20, 4.9, -35.14, 0.60101, 19, 79.59, -29.85, 0.36655, 2, 20, -20.65, -15.45, 0.72016, 19, 47.61, -25.67, 0.27984, 2, 20, -45.22, 0.48, 0.19417, 19, 18.36, -24.26, 0.80583, 2, 19, -20.77, -7.08, 0.28895, 18, 15.06, 5.33, 0.71105, 3, 20, -52.19, 23.92, 0.00773, 19, 0.55, -7.5, 0.96764, 18, 2.94, 22.88, 0.02463, 2, 21, -136.05, 8.21, 0.01464, 18, 2.3, -31.99, 0.98536, 2, 21, -136.69, -9.65, 0.00093, 18, 16.98, -21.78, 0.99907, 1, 22, 35.19, 13.88, 1, 3, 22, 6.57, 35.87, 0.85418, 21, 55.73, 35.84, 0.14576, 18, -134.87, 104.86, 6e-05, 4, 22, -25.01, 44.15, 0.42657, 21, 25.49, 48.15, 0.55646, 19, 117.15, 68.12, 0.01005, 18, -126.57, 73.28, 0.00693, 5, 22, -65.21, 37.15, 0.10093, 21, -15.28, 46.43, 0.72359, 20, 50.16, 48.98, 0.00275, 19, 76.42, 65.62, 0.11699, 18, -100.73, 41.7, 0.05573, 5, 22, -98.4, 36.08, 0.02443, 21, -48.33, 49.68, 0.50429, 20, 22.86, 67.89, 0.00432, 19, 43.32, 68.24, 0.27869, 18, -83.51, 13.31, 0.18827], "hull": 24, "edges": [8, 10, 10, 12, 12, 14, 36, 38, 38, 40, 44, 46, 0, 46, 18, 20, 14, 16, 16, 18, 40, 42, 42, 44, 32, 34, 34, 36, 0, 2, 2, 4, 4, 6, 6, 8, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 22, 24], "width": 186, "height": 213}}, "chi2": {"images/chi2": {"type": "mesh", "uvs": [0, 0.6001, 0, 0.49849, 0.08228, 0.42153, 0.13579, 0.37147, 0.21503, 0.32513, 0.28423, 0.28465, 0.4002, 0.2278, 0.48753, 0.18497, 0.58889, 0.10721, 0.66986, 0.0451, 0.78441, 0, 0.89924, 0.00563, 0.98287, 0.00972, 1, 0.10619, 1, 0.19848, 1, 0.29912, 1, 0.39276, 1, 0.521, 0.97915, 0.60539, 0.96028, 0.68177, 0.82314, 0.8104, 0.73722, 0.87318, 0.65372, 0.93419, 0.56777, 0.96042, 0.44817, 0.99691, 0.31799, 0.99873, 0.22742, 1, 0.13069, 0.97449, 0.01551, 0.94411, 0.00705, 0.80582, 0, 0.69072, 0.69891, 0.28714, 0.72901, 0.25976, 0.77742, 0.21021, 0.83369, 0.15806, 0.87949, 0.18023, 0.90042, 0.20761, 0.88995, 0.29758, 0.85724, 0.3745, 0.81275, 0.47099, 0.74994, 0.5401, 0.70414, 0.60268, 0.62302, 0.67309, 0.55236, 0.72525, 0.50656, 0.76437, 0.4202, 0.80348, 0.30505, 0.84651, 0.6505, 0.33278, 0.57984, 0.38754, 0.50002, 0.42144, 0.41104, 0.43709, 0.34692, 0.46708, 0.30636, 0.53358, 0.26972, 0.58704, 0.23177, 0.60529, 0.1598, 0.60529, 0.32075, 0.38885, 0.39272, 0.35364, 0.46992, 0.31974, 0.56806, 0.26628, 0.65311, 0.18935, 0.71461, 0.12937, 0.82976, 0.08895, 0.93313, 0.10851, 0.958, 0.205, 0.95145, 0.32496, 0.93052, 0.41884, 0.9122, 0.51141, 0.86509, 0.61964, 0.77742, 0.72525, 0.64657, 0.80348, 0.53273, 0.86607, 0.36185, 0.92496, 0.24587, 0.92652, 0.13458, 0.85311, 0.22863, 0.73598, 0.08129, 0.75316, 0.08913, 0.56105, 0.23333, 0.45328, 0.3258, 0.68444, 0.41044, 0.60947, 0.45903, 0.56105, 0.65495, 0.46734, 0.79758, 0.31897, 0.72235, 0.39706, 0.55464, 0.51732, 0.84304, 0.23775], "triangles": [78, 2, 3, 78, 77, 2, 55, 77, 78, 76, 55, 75, 74, 76, 75, 27, 74, 73, 1, 2, 77, 0, 1, 77, 30, 0, 77, 55, 76, 30, 55, 30, 77, 29, 30, 76, 29, 76, 74, 28, 29, 74, 27, 28, 74, 54, 53, 79, 78, 4, 56, 3, 4, 78, 78, 54, 55, 53, 54, 78, 75, 55, 54, 75, 54, 79, 46, 75, 79, 74, 75, 46, 72, 46, 45, 73, 74, 46, 73, 46, 72, 25, 73, 72, 25, 72, 24, 26, 27, 73, 26, 73, 25, 53, 78, 52, 78, 56, 51, 52, 78, 51, 80, 52, 51, 81, 80, 51, 53, 52, 80, 79, 53, 80, 43, 81, 42, 80, 81, 43, 44, 80, 43, 45, 79, 80, 70, 42, 69, 43, 42, 70, 44, 45, 80, 21, 70, 69, 46, 79, 45, 70, 71, 44, 70, 44, 43, 45, 44, 71, 20, 21, 69, 72, 45, 71, 22, 70, 21, 71, 70, 22, 23, 71, 22, 24, 72, 71, 24, 71, 23, 58, 6, 7, 58, 7, 59, 57, 5, 6, 57, 6, 58, 58, 59, 48, 56, 5, 57, 4, 5, 56, 49, 58, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 48, 47, 82, 85, 49, 48, 85, 48, 82, 81, 50, 49, 81, 49, 85, 51, 50, 81, 41, 82, 40, 85, 82, 41, 42, 85, 41, 81, 85, 42, 69, 41, 68, 42, 41, 69, 19, 20, 69, 8, 9, 61, 60, 8, 61, 32, 60, 33, 59, 7, 8, 59, 8, 60, 31, 60, 32, 59, 60, 31, 47, 59, 31, 48, 59, 47, 83, 84, 31, 83, 31, 32, 47, 31, 84, 82, 47, 84, 39, 84, 38, 40, 84, 39, 82, 84, 40, 40, 39, 68, 68, 41, 40, 19, 69, 68, 62, 10, 11, 63, 11, 12, 63, 12, 13, 62, 11, 63, 61, 9, 10, 61, 10, 62, 34, 62, 63, 61, 62, 34, 35, 34, 63, 64, 63, 13, 64, 35, 63, 13, 14, 64, 36, 35, 64, 33, 61, 34, 60, 61, 33, 86, 34, 35, 86, 35, 36, 33, 34, 86, 37, 86, 36, 64, 14, 15, 83, 33, 86, 83, 86, 37, 32, 33, 83, 64, 37, 36, 65, 64, 15, 65, 37, 64, 38, 83, 37, 38, 37, 65, 65, 15, 16, 66, 38, 65, 66, 65, 16, 38, 84, 83, 39, 38, 66, 67, 39, 66, 66, 16, 17, 67, 66, 17, 18, 67, 17, 68, 39, 67, 68, 67, 18, 19, 68, 18], "vertices": [1, 4, 67.66, -15.99, 1, 1, 4, 91.4, -32.2, 1, 1, 4, 96.31, -63.64, 1, 4, 15, -51.55, 63.32, 0.00886, 14, 64.35, 80.18, 0.03434, 13, -25.76, 94.4, 0.4288, 4, 99.5, -84.09, 0.528, 4, 16, -77.76, 87.19, 0.00251, 15, -25.78, 66.01, 0.32278, 14, 87.99, 69.58, 0.45581, 13, -3.41, 107.52, 0.21891, 4, 16, -55.35, 84.06, 0.02289, 15, -3.28, 68.37, 0.5743, 14, 108.64, 60.32, 0.30834, 13, 16.1, 118.97, 0.09448, 5, 17, -43.41, 98.7, 5e-05, 16, -19.72, 76.38, 0.17, 15, 33.15, 69.48, 0.75629, 14, 140.71, 43.01, 0.06225, 13, 48.8, 135.07, 0.01141, 5, 17, -20.23, 83.99, 0.01973, 16, 7.11, 70.59, 0.4293, 15, 60.59, 70.33, 0.54578, 14, 164.86, 29.97, 0.00488, 13, 73.43, 147.18, 0.00031, 3, 17, 13.44, 71.06, 0.22538, 16, 43.18, 70.11, 0.59691, 15, 95.72, 78.53, 0.17771, 3, 17, 40.34, 60.73, 0.55216, 16, 72, 69.72, 0.40022, 15, 123.77, 85.09, 0.04762, 3, 17, 68.07, 39.8, 0.87799, 16, 105.25, 59.67, 0.11864, 15, 158.46, 83.34, 0.00337, 3, 17, 83.58, 11.34, 0.99264, 16, 129.65, 38.32, 0.00736, 14, 273.53, -35.19, 0, 3, 17, 94.87, -9.4, 1, 15, 208.27, 57.67, 0, 14, 286.26, -55.07, 0, 4, 17, 74.09, -27.75, 0.99998, 16, 134.26, -1.63, 2e-05, 15, 201.38, 30.82, 0, 14, 266.82, -74.84, 0, 4, 17, 51.8, -41.35, 0.99999, 16, 118.05, -22.1, 0, 15, 190.57, 7.04, 1e-05, 14, 245.54, -89.98, 0, 4, 17, 27.49, -56.19, 0.97809, 16, 100.37, -44.44, 0.02026, 15, 178.78, -18.89, 0.00118, 14, 222.34, -106.5, 0.00047, 4, 17, 4.87, -70, 0.85164, 16, 83.92, -65.21, 0.12474, 15, 167.82, -43.01, 0.01708, 14, 200.75, -121.87, 0.00654, 4, 17, -26.11, -88.91, 0.59142, 16, 61.4, -93.67, 0.2824, 15, 152.8, -76.05, 0.08916, 14, 171.18, -142.91, 0.03703, 4, 17, -49.56, -96.33, 0.44411, 16, 41.96, -108.74, 0.32234, 15, 137.56, -95.36, 0.15852, 14, 148.32, -151.97, 0.07503, 4, 17, -70.78, -103.05, 0.34952, 16, 24.37, -122.39, 0.32048, 15, 123.77, -112.83, 0.21424, 14, 127.62, -160.17, 0.11576, 4, 17, -122, -89, 0.15643, 16, -28.55, -126.92, 0.21995, 15, 73.5, -129.97, 0.3259, 14, 75.54, -149.76, 0.29771, 4, 17, -149.79, -77.58, 0.0826, 16, -58.57, -125.81, 0.13861, 15, 44.09, -136.12, 0.32502, 14, 47.01, -140.33, 0.45377, 4, 17, -176.8, -66.48, 0.04107, 16, -87.75, -124.74, 0.07915, 15, 15.51, -142.09, 0.27645, 14, 19.29, -131.15, 0.60332, 4, 17, -195.76, -49.66, 0.02044, 16, -111.36, -115.51, 0.04414, 15, -9.62, -138.82, 0.21342, 14, -0.81, -115.71, 0.72201, 4, 17, -222.14, -26.25, 0.00494, 16, -144.21, -102.67, 0.01393, 15, -44.6, -134.26, 0.11727, 14, -28.78, -94.22, 0.86386, 1, 13, 25.62, -83.11, 1, 1, 13, 0.08, -83.47, 1, 1, 4, -40.63, 13.32, 1, 1, 4, -15.21, 35.3, 1, 1, 4, 18.45, 15.2, 1, 1, 4, 46.48, -1.52, 1, 3, 17, -13.86, 18.05, 0.00487, 16, 35.9, 10.92, 0.97411, 15, 102.89, 19.34, 0.02102, 3, 17, -2.82, 14.84, 0.09891, 16, 47.37, 11.73, 0.88927, 15, 113.82, 22.88, 0.01182, 3, 17, 16.26, 10.49, 0.78307, 16, 66.77, 14.25, 0.21294, 15, 132.05, 29.99, 0.00398, 3, 17, 37.13, 4.64, 0.98367, 16, 88.38, 15.98, 0.016, 15, 152.6, 36.87, 0.00033, 4, 17, 38.5, -9.65, 1, 16, 94.61, 3.04, 0, 15, 161.77, 25.81, 0, 14, 230.05, -59.3, 0, 3, 17, 34.96, -18.73, 1, 15, 163.93, 16.31, 0, 14, 227.16, -68.6, 0, 4, 17, 11.69, -29.48, 0.98275, 16, 76.31, -24.83, 0.01683, 15, 150.71, -5.64, 0.0002, 14, 204.7, -80.96, 0.00022, 4, 17, -11.7, -32.94, 0.74269, 16, 55.56, -36.17, 0.24197, 15, 133.3, -21.65, 0.01075, 14, 181.62, -86.07, 0.0046, 4, 17, -41.54, -36.46, 0.38803, 16, 28.78, -49.8, 0.48258, 15, 110.58, -41.31, 0.09942, 14, 152.09, -91.68, 0.02996, 4, 17, -67.46, -31.53, 0.19849, 16, 2.75, -54.13, 0.43118, 15, 86.37, -51.79, 0.28805, 14, 125.89, -88.59, 0.08228, 4, 17, -89.31, -29.74, 0.1248, 16, -18.37, -60.01, 0.27859, 15, 67.28, -62.57, 0.42562, 14, 103.97, -88.34, 0.17099, 4, 17, -118.24, -20.59, 0.05672, 16, -48.67, -61.43, 0.12343, 15, 38.21, -71.24, 0.4574, 14, 74.47, -81.25, 0.36245, 4, 17, -141.22, -11.27, 0.02695, 16, -73.46, -60.63, 0.05739, 15, 13.96, -76.43, 0.35006, 14, 50.89, -73.58, 0.5656, 4, 17, -157.39, -6.02, 0.01575, 16, -90.45, -61.3, 0.03418, 15, -2.38, -81.17, 0.2536, 14, 34.38, -69.48, 0.69647, 4, 17, -179.53, 9, 0.00447, 16, -116.42, -54.86, 0.01113, 15, -29.13, -81.17, 0.12515, 14, 11.24, -56.05, 0.85926, 1, 13, 21.97, -40.03, 1, 2, 16, 17.18, 9.27, 0.94422, 15, 85.11, 13.23, 0.05578, 2, 16, -8.06, 9.49, 0.05864, 15, 60.56, 7.36, 0.94136, 3, 16, -31.66, 15.94, 0.00656, 15, 36.1, 7.94, 0.99343, 13, 76.95, 80.26, 0, 4, 16, -54.09, 28.04, 0.00346, 15, 11.42, 14.3, 0.93179, 14, 94.22, 6.18, 0.05722, 13, 51.86, 75.83, 0.00753, 4, 16, -73.53, 32.61, 0.00016, 15, -8.55, 14.05, 0.30484, 14, 76.82, 15.99, 0.6543, 13, 33.78, 67.35, 0.04069, 3, 15, -26.75, 1.65, 0.00196, 14, 54.86, 14.39, 0.92107, 13, 22.34, 48.53, 0.07696, 2, 14, 36.54, 14.04, 0.45116, 13, 12.01, 33.4, 0.54884, 2, 13, 1.31, 28.23, 0.888, 4, 29.59, -69.13, 0.112, 2, 13, -18.99, 28.23, 0.352, 4, 41.03, -52.37, 0.648, 4, 16, -65.57, 54.55, 0.00736, 15, -6.1, 37.26, 0.51142, 14, 90.58, 34.84, 0.39472, 13, 26.4, 89.49, 0.08651, 4, 16, -43.48, 49.76, 0.04129, 15, 16.49, 37.93, 0.80895, 14, 110.47, 24.08, 0.12608, 13, 46.69, 99.45, 0.02368, 4, 16, -20.45, 43.77, 0.15049, 15, 40.28, 37.66, 0.82947, 14, 130.91, 11.9, 0.01716, 13, 68.47, 109.04, 0.00288, 4, 17, -28.04, 52.62, 0.01145, 16, 10.64, 38.46, 0.54877, 15, 71.74, 39.98, 0.43978, 14, 159.28, -1.87, 0, 3, 17, 3.04, 43.49, 0.1924, 16, 42.95, 40.64, 0.68601, 15, 102.58, 49.87, 0.12159, 3, 17, 26.56, 37.53, 0.55385, 16, 67.09, 43.18, 0.40841, 15, 125.39, 58.15, 0.03775, 3, 17, 53.24, 15.77, 0.94923, 16, 99.65, 32, 0.04993, 15, 159.69, 55.13, 0.00084, 3, 17, 63.7, -11.99, 1, 15, 183.94, 38.03, 0, 14, 255.35, -59.86, 0, 3, 17, 44.05, -32.2, 0.99999, 15, 179.02, 10.27, 0, 14, 237.17, -81.4, 0, 4, 17, 14.11, -48.32, 0.95643, 16, 85.1, -41.67, 0.04004, 15, 163.29, -19.88, 0.00248, 14, 208.45, -99.59, 0.00105, 4, 17, -11.64, -57.12, 0.76032, 16, 63.98, -58.84, 0.19922, 15, 146.92, -41.62, 0.02928, 14, 183.38, -110.18, 0.01119, 4, 17, -36.69, -66.36, 0.54103, 16, 43.67, -76.17, 0.32038, 15, 131.38, -63.33, 0.09892, 14, 159.04, -121.16, 0.03968, 4, 17, -69.76, -70.98, 0.32999, 16, 14.24, -91.94, 0.33648, 15, 106.61, -85.72, 0.22227, 14, 126.38, -128.1, 0.11127, 4, 17, -108.15, -65.45, 0.1649, 16, -23.7, -100.03, 0.2383, 15, 71.74, -102.7, 0.33496, 14, 87.69, -125.29, 0.26184, 4, 17, -146.27, -45.49, 0.05828, 16, -66.37, -94.48, 0.10767, 15, 28.98, -107.58, 0.32938, 14, 48.26, -108.06, 0.50467, 4, 17, -178.12, -27.31, 0.0187, 16, -102.53, -88.44, 0.04059, 15, -7.57, -110.42, 0.2172, 14, 15.21, -92.18, 0.72351, 1, 13, 37.99, -62.23, 1, 1, 13, 5.28, -62.67, 1, 1, 4, -12.88, -6.95, 1, 1, 13, 0.42, -8.75, 1, 1, 4, 18.96, -10.49, 1, 1, 4, 62.61, -42.97, 1, 2, 13, 1.75, 71.25, 0.712, 4, 64.87, -93.75, 0.288, 4, 17, -164.64, 49.28, 0.00011, 16, -116.38, -11.92, 0.00024, 15, -39.42, -39.48, 0.02162, 14, 23.26, -14.83, 0.97804, 4, 17, -134.1, 39.96, 0.00097, 16, -84.5, -10.1, 0.00111, 15, -8.92, -30.04, 0.17894, 14, 54.38, -21.97, 0.81898, 4, 17, -115.27, 35.4, 0.00171, 16, -65.25, -7.86, 0.00168, 15, 9.23, -23.24, 0.55346, 14, 73.49, -25.19, 0.44315, 4, 17, -63.84, 2.06, 0.04041, 16, -5.47, -21.36, 0.43543, 15, 70.5, -21.96, 0.49555, 14, 127.13, -54.83, 0.02861, 3, 17, -7.05, -10.4, 0.67219, 16, 52.13, -13.41, 0.32747, 14, 184.67, -63.25, 0.00035, 4, 17, -36.96, -3.8, 0.09915, 16, 21.78, -17.57, 0.87508, 15, 96.03, -11.72, 0.02065, 14, 154.36, -58.78, 0.00512, 4, 17, -90.65, 18.84, 0.00898, 16, -36.43, -14.89, 0.02669, 15, 38.89, -23.13, 0.86654, 14, 99.21, -39.98, 0.09779, 1, 17, 19.25, -9.36, 1], "hull": 31, "edges": [18, 20, 24, 26, 38, 40, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 20, 22, 22, 24, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 2, 0, 0, 60, 2, 4, 4, 6], "width": 282, "height": 283}}, "chiguang2": {"images/chiguang2": {"x": 46.95, "y": -19, "rotation": -54.56, "width": 252, "height": 294}}, "guang1": {"images/guang1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.57, -81.42, 9.33, -34.52, 58.18, -23.86, 68.42, -70.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 50}}, "guang2": {"images/guang2": {"x": 49.23, "y": 28.47, "rotation": 16.26, "width": 91, "height": 88}}, "mao1": {"images/mao1": {"type": "mesh", "uvs": [0.08436, 0.04179, 0, 0.18916, 0, 0.37951, 0.07736, 0.54837, 0.23136, 0.65582, 0.32936, 0.77249, 0.45536, 0.8646, 0.67936, 0.90758, 0.80886, 0.87995, 0.93836, 0.76328, 1, 0.67424, 0.91736, 0.59135, 0.79836, 0.4931, 0.78786, 0.36109, 0.70036, 0.26591, 0.56736, 0.17074, 0.47636, 0.07863, 0.34686, 0.01416, 0.19986, 0, 0.19286, 0.15539, 0.27686, 0.27205, 0.37486, 0.37644, 0.47636, 0.48389, 0.58836, 0.57293, 0.69336, 0.66503, 0.81236, 0.74793], "triangles": [19, 0, 18, 19, 18, 17, 1, 0, 19, 16, 20, 19, 16, 19, 17, 20, 16, 15, 21, 20, 15, 20, 2, 1, 20, 1, 19, 3, 2, 20, 14, 22, 21, 14, 21, 15, 22, 14, 13, 23, 22, 13, 3, 20, 21, 4, 3, 21, 4, 21, 22, 5, 4, 22, 12, 23, 13, 24, 23, 12, 24, 12, 11, 25, 24, 11, 9, 25, 11, 10, 9, 11, 5, 22, 23, 6, 5, 23, 6, 23, 24, 25, 7, 24, 8, 25, 9, 7, 6, 24, 25, 8, 7], "vertices": [1, 25, -3, -2.67, 1, 2, 26, -15.89, -8.18, 0.01131, 25, 2.19, -10.51, 0.98869, 3, 27, -28.15, -11.7, 0.00092, 26, -7.42, -14.96, 0.25152, 25, 11.6, -15.91, 0.74756, 3, 27, -18.54, -15.61, 0.04869, 26, 2.52, -17.95, 0.62366, 25, 21.87, -17.35, 0.32765, 3, 27, -8.78, -14.33, 0.31649, 26, 12.11, -15.76, 0.62869, 25, 31.01, -13.73, 0.05483, 3, 27, -0.6, -15.43, 0.74903, 26, 20.36, -16.09, 0.25008, 25, 39.22, -12.79, 0.00089, 2, 27, 7.56, -14.55, 0.96689, 26, 28.4, -14.45, 0.03311, 1, 27, 17.1, -8.2, 1, 1, 27, 20.47, -2.45, 1, 1, 27, 20.19, 6.83, 1, 2, 27, 18.68, 12.57, 0.99665, 26, 36.94, 13.59, 0.00335, 2, 27, 12.41, 12.88, 0.95247, 26, 30.67, 13.32, 0.04753, 3, 27, 4.25, 12.5, 0.61353, 26, 22.58, 12.17, 0.38431, 25, 37.09, 15.47, 0.00216, 3, 27, -1.52, 17.35, 0.19803, 26, 16.37, 16.46, 0.76658, 25, 30.31, 18.77, 0.03539, 3, 27, -8.47, 17.98, 0.04796, 26, 9.4, 16.43, 0.81392, 25, 23.42, 17.68, 0.13812, 3, 27, -16.99, 16.97, 0.00018, 26, 1.01, 14.63, 0.49438, 25, 15.41, 14.61, 0.50544, 2, 26, -5.93, 14.36, 0.15067, 25, 8.59, 13.28, 0.84933, 2, 26, -12.84, 11.6, 0.01192, 25, 2.18, 9.5, 0.98808, 1, 25, -2.18, 3.53, 1, 1, 25, 5.32, -1.19, 1, 1, 25, 13.18, -0.86, 1, 2, 27, -15.25, 1.9, 0.0001, 26, 4.16, -0.21, 0.9999, 2, 27, -7.32, 1.29, 0.00046, 26, 12.11, -0.07, 0.99954, 2, 27, 0.22, 1.79, 0.81201, 26, 19.57, 1.13, 0.18799, 2, 27, 7.65, 1.91, 0.98643, 26, 26.95, 1.95, 0.01357, 2, 27, 15.18, 2.91, 0.99929, 26, 34.36, 3.64, 0.00071], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 50, "height": 57}}, "re1": {"images/re1": {"type": "mesh", "uvs": [0.0376, 0.23316, 0.01337, 0.38482, 0.12968, 0.61349, 0.26052, 0.80015, 0.45679, 0.99849, 0.58044, 1, 0.73098, 0.91905, 0.86567, 0.75883, 0.97263, 0.51468, 0.9964, 0.19043, 0.88944, 0.04928, 0.5329, 0, 0.2239, 0.0798, 0.20409, 0.29722, 0.44178, 0.42692, 0.66362, 0.53374, 0.80624, 0.61003], "triangles": [6, 5, 15, 15, 5, 4, 4, 3, 15, 6, 16, 7, 6, 15, 16, 3, 14, 15, 3, 2, 14, 7, 16, 8, 2, 13, 14, 16, 15, 8, 14, 11, 15, 8, 15, 9, 9, 15, 10, 2, 1, 13, 15, 11, 10, 13, 12, 14, 14, 12, 11, 1, 0, 13, 13, 0, 12], "vertices": [2, 30, 11.49, 0.26, 1, 29, 21.76, 0.73, 0, 2, 30, 10.35, 4.25, 0.85054, 29, 20.35, 4.63, 0.14946, 2, 30, 5.02, 8.59, 0.19193, 29, 14.74, 8.6, 0.80807, 2, 30, -0.17, 11.75, 0.00152, 29, 9.34, 11.4, 0.99848, 1, 29, 2.29, 13.65, 1, 1, 29, -0.55, 12.15, 1, 1, 29, -2.94, 8.36, 1, 1, 29, -3.94, 2.88, 1, 2, 30, -13.78, -2.98, 0.0773, 29, -3.23, -4.24, 0.9227, 2, 30, -10.68, -11.19, 0.48211, 29, 0.42, -12.22, 0.51789, 2, 30, -6.57, -13.5, 0.64002, 29, 4.69, -14.23, 0.35998, 2, 30, 2.41, -10.83, 0.97022, 29, 13.47, -10.96, 0.02978, 1, 30, 8.81, -5.52, 1, 2, 30, 6.83, 0.03, 0.99939, 29, 17.13, 0.18, 0.00061, 1, 29, 10.03, 0.29, 1, 1, 29, 3.58, 0.06, 1, 2, 30, -10.92, 1.17, 0, 29, -0.66, 0.1, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 26, "height": 27}}, "re2": {"images/re2": {"type": "mesh", "uvs": [0.99454, 0.06696, 0.75137, 0, 0.51754, 0, 0.31801, 0, 0.1746, 0.04869, 0.03431, 0.16565, 0, 0.51289, 0.1216, 0.75779, 0.30554, 0.95151, 0.54872, 1, 0.70772, 0.90765, 0.87295, 0.71393, 0.95713, 0.50193, 1, 0.31917, 0.87919, 0.16931, 0.74513, 0.31552, 0.56431, 0.40324, 0.36166, 0.48, 0.23384, 0.6189], "triangles": [8, 17, 9, 9, 17, 10, 17, 16, 10, 7, 18, 8, 8, 18, 17, 10, 16, 11, 7, 6, 18, 16, 15, 11, 18, 6, 17, 6, 5, 17, 5, 4, 17, 4, 3, 17, 16, 3, 2, 16, 17, 3, 11, 15, 12, 12, 15, 13, 13, 15, 14, 16, 2, 15, 14, 0, 13, 15, 1, 14, 15, 2, 1, 14, 1, 0], "vertices": [2, 28, 15.66, 1.9, 0.99993, 24, 29.45, 8.77, 7e-05, 2, 28, 9.32, 7.56, 0.99971, 24, 21.47, 11.68, 0.00029, 2, 28, 2.33, 11.35, 0.80817, 24, 13.58, 12.63, 0.19183, 2, 28, -3.63, 14.59, 0.41719, 24, 6.84, 13.44, 0.58281, 2, 28, -8.59, 15.68, 0.20793, 24, 1.84, 12.62, 0.79207, 2, 28, -14.4, 14.98, 0.07936, 24, -3.31, 9.82, 0.92064, 2, 28, -20.23, 6.69, 0.00024, 24, -5.66, -0.04, 0.99976, 1, 24, -2.4, -7.58, 1, 1, 24, 3.14, -13.91, 1, 1, 24, 11.18, -16.29, 1, 2, 28, -4.55, -14.86, 0.00399, 24, 16.86, -14.27, 0.99601, 2, 28, 3.06, -12.61, 0.18814, 24, 23.11, -9.36, 0.81186, 2, 28, 8.51, -8.57, 0.6435, 24, 26.69, -3.6, 0.3565, 2, 28, 12.33, -4.61, 0.93843, 24, 28.76, 1.49, 0.06157, 1, 28, 10.79, 1.17, 1, 2, 28, 4.76, -0.38, 0.98707, 24, 20.17, 2.62, 0.01293, 2, 28, -1.85, 0.32, 0.24001, 24, 13.77, 0.83, 0.75999, 1, 24, 6.66, -0.56, 1, 1, 24, 1.86, -4.04, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 34, "height": 29}}, "shangya": {"images/shangya": {"type": "mesh", "uvs": [0.00913, 0.29664, 0.0509, 0.25408, 0.16715, 0.14151, 0.3527, 0.24369, 0.51768, 0.31223, 0.61048, 0.28587, 0.69168, 0.12769, 0.85262, 0.08512, 0.99302, 0.2943, 0.88902, 0.50703, 0.76596, 0.54603, 0.71049, 0.50703, 0.65329, 0.56376, 0.64289, 0.64885, 0.66369, 0.88994, 0.60476, 0.94312, 0.52156, 0.74812, 0.49383, 0.5283, 0.27889, 0.46448, 0.21129, 0.52476, 0.23383, 0.73394, 0.26676, 0.9573, 0.16796, 1, 0.12636, 0.78003, 0.07685, 0.51249, 0.04046, 0.47297, 0.00658, 0.43407, 0.51808, 0.42548, 0.62382, 0.42548, 0.69835, 0.31203, 0.85262, 0.2943, 0.29448, 0.36521, 0.09862, 0.36876, 0.18702, 0.3404, 0.047, 0.3576], "triangles": [9, 30, 8, 30, 7, 8, 9, 10, 30, 30, 6, 7, 22, 20, 21, 22, 23, 20, 23, 24, 19, 23, 19, 20, 19, 32, 33, 32, 19, 24, 19, 33, 18, 24, 25, 32, 18, 33, 31, 2, 33, 32, 3, 31, 2, 32, 1, 2, 31, 33, 2, 32, 25, 34, 32, 34, 1, 25, 26, 34, 26, 0, 34, 34, 0, 1, 14, 15, 13, 15, 16, 13, 16, 17, 13, 12, 13, 27, 27, 13, 17, 12, 27, 28, 12, 28, 11, 18, 31, 17, 31, 3, 17, 27, 3, 4, 27, 17, 3, 30, 10, 29, 28, 29, 11, 10, 11, 29, 27, 5, 28, 27, 4, 5, 28, 5, 29, 5, 6, 29, 29, 6, 30], "vertices": [1, 64, 1.29, 4.62, 1, 1, 64, 2.26, 2.75, 1, 1, 64, 4.81, -2.44, 1, 2, 65, 3.09, 9.72, 0.45194, 64, 2.69, -10.82, 0.54806, 1, 65, 1.69, 2.27, 1, 1, 65, 2.34, -1.89, 1, 2, 65, 5.87, -5.49, 0.792, 80, -1.55, 7.7, 0.208, 2, 65, 6.92, -12.72, 0.232, 80, 0.91, 0.82, 0.768, 2, 65, 2.41, -19.11, 0.232, 80, -2.24, -6.33, 0.768, 2, 65, -2.34, -14.5, 0.232, 80, -7.81, -2.76, 0.768, 2, 65, -3.28, -8.98, 0.608, 80, -9.83, 2.47, 0.392, 2, 65, -2.46, -6.47, 0.792, 80, -9.53, 5.09, 0.208, 1, 65, -3.75, -3.91, 1, 1, 65, -5.63, -3.48, 1, 1, 65, -10.92, -4.49, 1, 1, 65, -12.13, -1.86, 1, 2, 65, -7.89, 1.95, 0.99975, 64, -8.29, -18.59, 0.00025, 1, 65, -3.08, 3.27, 1, 2, 65, -1.82, 12.96, 0.25901, 64, -2.22, -7.58, 0.74099, 1, 64, -3.59, -4.55, 1, 2, 65, -7.78, 14.9, 0.00132, 64, -8.17, -5.64, 0.99868, 1, 64, -13.07, -7.2, 1, 1, 64, -14.07, -2.76, 1, 1, 64, -9.26, -0.82, 1, 1, 64, -3.41, 1.5, 1, 1, 64, -2.57, 3.15, 1, 1, 64, -1.73, 4.69, 1, 1, 65, -0.8, 2.22, 1, 1, 65, -0.73, -2.54, 1, 2, 65, 1.82, -5.86, 0.792, 80, -5.45, 6.54, 0.208, 2, 65, 2.32, -12.79, 0.232, 80, -3.59, -0.16, 0.768, 2, 65, 0.37, 12.3, 0.30909, 64, -0.02, -8.24, 0.69091, 1, 64, -0.23, 0.57, 1, 1, 64, 0.45, -3.4, 1, 1, 64, -0.02, 2.89, 1], "hull": 27, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 52, 48, 50, 50, 52, 0, 2, 2, 4], "width": 45, "height": 22}}, "shangzui": {"images/shangzui": {"type": "mesh", "uvs": [0.15451, 0.07214, 0.24346, 0.01944, 0.35966, 0.074, 0.41827, 0.23197, 0.48784, 0.30485, 0.57555, 0.35627, 0.67456, 0.38609, 0.81501, 0.46238, 0.90625, 0.55309, 0.88236, 0.69438, 0.83957, 0.83768, 0.78166, 0.99999, 0.67843, 0.90938, 0.52675, 0.73685, 0.46901, 0.70773, 0.41584, 0.76038, 0.34747, 0.76748, 0.26775, 0.75164, 0.19786, 0.72539, 0.16451, 0.75044, 0.1358, 0.7645, 0.09886, 0.75229, 0.03712, 0.64745, 0.03942, 0.57486, 0.04168, 0.50376, 0.05744, 0.41831, 0.09368, 0.23876, 0.23209, 0.43212, 0.31506, 0.41402, 0.0947, 0.45691, 0.09189, 0.51074, 0.1975, 0.24584, 0.16109, 0.49362, 0.17437, 0.57771, 0.27034, 0.62718, 0.36738, 0.61673, 0.49528, 0.58059, 0.5383, 0.46915, 0.59418, 0.63473, 0.76882, 0.59002, 0.72863, 0.75595, 0.10661, 0.6441, 0.13986, 0.25373, 0.18458, 0.65031, 0.32562, 0.25042, 0.24096, 0.50691, 0.30037, 0.48004, 0.42337, 0.44357, 0.63472, 0.50942], "triangles": [39, 7, 8, 9, 39, 8, 40, 38, 39, 40, 39, 9, 10, 40, 9, 40, 13, 38, 12, 40, 10, 12, 13, 40, 11, 12, 10, 38, 13, 36, 31, 0, 1, 44, 31, 1, 2, 44, 1, 42, 26, 0, 3, 44, 2, 31, 42, 0, 28, 44, 3, 15, 35, 14, 39, 48, 7, 38, 48, 39, 36, 37, 38, 35, 46, 47, 38, 37, 48, 35, 47, 36, 48, 6, 7, 47, 3, 4, 47, 28, 3, 37, 4, 5, 47, 4, 37, 48, 5, 6, 37, 5, 48, 36, 47, 37, 13, 14, 36, 25, 26, 42, 29, 25, 42, 18, 43, 34, 34, 46, 35, 16, 34, 35, 17, 34, 16, 14, 35, 36, 17, 18, 34, 16, 35, 15, 22, 23, 41, 19, 41, 43, 19, 43, 18, 21, 22, 41, 20, 21, 41, 19, 20, 41, 23, 30, 41, 23, 24, 30, 28, 27, 44, 32, 42, 31, 32, 29, 42, 46, 27, 28, 31, 27, 32, 45, 27, 46, 32, 27, 45, 30, 24, 29, 30, 29, 32, 33, 32, 45, 47, 46, 28, 34, 45, 46, 33, 45, 34, 33, 41, 30, 33, 30, 32, 43, 33, 34, 41, 33, 43, 44, 27, 31, 24, 25, 29], "vertices": [2, 67, 19.98, -6.28, 0.12282, 35, -0.99, 6.23, 0.87718, 3, 63, 32.3, -5.65, 0.00055, 66, 23.66, 26.79, 0.00166, 35, 2.61, -1.19, 0.99779, 5, 63, 28.84, -15.46, 0.07051, 65, 44.67, 3.01, 0.00064, 67, 20.12, -23.51, 0.00274, 66, 20.2, 16.98, 0.11485, 35, -0.85, -11.01, 0.81127, 5, 63, 18.49, -20.54, 0.17949, 65, 34.32, -2.07, 0.01977, 67, 9.77, -28.6, 0.02217, 66, 9.85, 11.9, 0.39518, 35, -11.19, -16.09, 0.38339, 6, 63, 13.77, -26.46, 0.1145, 65, 29.6, -7.99, 0.02441, 67, 5.05, -34.51, 0.00443, 66, 5.13, 5.98, 0.73204, 35, -15.91, -22.01, 0.12438, 80, 22.2, 9.97, 0.00025, 3, 66, 1.85, -1.44, 0.99145, 35, -19.2, -29.42, 0.00021, 80, 20.45, 2.04, 0.00833, 2, 66, 0.01, -9.79, 0.71031, 80, 20.31, -6.5, 0.28969, 2, 66, -4.84, -21.66, 0.38371, 80, 17.9, -19.1, 0.61629, 2, 66, -10.71, -29.41, 0.28181, 80, 13.69, -27.87, 0.71819, 2, 66, -20.06, -27.55, 0.19026, 80, 4.15, -27.89, 0.80974, 2, 66, -29.58, -24.1, 0.0626, 80, -5.86, -26.4, 0.9374, 1, 80, -17.36, -23.93, 1, 1, 80, -13.37, -14.19, 1, 3, 65, 1.14, -11.69, 0.24, 66, -23.32, 2.28, 0.29792, 80, -4.96, 0.69, 0.46208, 5, 63, -12.84, -25.29, 0.00272, 65, 2.99, -6.81, 0.45968, 66, -21.48, 7.15, 0.25985, 35, -42.53, -20.83, 5e-05, 80, -4.12, 5.84, 0.27769, 3, 65, -0.55, -2.4, 0.19882, 66, -25.02, 11.57, 0.72118, 80, -8.47, 9.46, 0.08, 5, 63, -16.94, -15.14, 0.01912, 64, -1.5, -17.2, 0.01813, 65, -1.11, 3.34, 0.69838, 66, -25.58, 17.3, 0.10437, 80, -10.15, 14.97, 0.16, 5, 63, -15.99, -8.43, 0.07681, 64, -0.56, -10.49, 0.09809, 65, -0.17, 10.05, 0.75864, 66, -24.63, 24.01, 6e-05, 80, -10.56, 21.74, 0.06639, 3, 63, -14.35, -2.53, 0.13993, 64, 1.08, -4.59, 0.45818, 65, 1.48, 15.95, 0.40188, 3, 63, -16.05, 0.25, 0.06576, 64, -0.61, -1.82, 0.63641, 65, -0.22, 18.72, 0.29783, 2, 64, -1.58, 0.58, 0.776, 65, -1.18, 21.12, 0.224, 1, 64, -0.82, 3.69, 1, 3, 63, -9.41, 11.05, 0.28445, 64, 6.02, 8.98, 0.65646, 67, -18.14, 3, 0.05909, 3, 63, -4.62, 10.93, 0.13302, 64, 10.81, 8.86, 0.75959, 67, -13.35, 2.88, 0.10739, 3, 63, 0.07, 10.81, 0.33939, 64, 15.51, 8.75, 0.52807, 67, -8.65, 2.76, 0.13254, 3, 63, 5.73, 9.57, 0.20569, 64, 21.17, 7.51, 0.3238, 67, -2.99, 1.52, 0.47052, 4, 63, 17.63, 6.71, 0.00022, 67, 8.9, -1.34, 0.66461, 66, 8.99, 39.15, 0.00553, 35, -12.06, 11.17, 0.32964, 6, 63, 5.05, -5.11, 0.66745, 65, 20.87, 13.37, 0.04546, 67, -3.68, -13.16, 0.1169, 66, -3.59, 27.33, 0.07905, 35, -24.64, -0.65, 0.08766, 80, 9.41, 29.16, 0.00348, 6, 63, 6.35, -12.06, 0.41362, 65, 22.17, 6.42, 0.10836, 67, -2.38, -20.11, 0.05486, 66, -2.29, 20.38, 0.24758, 35, -23.34, -7.6, 0.15557, 80, 12.06, 22.61, 0.02001, 4, 63, 3.23, 6.41, 0.62368, 67, -5.49, -1.65, 0.37609, 66, -5.41, 38.85, 8e-05, 35, -26.46, 10.86, 0.00015, 2, 63, -0.32, 6.59, 0.83948, 67, -9.05, -1.46, 0.16052, 5, 63, 17.3, -2.02, 0.25869, 65, 33.12, 16.46, 0.00175, 67, 8.57, -10.07, 0.31299, 66, 8.66, 30.42, 0.04067, 35, -12.39, 2.44, 0.38589, 1, 63, 0.9, 0.79, 1, 6, 63, -4.64, -0.41, 0.92847, 65, 11.19, 18.07, 0.06461, 67, -13.36, -8.46, 0.00406, 66, -13.27, 32.03, 0.00268, 35, -34.32, 4.05, 7e-05, 80, -1.01, 31.85, 0.0001, 6, 63, -7.78, -8.52, 0.20413, 64, 7.66, -10.58, 0.08568, 65, 8.05, 9.96, 0.47526, 66, -16.42, 23.92, 0.01389, 35, -37.47, -4.06, 0.00198, 80, -2.48, 23.28, 0.21905, 6, 63, -6.96, -16.66, 0.04868, 64, 8.47, -18.72, 0.0071, 65, 8.86, 1.82, 0.37665, 66, -15.6, 15.78, 0.31549, 35, -36.65, -12.2, 0.00223, 80, -0.07, 15.46, 0.24985, 5, 63, -4.41, -27.36, 0.01805, 65, 11.41, -8.89, 0.31832, 66, -13.05, 5.08, 0.46395, 35, -34.1, -22.91, 0.00179, 80, 4.55, 5.47, 0.1979, 5, 63, 3, -30.86, 0.02693, 65, 18.82, -12.39, 0.03408, 66, -5.64, 1.58, 0.69998, 35, -26.69, -26.41, 0.00592, 80, 12.51, 3.51, 0.23309, 3, 65, 7.97, -17.25, 0.24, 66, -16.5, -3.29, 0.29792, 80, 2.83, -3.4, 0.46208, 2, 66, -13.32, -17.91, 0.29032, 80, 8.85, -17.11, 0.70968, 2, 66, -24.32, -14.7, 0.07731, 80, -2.57, -16.15, 0.92269, 4, 63, -9.1, 5.22, 0.43806, 64, 6.33, 3.15, 0.51899, 65, 6.72, 23.69, 0.00858, 67, -17.83, -2.83, 0.03437, 5, 63, 16.7, 2.82, 0.16929, 65, 32.53, 21.29, 6e-05, 67, 7.97, -5.23, 0.51192, 66, 8.06, 35.26, 0.01255, 35, -12.99, 7.27, 0.30618, 5, 63, -9.41, -1.34, 0.29673, 64, 6.02, -3.4, 0.35907, 65, 6.41, 17.14, 0.34278, 67, -18.14, -9.39, 0.0003, 66, -18.05, 31.1, 0.00113, 6, 63, 17.16, -12.78, 0.2243, 65, 32.98, 5.7, 0.01638, 67, 8.43, -20.83, 0.07499, 66, 8.52, 19.66, 0.22695, 35, -12.53, -8.33, 0.4572, 80, 22.8, 24.05, 0.00019, 6, 63, 0.12, -5.93, 0.78238, 65, 15.95, 12.55, 0.1105, 67, -8.61, -13.98, 0.00977, 66, -8.52, 26.51, 0.05971, 35, -29.57, -1.47, 0.03053, 80, 4.74, 27.38, 0.00711, 6, 63, 1.97, -10.89, 0.56265, 65, 17.8, 7.58, 0.16579, 67, -6.76, -18.94, 0.01969, 66, -6.67, 21.55, 0.157, 35, -27.72, -6.44, 0.06922, 80, 7.54, 22.88, 0.02565, 6, 63, 4.54, -21.19, 0.21911, 65, 20.36, -2.71, 0.13031, 67, -4.19, -29.24, 0.00467, 66, -4.1, 11.25, 0.47553, 35, -25.15, -16.73, 0.07749, 80, 12.1, 13.3, 0.09288, 4, 63, 0.46, -39, 0, 65, 16.29, -20.53, 6e-05, 66, -8.18, -6.56, 0.848, 80, 11.64, -4.97, 0.15193], "hull": 27, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 52, 18, 20, 20, 22, 24, 26, 48, 50, 64, 62, 62, 2, 50, 52, 4, 6, 2, 4, 0, 2, 44, 46, 46, 48], "width": 84, "height": 66}}, "shen": {"images/shen": {"type": "mesh", "uvs": [0.44458, 0, 0.56847, 0.00506, 0.62972, 0.04704, 0.68818, 0.10526, 0.73412, 0.16214, 0.75082, 0.20818, 0.79815, 0.23661, 0.85244, 0.26505, 0.89002, 0.29484, 0.92621, 0.37744, 0.94013, 0.45191, 0.93735, 0.53045, 0.94517, 0.57773, 0.93432, 0.63138, 0.96144, 0.65512, 0.98404, 0.70788, 0.98675, 0.8011, 1, 0.83803, 0.99579, 0.89343, 0.99339, 0.93944, 0.96973, 0.94892, 0.90189, 0.94811, 0.87007, 0.95069, 0.8294, 0.90683, 0.84532, 0.86212, 0.86711, 0.84771, 0.87168, 0.81609, 0.87778, 0.78792, 0.85187, 0.73554, 0.82748, 0.70737, 0.80412, 0.69353, 0.78634, 0.67624, 0.72753, 0.67722, 0.68519, 0.65803, 0.64285, 0.63772, 0.61037, 0.64957, 0.55527, 0.65408, 0.45779, 0.64098, 0.33256, 0.61122, 0.27328, 0.5796, 0.17768, 0.48289, 0.11615, 0.40141, 0.14854, 0.16857, 0.23161, 0.05078, 0.31265, 0.02039, 0.82381, 0.56266, 0.88307, 0.65274, 0.92196, 0.71399, 0.91641, 0.78786, 0.92134, 0.8724, 0.91831, 0.82078, 0.71516, 0.43293, 0.57114, 0.35477, 0.46351, 0.28694, 0.36345, 0.2309, 0.26794, 0.17486, 0.63027, 0.38722, 0.76216, 0.50225], "triangles": [18, 20, 49, 25, 50, 49, 20, 21, 49, 49, 24, 25, 49, 23, 24, 49, 22, 23, 21, 22, 49, 18, 49, 17, 18, 19, 20, 14, 47, 46, 47, 14, 15, 47, 27, 28, 48, 27, 47, 48, 47, 15, 16, 48, 15, 50, 48, 16, 27, 48, 50, 26, 27, 50, 17, 49, 50, 17, 50, 16, 50, 25, 26, 10, 45, 57, 11, 45, 10, 45, 11, 12, 12, 46, 45, 33, 34, 57, 13, 46, 12, 45, 33, 57, 45, 32, 33, 46, 31, 45, 31, 32, 45, 30, 31, 46, 29, 30, 46, 14, 46, 13, 28, 29, 46, 47, 28, 46, 37, 52, 56, 56, 36, 37, 55, 43, 44, 42, 43, 55, 54, 44, 0, 55, 44, 54, 53, 0, 1, 53, 1, 2, 53, 2, 3, 52, 53, 3, 4, 52, 3, 54, 0, 53, 5, 52, 4, 55, 41, 42, 54, 41, 55, 54, 40, 41, 53, 39, 40, 53, 40, 54, 38, 39, 53, 52, 38, 53, 37, 38, 52, 56, 52, 5, 35, 36, 56, 34, 35, 56, 6, 51, 56, 6, 56, 5, 51, 6, 7, 51, 7, 8, 51, 8, 9, 57, 51, 9, 10, 57, 9, 34, 56, 51, 34, 51, 57], "vertices": [2, 5, -76.95, 81.25, 0.0115, 4, 93.07, -29.88, 0.9885, 2, 5, -46.85, 88.67, 0.07577, 4, 74.53, -54.73, 0.92423, 2, 5, -29.13, 82.6, 0.15154, 4, 56.99, -61.29, 0.84846, 2, 5, -10.91, 72.33, 0.30926, 4, 36.39, -64.92, 0.69074, 2, 5, 4.21, 61.51, 0.50665, 4, 17.84, -66.17, 0.49335, 2, 5, 11.53, 51.32, 0.69126, 4, 5.72, -62.94, 0.30874, 2, 5, 24.93, 47.62, 0.88407, 4, -6.99, -68.59, 0.11593, 2, 5, 40.01, 44.41, 0.97281, 4, -20.68, -75.68, 0.02719, 2, 5, 51.17, 39.69, 0.99462, 4, -32.3, -79.12, 0.00538, 1, 5, 65.8, 21.84, 1, 2, 5, 74.5, 4.44, 0.8996, 58, 64.5, -30.98, 0.1004, 2, 5, 79.49, -15.13, 0.28508, 58, 45.96, -22.97, 0.71492, 3, 5, 84.76, -26.25, 0.06291, 58, 33.93, -20.36, 0.92781, 57, 71.93, -6.16, 0.00928, 3, 5, 86.02, -40.24, 0.00228, 58, 22.08, -12.81, 0.81667, 57, 58.15, -3.45, 0.18105, 2, 58, 13.93, -16.89, 0.38807, 57, 52.04, -10.23, 0.61193, 1, 57, 38.48, -15.88, 1, 2, 57, 14.53, -16.56, 0.96672, 56, 35.83, -15.62, 0.03328, 2, 57, 5.04, -19.87, 0.75259, 56, 26.56, -19.52, 0.24741, 2, 57, -9.2, -18.82, 0.24232, 56, 12.29, -19.36, 0.75768, 2, 57, -21.03, -18.22, 0.05007, 56, 0.45, -19.49, 0.94993, 2, 57, -23.46, -12.3, 0.02453, 56, -2.35, -13.74, 0.97547, 1, 56, -3.2, 3.2, 1, 1, 56, -4.36, 11.1, 1, 2, 57, -12.65, 22.78, 0.00242, 56, 6.25, 21.95, 0.99758, 2, 57, -1.15, 18.8, 0.12408, 56, 17.97, 18.69, 0.87592, 3, 58, -23.56, 23.12, 0.00362, 57, 2.55, 13.35, 0.38986, 56, 22.01, 13.49, 0.60652, 3, 58, -16.41, 19.09, 0.05299, 57, 10.68, 12.21, 0.80688, 56, 30.19, 12.85, 0.14012, 3, 58, -10.23, 15.03, 0.27609, 57, 17.91, 10.69, 0.71481, 56, 37.51, 11.78, 0.0091, 2, 4, -120.46, -7.38, 0.0001, 58, 4.67, 16.15, 0.9999, 2, 4, -111.04, -6.43, 0.00181, 58, 13.64, 19.19, 0.99819, 3, 5, 59.25, -64.69, 0.00296, 4, -104.81, -3.61, 0.00601, 58, 19.08, 23.33, 0.99103, 3, 5, 53.73, -61.67, 0.01522, 4, -98.63, -2.45, 0.01494, 58, 24.84, 25.85, 0.96985, 3, 5, 39.69, -66.03, 0.0797, 4, -90.55, 9.84, 0.05391, 58, 29.96, 39.63, 0.86639, 3, 5, 28.15, -64.26, 0.15189, 4, -80.51, 15.8, 0.1071, 58, 38.41, 47.68, 0.74101, 3, 5, 16.53, -62.21, 0.23297, 4, -70.23, 21.6, 0.2289, 58, 47.14, 55.64, 0.53813, 3, 5, 9.58, -67.41, 0.24291, 4, -68.17, 30.02, 0.3399, 58, 47.26, 64.31, 0.41719, 3, 5, -3.32, -72.38, 0.21553, 4, -61.36, 42.05, 0.48449, 58, 51.21, 77.56, 0.29998, 3, 5, -27.65, -75.97, 0.11845, 4, -44.84, 60.28, 0.72776, 58, 63.23, 99.02, 0.15378, 3, 5, -59.85, -77.39, 0.02395, 4, -20.87, 81.82, 0.92178, 58, 81.77, 125.38, 0.05427, 3, 5, -76.35, -73.74, 0.00658, 4, -5.8, 89.48, 0.96259, 58, 94.74, 136.22, 0.03083, 2, 4, 28.2, 95.2, 0.995, 58, 126.6, 149.41, 0.005, 2, 4, 54.17, 96.1, 0.99978, 58, 151.71, 156.09, 0.00022, 1, 4, 99.03, 55.67, 1, 1, 4, 112.32, 21.45, 1, 1, 4, 107.34, 0.31, 1, 2, 4, -79.81, -26.64, 0.00234, 58, 48.6, 6.48, 0.99766, 2, 4, -107.28, -25.82, 5e-05, 58, 21.64, 1.13, 0.99995, 2, 58, 3.44, -2.19, 0.06728, 57, 36.91, -0.36, 0.93272, 3, 58, -13.73, 6.03, 0.02229, 57, 17.93, 1.03, 0.97731, 56, 38.13, 2.14, 0.0004, 1, 56, 16.52, -0.44, 1, 3, 58, -21.78, 8.67, 0.00062, 57, 9.47, 0.55, 0.99652, 56, 29.71, 1.14, 0.00286, 3, 5, 19.14, -6.62, 0.94836, 4, -36.96, -23.01, 0.00283, 58, 89.55, 19.61, 0.04881, 2, 5, -21.04, 2.58, 0.1136, 4, -0.07, -4.6, 0.8864, 1, 4, 29.5, 7.79, 1, 1, 4, 55.5, 20.33, 1, 1, 4, 80.86, 31.93, 1, 3, 5, -4.52, -1.29, 0.83912, 4, -15.29, -12.11, 0.15671, 58, 108.23, 35.09, 0.00417, 3, 5, 35.41, -20.43, 0.50559, 4, -58.3, -22.67, 0.00911, 58, 68.68, 15.17, 0.4853], "hull": 45, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88], "width": 250, "height": 257}}, "shipin1": {"images/shipin1": {"type": "mesh", "uvs": [0.15336, 0.01081, 0.01016, 0.29552, 0.02999, 0.57752, 0.26792, 0.94087, 0.85395, 0.51245, 0.98614, 0.21418, 0.83413, 0, 0.38909, 0.15724], "triangles": [4, 6, 5, 7, 6, 4, 7, 2, 1, 7, 1, 0, 3, 2, 7, 3, 7, 4], "vertices": [57.22, -36.16, 40.8, -30.36, 26.74, -34.73, 11.53, -53.64, 41.3, -85.53, 58.26, -90.49, 67.06, -78.6, 53, -52.52], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 64, "height": 52}}, "shipin2": {"images/shipin2": {"x": 39.07, "y": 15.09, "rotation": 16.26, "width": 72, "height": 82}}, "tou": {"images/tou": {"type": "mesh", "uvs": [0.2316, 0.07664, 0.35469, 0, 0.55914, 0, 0.67823, 0.04847, 0.83452, 0.11209, 0.92042, 0.23371, 0.88621, 0.37091, 0.88147, 0.53205, 0.76695, 0.58173, 0.63699, 0.62797, 0.50093, 0.65763, 0.16993, 0.76727, 0.04908, 0.69615, 0, 0.61422, 0, 0.55789, 0.02067, 0.48538, 0.0383, 0.42356, 0.01875, 0.3742, 0.02526, 0.33448, 0.01576, 0.30166, 0.03127, 0.28331, 0.04475, 0.28525, 0.07748, 0.24618, 0.1259, 0.18408, 0.17471, 0.11823, 0.45323, 0.49865, 0.34652, 0.43321, 0.31277, 0.39064, 0.21357, 0.33814, 0.17452, 0.34079, 0.14252, 0.42157, 0.13438, 0.45329, 0.11224, 0.53226, 0.11175, 0.60254, 0.53858, 0.47834, 0.61573, 0.43615, 0.64197, 0.37117, 0.66343, 0.22034, 0.76704, 0.43749, 0.62962, 0.58017, 0.50105, 0.57222, 0.13777, 0.68599, 0.20466, 0.38704, 0.21996, 0.47514, 0.35458, 0.37998, 0.14699, 0.38284, 0.39782, 0.36054, 0.40264, 0.34271, 0.42256, 0.33424, 0.45032, 0.33513, 0.47747, 0.32264, 0.51428, 0.30169, 0.38816, 0.33245, 0.42075, 0.31506, 0.4473, 0.31417, 0.47325, 0.30971, 0.37609, 0.35162, 0.4263, 0.36357, 0.45092, 0.35341, 0.43161, 0.37864, 0.47083, 0.36322, 0.48357, 0.33585, 0.3954, 0.38373, 0.34644, 0.28914, 0.41148, 0.25879, 0.29988, 0.32955, 0.48853, 0.25253, 0.51149, 0.39541, 0.45426, 0.4251, 0.53502, 0.34955, 0.54689, 0.315, 0.56412, 0.26551, 0.55538, 0.21663, 0.27296, 0.3647, 0.39439, 0.43353, 0.1317, 0.37397, 0.11549, 0.35662, 0.10294, 0.35391, 0.08458, 0.34262, 0.10171, 0.36494, 0.11575, 0.37639, 0.14836, 0.32328, 0.06182, 0.33127, 0.09922, 0.29749, 0.11426, 0.38436, 0.09224, 0.37232, 0.08979, 0.35716, 0.04304, 0.33421, 0.13681, 0.26701, 0.22729, 0.25025, 0.18359, 0.19026, 0.25452, 0.18934, 0.22663, 0.11252, 0.31577, 0.11672, 0.50626, 0.16573, 0.6331, 0.31652, 0.72034, 0.31374, 0.7245, 0.50336, 0.3457, 0.2181, 0.30264, 0.27687, 0.27135, 0.31443, 0.21963, 0.29452, 0.2498, 0.34707, 0.21144, 0.42569, 0.40681, 0.14785, 0.05819, 0.44117, 0.12713, 0.36366, 0.09636, 0.4087, 0.06989, 0.38068, 0.0625, 0.35248], "triangles": [4, 37, 3, 37, 4, 5, 39, 97, 8, 104, 1, 2, 94, 2, 3, 9, 39, 8, 8, 97, 7, 3, 37, 94, 6, 96, 5, 96, 37, 5, 75, 45, 84, 85, 79, 80, 84, 85, 80, 75, 84, 80, 107, 85, 84, 106, 76, 81, 86, 78, 77, 106, 79, 76, 79, 77, 76, 66, 72, 71, 50, 55, 51, 47, 48, 57, 44, 65, 56, 46, 44, 56, 51, 66, 71, 55, 64, 66, 53, 63, 64, 54, 53, 64, 52, 63, 53, 48, 53, 54, 52, 53, 48, 47, 52, 48, 64, 63, 98, 50, 49, 54, 55, 66, 51, 50, 54, 55, 48, 54, 49, 94, 66, 64, 54, 64, 55, 65, 63, 52, 56, 65, 52, 56, 52, 47, 46, 56, 47, 66, 94, 72, 71, 72, 37, 95, 71, 37, 35, 36, 38, 69, 95, 36, 95, 70, 71, 62, 44, 46, 26, 44, 62, 74, 26, 62, 74, 62, 68, 69, 61, 70, 60, 58, 61, 60, 61, 69, 59, 57, 58, 59, 58, 60, 62, 57, 59, 67, 60, 69, 67, 68, 59, 67, 59, 60, 61, 50, 51, 58, 48, 49, 46, 47, 57, 49, 50, 61, 70, 61, 51, 58, 49, 61, 58, 57, 48, 62, 46, 57, 62, 59, 68, 95, 69, 70, 36, 67, 69, 36, 95, 96, 37, 96, 95, 36, 96, 38, 35, 67, 36, 70, 51, 71, 43, 33, 32, 25, 41, 33, 41, 40, 11, 31, 15, 105, 12, 13, 33, 38, 96, 6, 97, 38, 7, 29, 45, 106, 90, 24, 92, 83, 22, 88, 92, 24, 0, 82, 21, 83, 15, 16, 105, 16, 108, 107, 13, 14, 33, 14, 15, 32, 105, 16, 107, 16, 17, 108, 78, 82, 83, 45, 75, 106, 109, 78, 86, 109, 82, 78, 79, 86, 77, 77, 78, 81, 18, 19, 87, 82, 87, 19, 108, 109, 86, 108, 17, 109, 80, 79, 106, 108, 86, 85, 85, 86, 79, 30, 84, 45, 87, 82, 109, 107, 84, 30, 109, 18, 87, 108, 85, 107, 80, 106, 75, 109, 17, 18, 82, 19, 21, 21, 22, 83, 21, 19, 20, 22, 23, 88, 88, 23, 90, 23, 24, 90, 10, 11, 40, 7, 38, 6, 9, 10, 40, 9, 40, 39, 97, 35, 38, 76, 77, 81, 33, 14, 32, 12, 33, 41, 12, 41, 11, 32, 15, 31, 39, 34, 97, 25, 43, 26, 25, 68, 34, 40, 25, 34, 25, 74, 68, 40, 34, 39, 25, 26, 74, 34, 67, 35, 97, 34, 35, 43, 25, 33, 25, 40, 41, 68, 67, 34, 26, 43, 103, 31, 30, 103, 32, 31, 43, 31, 103, 43, 31, 107, 30, 105, 107, 31, 78, 83, 81, 94, 104, 2, 30, 45, 42, 83, 88, 81, 64, 98, 104, 90, 92, 91, 91, 93, 98, 98, 93, 104, 91, 92, 93, 93, 1, 104, 92, 0, 93, 89, 91, 98, 89, 90, 91, 88, 90, 89, 44, 27, 65, 26, 27, 44, 26, 103, 27, 45, 29, 42, 100, 101, 99, 65, 100, 99, 28, 101, 100, 102, 28, 100, 102, 100, 65, 73, 102, 65, 101, 89, 99, 101, 88, 89, 81, 88, 101, 28, 29, 101, 101, 29, 81, 42, 29, 28, 65, 99, 63, 27, 73, 65, 42, 28, 102, 42, 102, 73, 103, 42, 73, 103, 73, 27, 30, 42, 103, 99, 89, 98, 37, 72, 94, 63, 99, 98, 94, 64, 104, 93, 0, 1, 29, 106, 81], "vertices": [3, 35, 41.86, -1, 0.10195, 6, -38.99, 102.49, 0.528, 23, 43.18, -44.39, 0.37005, 3, 35, 54.11, -15.09, 0.1296, 6, -24.72, 114.52, 0.4, 23, 57.98, -55.77, 0.4704, 3, 35, 54.47, -38.81, 0.1296, 6, -1, 114.52, 0.4, 23, 63.04, -78.94, 0.4704, 3, 35, 47.07, -52.74, 0.1296, 6, 12.81, 106.91, 0.4, 23, 58.55, -94.06, 0.4704, 3, 35, 37.36, -71.02, 0.1296, 6, 30.94, 96.92, 0.4, 23, 52.66, -113.9, 0.4704, 3, 35, 18.42, -81.27, 0.1296, 6, 40.91, 77.83, 0.4, 23, 36.13, -127.71, 0.4704, 3, 35, -3.18, -77.63, 0.1296, 6, 36.94, 56.29, 0.4, 23, 14.24, -128.43, 0.4704, 3, 35, -28.48, -77.47, 0.1296, 6, 36.39, 30.99, 0.4, 23, -10.6, -133.29, 0.4704, 2, 35, -36.48, -64.31, 0.216, 23, -21.05, -121.98, 0.784, 3, 35, -43.97, -49.35, 0.1296, 6, 8.03, 15.93, 0.4, 23, -31.36, -108.8, 0.4704, 3, 35, -48.87, -33.64, 0.1296, 6, -7.75, 11.27, 0.4, 23, -39.27, -94.37, 0.4704, 2, 67, -45.71, -8.02, 0.12, 6, -46.15, -5.94, 0.88, 2, 67, -34.76, 6.17, 0.12, 6, -60.16, 5.23, 0.88, 2, 67, -21.98, 12.06, 0.12, 6, -65.86, 18.09, 0.88, 2, 67, -13.14, 12.19, 0.12, 6, -65.86, 26.93, 0.88, 2, 67, -1.72, 9.97, 0.28, 6, -63.46, 38.32, 0.72, 2, 67, 8.02, 8.08, 0.472, 6, -61.42, 48.02, 0.528, 2, 6, -63.69, 55.77, 0.528, 71, 5.7, -8.84, 0.472, 3, 6, -62.93, 62.01, 0.528, 70, -1.45, 11.72, 0.22943, 71, 0.14, -11.77, 0.24257, 3, 6, -64.03, 67.16, 0.528, 70, 2.7, 14.97, 0.10195, 23, 3.32, -27.46, 0.37005, 3, 6, -62.23, 70.04, 0.528, 70, 6.07, 14.62, 0.10195, 23, 6.52, -28.61, 0.37005, 3, 6, -60.67, 69.74, 0.528, 70, 6.49, 13.08, 0.10195, 23, 6.56, -30.2, 0.37005, 4, 35, 14.97, 16.47, 0.06906, 6, -56.87, 75.87, 0.528, 70, 13.66, 12.36, 0.03289, 23, 13.36, -32.6, 0.37005, 3, 35, 24.8, 11, 0.10195, 6, -51.26, 85.62, 0.528, 23, 24.08, -36.01, 0.37005, 3, 35, 35.23, 5.5, 0.10195, 6, -45.59, 95.96, 0.528, 23, 35.39, -39.34, 0.37005, 5, 35, -24, -27.72, 0.00374, 67, -3.04, -40.23, 0.01985, 66, -2.95, 0.26, 0.72425, 6, -13.29, 36.23, 0.016, 69, 17.46, -12.18, 0.23616, 4, 35, -13.91, -15.19, 0.29705, 67, 7.05, -27.69, 0.14014, 66, 7.14, 12.8, 0.29981, 69, 1.65, -15.11, 0.263, 4, 35, -7.29, -11.17, 0.62039, 67, 13.67, -23.68, 0.0983, 66, 13.76, 16.81, 0.07889, 69, -6, -13.84, 0.20241, 2, 35, 0.77, 0.46, 0.904, 6, -41.09, 61.43, 0.096, 2, 35, 0.29, 4.98, 0.792, 6, -45.62, 61.02, 0.208, 3, 67, 8.51, -4, 0.40371, 6, -49.33, 48.33, 0.392, 71, 3.64, 7.2, 0.20429, 2, 67, 3.52, -3.14, 0.608, 6, -50.27, 43.35, 0.392, 2, 67, -8.92, -0.76, 0.408, 6, -52.84, 30.96, 0.592, 2, 67, -19.95, -0.87, 0.176, 6, -52.89, 19.92, 0.824, 4, 67, 0.3, -50.08, 0.00024, 66, 0.39, -9.59, 0.71422, 6, -3.39, 39.42, 0.16216, 69, 21.35, -2.53, 0.12338, 3, 66, 7.15, -18.43, 0.47901, 6, 5.56, 46.04, 0.28504, 69, 21.99, 8.59, 0.23595, 3, 66, 17.4, -21.32, 0.18565, 6, 8.61, 56.25, 0.12725, 69, 16.1, 17.46, 0.6871, 4, 35, 20.07, -51.43, 0.35789, 6, 11.1, 79.93, 0.032, 68, -2.07, -34.36, 0.30809, 23, 31.82, -98.14, 0.30202, 3, 35, -13.84, -63.97, 0.34359, 6, 23.12, 45.83, 0.46349, 23, 1.08, -117.16, 0.19292, 5, 35, -36.48, -48.38, 0.27564, 67, -15.52, -60.88, 0.00268, 66, -15.43, -20.39, 0.18952, 6, 7.18, 23.43, 0.32, 23, -24.21, -106.36, 0.21216, 4, 35, -35.46, -33.44, 0.38153, 67, -14.5, -45.95, 0.03676, 66, -14.41, -5.46, 0.4137, 6, -7.74, 24.68, 0.168, 2, 67, -33, -4.09, 0.12, 6, -49.87, 6.82, 0.88, 2, 35, -6.92, 1.38, 0.83236, 67, 14.04, -11.13, 0.16764, 3, 35, -20.72, -0.61, 0.21899, 67, 0.24, -13.11, 0.57147, 66, 0.33, 27.38, 0.20955, 5, 35, -5.54, -16, 0.28515, 67, 15.42, -28.5, 0.03517, 66, 15.51, 11.99, 0.10374, 69, -4.17, -9.04, 0.29292, 68, -3.53, 9.34, 0.28303, 4, 35, -6.36, 8.08, 0.11491, 6, -48.81, 54.41, 0.392, 70, -2.09, -4.3, 0.17544, 71, -1.66, 4.16, 0.31765, 5, 35, -2.42, -20.96, 0.07402, 67, 18.55, -33.47, 0.0064, 66, 18.63, 7.02, 0.02788, 69, -3.31, -3.23, 0.47956, 68, -3.71, 3.47, 0.41214, 5, 35, 0.39, -21.48, 0.0286, 67, 21.35, -33.99, 0.00156, 66, 21.44, 6.5, 0.00558, 69, -5.1, -1.01, 0.21211, 68, -1.67, 1.48, 0.75215, 2, 69, -4.65, 1.62, 0.24736, 68, -1.81, -1.19, 0.75264, 2, 69, -2.48, 4, 0.54315, 68, -3.69, -3.81, 0.45685, 2, 69, -1.96, 7.68, 0.53899, 68, -3.76, -7.52, 0.46101, 2, 69, -1.76, 13.06, 0.51686, 68, -3.33, -12.89, 0.48314, 5, 35, 1.98, -19.78, 0.04632, 67, 22.94, -32.28, 0.00138, 66, 23.03, 8.21, 0.0043, 69, -7.41, -1.27, 0.03176, 68, 0.6, 2.01, 0.91623, 2, 69, -7.09, 3.38, 0.0798, 68, 0.83, -2.65, 0.9202, 2, 69, -5.23, 5.84, 0.28634, 68, -0.74, -5.31, 0.71366, 2, 69, -3.84, 8.6, 0.41501, 68, -1.79, -8.21, 0.58499, 5, 35, -1.05, -18.42, 0.14221, 67, 19.91, -30.93, 0.00803, 66, 20, 9.56, 0.02595, 69, -6, -4.27, 0.21104, 68, -1.16, 4.82, 0.61276, 4, 35, -2.84, -24.28, 0.01342, 67, 18.12, -36.78, 0.00141, 66, 18.21, 3.71, 0.00832, 69, -0.83, -1, 0.97685, 2, 69, -0.23, 2.22, 0.84452, 68, -6.13, -2.3, 0.15548, 4, 35, -5.2, -24.93, 0.02006, 67, 15.76, -37.43, 0.00304, 69, 1.38, -2.04, 0.96825, 68, -8.23, 1.74, 0.00865, 2, 69, 2.44, 3.01, 0.93202, 68, -8.68, -3.4, 0.06798, 2, 69, 0.08, 6.89, 0.68218, 68, -5.89, -6.98, 0.31782, 5, 35, -6.06, -20.74, 0.11417, 67, 14.9, -33.25, 0.01733, 66, 14.99, 7.25, 0.0988, 69, -0.69, -5.78, 0.60769, 68, -6.61, 5.69, 0.16201, 3, 35, 8.7, -14.83, 0.19211, 66, 29.75, 13.15, 1e-05, 68, 8.94, 2.36, 0.80787, 2, 35, 13.58, -22.3, 0.03118, 68, 8.82, -6.57, 0.96882, 3, 35, 2.28, -9.53, 0.57134, 67, 23.24, -22.03, 0.00524, 68, 6.57, 10.34, 0.42341, 3, 35, 14.7, -31.23, 0.30655, 66, 35.75, -3.24, 3e-05, 68, 4.77, -14.6, 0.69341, 2, 66, 13.36, -6.25, 0.26833, 69, 9.34, 3.4, 0.73167, 4, 35, -12.45, -27.67, 0.01582, 67, 8.51, -40.17, 0.00332, 66, 8.6, 0.32, 0.45964, 69, 8.67, -4.69, 0.52122, 2, 66, 20.6, -8.87, 0.08776, 69, 5.56, 10.1, 0.91224, 1, 69, 2.27, 14.63, 1, 1, 68, -1.73, -20.83, 1, 3, 35, 20.46, -38.89, 0.35656, 68, 5.26, -24.17, 0.41144, 23, 29.72, -85.77, 0.232, 2, 35, -3.29, -6.49, 0.94815, 67, 17.67, -19, 0.05185, 4, 35, -13.88, -20.74, 0.13314, 67, 7.08, -33.25, 0.05734, 66, 7.17, 7.24, 0.41434, 69, 5.24, -10.87, 0.39518, 4, 35, -5, 9.87, 0.04045, 6, -50.58, 55.81, 0.392, 70, -1.61, -2.09, 0.27419, 71, -1.79, 1.91, 0.29336, 3, 6, -52.46, 58.53, 0.392, 70, 0.01, 0.79, 0.5721, 71, -2.96, -1.19, 0.0359, 3, 6, -53.92, 58.96, 0.392, 70, -0.24, 2.29, 0.47045, 71, -2.48, -2.63, 0.13755, 3, 6, -56.05, 60.73, 0.392, 70, 0.42, 4.98, 0.43964, 71, -2.73, -5.39, 0.16836, 3, 6, -54.06, 57.22, 0.392, 70, -1.86, 1.66, 0.25926, 71, -0.98, -1.76, 0.34874, 4, 35, -5.41, 11.72, 0.00659, 6, -52.43, 55.43, 0.392, 70, -2.77, -0.6, 0.05703, 71, -0.42, 0.6, 0.54438, 2, 35, 2.99, 8.06, 0.688, 6, -48.65, 63.77, 0.312, 3, 6, -58.69, 62.51, 0.392, 70, 0.86, 8.13, 0.43022, 71, -2.69, -8.57, 0.17778, 2, 35, 6.95, 13.82, 0.6, 6, -54.35, 67.82, 0.4, 3, 35, -6.66, 11.87, 0.01066, 6, -52.61, 54.17, 0.392, 71, 0.71, 1.18, 0.59734, 3, 6, -55.16, 56.06, 0.392, 70, -3.39, 2.13, 0.10025, 71, 0.61, -2, 0.50775, 3, 6, -55.45, 58.44, 0.392, 70, -1.38, 3.43, 0.33055, 71, -1.19, -3.59, 0.27745, 2, 6, -60.87, 62.05, 0.392, 71, -1.07, -10.1, 0.608, 3, 35, 11.8, 9.54, 0.33331, 6, -49.99, 72.6, 0.328, 23, 11.63, -40.02, 0.33869, 2, 35, 14.6, -0.92, 0.88, 6, -39.5, 75.23, 0.12, 3, 35, 23.94, 4.29, 0.4128, 6, -44.56, 84.65, 0.4, 23, 24.56, -42.75, 0.1872, 3, 35, 24.21, -3.93, 0.64397, 6, -36.34, 84.79, 0.064, 23, 26.46, -50.76, 0.29203, 2, 35, 36.21, -0.51, 0.608, 6, -39.57, 96.85, 0.392, 1, 35, 35.71, -10.86, 1, 3, 35, 28.36, -33.07, 0.43342, 68, 15.07, -23.75, 0.18258, 23, 36.31, -78.5, 0.384, 2, 6, 7.58, 64.83, 0.05372, 69, 8.85, 22.16, 0.94628, 4, 35, 5.5, -58.26, 0.321, 6, 17.7, 65.26, 0.179, 69, 15, 30.21, 0.22342, 23, 18.9, -107.72, 0.27658, 4, 35, -24.26, -59.2, 0.29966, 66, -3.21, -31.21, 0.11314, 6, 18.18, 35.49, 0.4, 23, -10.08, -114.54, 0.1872, 3, 35, 19.85, -14.58, 0.66985, 68, 18.33, -3.66, 0.09815, 23, 24.3, -62.06, 0.232, 1, 35, 10.55, -9.72, 1, 2, 35, 4.6, -6.18, 0.99959, 67, 25.56, -18.69, 0.00041, 2, 35, 7.63, -0.14, 0.896, 6, -40.38, 68.28, 0.104, 2, 35, -0.56, -3.76, 0.98653, 67, 20.4, -16.27, 0.01347, 3, 35, -12.97, 0.5, 0.49, 67, 7.99, -12.01, 0.39285, 66, 8.08, 28.48, 0.11715, 2, 35, 30.99, -21.49, 0.688, 23, 36.59, -66.63, 0.312, 2, 67, 5.29, 5.73, 0.504, 6, -59.11, 45.26, 0.496, 4, 35, -3.39, 10.43, 0.01382, 6, -51.11, 57.43, 0.392, 70, -0.39, -0.91, 0.52783, 71, -2.82, 0.55, 0.06635, 4, 35, -10.51, 13.89, 0.00097, 67, 10.45, 1.38, 0.39918, 6, -54.68, 50.35, 0.392, 71, 5.03, 1.64, 0.20785, 4, 67, 14.8, 4.52, 0.28698, 6, -57.75, 54.75, 0.392, 70, -5.71, 3.89, 0.03999, 71, 3.16, -3.38, 0.28103, 3, 6, -58.61, 59.18, 0.392, 70, -2.1, 6.6, 0.29468, 71, 0.01, -6.61, 0.31332], "hull": 25, "edges": [0, 2, 2, 4, 12, 14, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 28, 30, 30, 32, 18, 20, 20, 22, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 22, 24], "width": 116, "height": 157}}, "toumao": {"images/toumao": {"type": "mesh", "uvs": [0.19077, 0.0287, 0.13077, 0.0837, 0.07685, 0.18682, 0.0311, 0.29121, 0.036, 0.34595, 0.01149, 0.43125, 0, 0.49745, 0, 0.54865, 0.03944, 0.62561, 0.089, 0.70665, 0.14667, 0.76791, 0.21656, 0.82918, 0.31442, 0.88636, 0.42101, 0.91359, 0.47693, 0.96668, 0.51537, 0.92993, 0.58876, 0.94354, 0.6639, 0.96124, 0.68487, 0.90814, 0.68312, 0.84552, 0.71458, 0.80059, 0.71633, 0.72026, 0.75652, 0.69167, 0.79146, 0.63313, 0.79671, 0.55417, 0.90505, 0.57187, 0.95397, 0.49971, 0.91204, 0.46431, 0.85262, 0.39488, 0.77224, 0.29413, 0.73904, 0.2315, 0.6674, 0.1539, 0.56779, 0.10761, 0.52935, 0.03682, 0.42451, 0, 0.31442, 0, 0.54508, 0.85368, 0.46819, 0.78425, 0.39655, 0.66989, 0.38956, 0.51196, 0.31791, 0.07222, 0.30568, 0.21244, 0.33189, 0.33361, 0.57828, 0.31047, 0.48567, 0.21653, 0.70235, 0.42483, 0.54857, 0.47248, 0.52935, 0.62768, 0.64294, 0.54191, 0.59925, 0.72571, 0.20608, 0.33906, 0.19909, 0.21244, 0.44548, 0.12123, 0.22181, 0.13076, 0.20084, 0.52694, 0.262, 0.68759, 0.38432, 0.84415, 0.21307, 0.61679, 0.39131, 0.59093, 0.54508, 0.55008, 0.67963, 0.61679, 0.31791, 0.76791, 0.55556, 0.67397, 0.42101, 0.73115, 0.64818, 0.77064, 0.47518, 0.88091, 0.62197, 0.90134, 0.76525, 0.48746, 0.3651, 0.42483, 0.51887, 0.40305, 0.45771, 0.30911, 0.65691, 0.37173, 0.19385, 0.42075], "triangles": [12, 11, 61, 56, 12, 61, 10, 55, 61, 11, 10, 61, 55, 57, 58, 9, 57, 55, 10, 9, 55, 8, 7, 54, 9, 8, 57, 52, 34, 33, 52, 33, 32, 40, 34, 52, 53, 0, 40, 1, 0, 53, 2, 1, 53, 41, 53, 40, 41, 40, 52, 51, 2, 53, 51, 53, 41, 44, 52, 32, 41, 52, 44, 70, 41, 44, 70, 44, 43, 42, 41, 70, 50, 51, 41, 50, 41, 42, 50, 3, 51, 69, 70, 43, 69, 43, 71, 72, 4, 50, 68, 42, 70, 68, 70, 69, 46, 69, 71, 39, 68, 69, 39, 69, 46, 68, 54, 72, 54, 6, 72, 72, 50, 42, 42, 68, 72, 7, 6, 54, 59, 39, 46, 59, 46, 48, 58, 39, 59, 54, 58, 57, 39, 54, 68, 58, 54, 39, 57, 8, 54, 47, 58, 59, 48, 47, 59, 67, 45, 28, 67, 28, 27, 48, 46, 45, 48, 45, 67, 24, 67, 27, 25, 24, 27, 48, 67, 24, 26, 25, 27, 60, 48, 24, 23, 60, 24, 47, 48, 60, 22, 60, 23, 44, 32, 31, 43, 44, 31, 43, 31, 30, 43, 30, 29, 71, 43, 29, 45, 71, 29, 28, 45, 29, 46, 71, 45, 21, 60, 22, 49, 62, 60, 21, 49, 60, 64, 49, 21, 37, 62, 49, 20, 64, 21, 19, 64, 20, 36, 37, 49, 36, 49, 64, 36, 64, 19, 65, 56, 37, 36, 65, 37, 66, 36, 19, 66, 19, 18, 13, 56, 65, 12, 56, 13, 15, 65, 36, 15, 36, 66, 13, 65, 15, 16, 15, 66, 17, 66, 18, 16, 66, 17, 14, 13, 15, 38, 58, 47, 62, 47, 60, 38, 55, 58, 62, 63, 38, 62, 38, 47, 61, 55, 38, 61, 38, 63, 37, 63, 62, 56, 61, 63, 56, 63, 37, 40, 35, 34, 0, 35, 40, 3, 50, 4, 51, 3, 2, 5, 4, 72, 6, 5, 72], "vertices": [3, 6, -35.84, 113.37, 0.64602, 23, 54.48, -45.14, 0.17798, 79, -42.31, 5.15, 0.176, 3, 6, -46.87, 99.39, 0.7182, 7, -283.55, -229.98, 0.0018, 23, 38.62, -36.69, 0.28, 4, 6, -49.33, 71.89, 0.69137, 7, -190.68, -223.56, 0.02636, 8, -280.67, -153.73, 0.00227, 23, 11.3, -31.95, 0.28, 2, 6, -66.82, 48.01, 0.904, 23, -15.99, -28.83, 0.096, 2, 6, -65.87, 34.38, 0.904, 23, -29.1, -32.66, 0.096, 2, 6, -70.63, 13.14, 0.904, 23, -50.86, -32.55, 0.096, 3, 6, -72.85, -3.35, 0.5641, 23, -67.44, -33.89, 0.0599, 72, -17.29, -12.62, 0.376, 5, 6, -68.59, -10.89, 0.39659, 7, 94.59, -202.43, 0.01055, 8, -0.07, -209.29, 0.00509, 23, -79.9, -36.61, 0.04378, 72, -4.78, -15.09, 0.544, 6, 6, -33.42, 30.26, 0.08312, 7, 34.56, -83.35, 0.10916, 8, -26.23, -78.53, 0.08893, 23, -96.99, -48.17, 0.02986, 72, 15.5, -11.28, 0.56591, 73, -14.49, -8.15, 0.12302, 5, 6, -44.35, 28.12, 0.07759, 7, 55.84, -68.11, 0.11547, 8, -1.67, -69.51, 0.14806, 72, 37.15, -5.75, 0.344, 73, 7.86, -8.57, 0.31488, 4, 7, 50, -51.88, 0.10304, 8, -2.97, -52.31, 0.17568, 73, 26.5, -5.39, 0.25728, 74, 0.14, -7.22, 0.464, 5, 7, 67.92, -42.11, 0.10245, 8, 16.9, -47.66, 0.23368, 9, -26.85, -44.21, 0.00012, 74, 20.54, -6.68, 0.25574, 75, -4.66, -7.8, 0.408, 4, 7, 86.08, -26.83, 0.06602, 8, 38.47, -37.76, 0.28925, 9, -3.45, -40.28, 0.15673, 75, 19.02, -6.36, 0.488, 3, 7, 97.35, -8.22, 0.01805, 8, 54.29, -22.82, 0.2013, 9, 15.72, -29.98, 0.78065, 1, 9, 32.78, -31.24, 1, 1, 9, 31.29, -19.53, 1, 1, 9, 43.57, -11.56, 1, 1, 9, 56.81, -4.04, 1, 3, 7, 106.7, 41.7, 0.00012, 9, 49.15, 8.24, 0.99789, 12, 496.39, -266.79, 0.00199, 3, 7, 78.42, 39.95, 0.01024, 9, 23.92, 21.12, 0.95068, 12, 407.69, -196.3, 0.03908, 4, 6, -85.24, -29.35, 0.00084, 7, 52.61, 38.44, 0.03503, 9, 0.93, 32.94, 0.85658, 12, 333.9, -128.01, 0.10755, 6, 6, -63.51, -16.22, 0.00782, 7, 18.01, 26.69, 0.08601, 8, -12.9, 31.95, 0.00038, 9, -34.84, 40.42, 0.47912, 12, 175.07, -61.73, 0.25067, 78, 21.08, 16.51, 0.176, 5, 6, -60.17, -18.9, 0.01173, 7, 11.53, 26.1, 0.10494, 9, -40.72, 43.2, 0.37636, 12, 129.41, -39.16, 0.38697, 78, 15.46, 25.45, 0.12, 5, 6, -54.89, -30.05, 0.01297, 7, 8.63, 30.53, 0.0907, 9, -40.96, 48.5, 0.23544, 12, 84.94, -18.63, 0.50089, 78, 2.33, 34.72, 0.16, 5, 6, -38.91, -60.43, 0.00773, 7, 14.51, 58.23, 0.03574, 9, -21.82, 69.36, 0.06774, 12, 36.56, -11.19, 0.81678, 78, -16.84, 39.22, 0.072, 1, 12, 51.96, -4.07, 1, 2, 11, 78.4, 22.54, 0.00107, 12, 48.19, 15.89, 0.99893, 2, 11, 66.41, 22.45, 0.03276, 12, 36.33, 17.72, 0.96724, 2, 11, 45.86, 25.55, 0.4208, 12, 16.54, 24.05, 0.5792, 3, 11, 16.81, 30.88, 0.80598, 10, 46.71, 30.05, 0.18523, 12, -11.29, 33.95, 0.00879, 2, 11, 0.94, 36.61, 0.43417, 10, 31.13, 36.52, 0.56583, 3, 6, 72.31, -64, 0.01747, 11, -24.82, 40.86, 0.08494, 10, 5.6, 42, 0.89759, 4, 6, 64.02, -6.53, 0.20371, 11, -76.59, 49.51, 0.00198, 10, -45.69, 53.1, 0.57831, 23, 50.89, -120.8, 0.216, 4, 6, 52.38, 33.09, 0.34882, 10, -123.28, 92.55, 0.27211, 23, 66.52, -109.75, 0.17107, 79, 23.37, 3.13, 0.208, 4, 6, 31.91, 80.41, 0.33224, 10, -247.46, 89.26, 0.07544, 23, 71.14, -87.92, 0.11232, 79, 3.03, 12.29, 0.48, 4, 6, -8.55, 117.17, 0.45053, 10, -357.29, 20.78, 0.00732, 23, 66.58, -67.05, 0.12614, 79, -18.32, 12.29, 0.416, 1, 9, 21.52, -2.26, 1, 2, 8, 30.34, 0.58, 0.51966, 9, -1.29, -1.14, 0.48034, 6, 6, -46.34, 2.85, 0.00012, 7, 37.11, 0.86, 0.43119, 8, -1.36, 1.97, 0.568, 9, -31.52, 8.47, 0.00039, 11, 177.37, -192.66, 1e-05, 12, 111.57, -212.33, 0.00029, 1, 23, -54.85, -108.49, 1, 3, 6, -8.56, 99.95, 0.30742, 10, -301.83, 5.46, 0.00458, 23, 49.16, -71.55, 0.688, 1, 23, 14.54, -76.68, 1, 1, 23, -13.85, -88.09, 1, 2, 10, 22.98, 0.56, 0.784, 23, 1.98, -133.56, 0.216, 3, 6, 55.02, -16.43, 0.03307, 10, -16.05, 8.54, 0.27893, 23, 21, -111.02, 0.688, 5, 6, 5.2, -64.71, 0.00106, 7, -8.97, 72.38, 0.00032, 9, -34.84, 93.48, 0.00011, 11, 31.75, -0.94, 0.56655, 12, -1.61, 0.16, 0.43195, 8, 6, 2.41, -14.9, 0.05705, 7, -9.11, 44.98, 0.01591, 9, -48.89, 69.96, 0.00369, 11, 26.05, -9.8, 0.04491, 10, 54, -11.03, 0.00286, 12, -8.66, -7.68, 0.04033, 23, -38.66, -136.53, 0.36326, 76, 13.12, 0.14, 0.472, 7, 6, -34.21, -12.43, 0.04247, 7, 18.73, 22.6, 0.27899, 8, -13.29, 27.81, 0.40724, 9, -36.3, 36.53, 0.1091, 11, 165.27, -60.82, 0.00716, 12, 120.65, -80.25, 0.11504, 78, -8.05, -15.08, 0.04, 9, 6, -22.95, -24.12, 0.05016, 7, 3.21, 33.17, 0.07784, 8, -25.44, 42.13, 4e-05, 9, -44.29, 53.52, 0.05511, 11, 61.46, -14.67, 0.0231, 12, 25.53, -18.13, 0.30093, 23, -51.65, -158.11, 0.06397, 77, 4.3, 10.18, 0.20486, 76, 37.25, 7.35, 0.224, 7, 6, -59.68, -11.22, 0.00713, 7, 29.68, 23.9, 0.08477, 8, -2.39, 26.15, 0.02987, 9, -26.21, 32.08, 0.49518, 11, 244.02, -84.23, 0, 12, 194.66, -115.92, 0.11904, 78, 18.38, -6.08, 0.264, 1, 23, -20.38, -64.53, 1, 1, 23, 10.13, -56.48, 1, 3, 6, 37.54, 32.34, 0.18118, 10, -108.84, 48.99, 0.13082, 23, 42.52, -98.33, 0.688, 1, 23, 30.94, -56.45, 1, 1, 23, -66.3, -73.52, 1, 4, 7, 35.57, -25.57, 0.10435, 8, -9.89, -23.1, 0.17386, 73, 18.62, 23.57, 0.496, 74, -0.73, 22.78, 0.22579, 4, 7, 78.9, -11.25, 0.03992, 8, 35.69, -20.83, 0.26943, 9, -1.71, -23.21, 0.19465, 75, 22.56, 10.43, 0.496, 6, 6, -25.79, 13.58, 0.05362, 7, 31.73, -36.49, 0.08643, 8, -16.49, -32.61, 0.06646, 23, -87.66, -80.61, 0.1846, 72, 19.85, 22.19, 0.224, 73, -1.39, 22.96, 0.3849, 7, 6, -25.45, 0.8, 0.00571, 7, 17.29, 4.81, 0.24431, 8, -19.41, 11.05, 0.05741, 9, -46.58, 21.95, 0.00114, 11, 102.19, -129.57, 0.00062, 12, 47.41, -138.06, 0.00282, 23, -73.99, -113.02, 0.688, 8, 6, -15.18, -11.32, 0.0554, 7, 4.01, 25.57, 0.0831, 8, -26.69, 34.59, 0.05763, 9, -47.47, 46.57, 0.02124, 11, 79.72, -19.58, 0.01899, 10, 107.15, -23.35, 1e-05, 12, 42.77, -25.89, 0.07564, 23, -57.69, -139.99, 0.688, 8, 6, -40.87, -18.28, 0.02106, 7, 6.24, 24.87, 0.09015, 8, -24.73, 33.33, 0.00146, 9, -45.9, 44.84, 0.15328, 11, 105.98, -20.32, 0.00125, 12, 68.57, -30.81, 0.28653, 78, -5.54, 14.09, 0.176, 77, 23.86, 14.18, 0.27027, 4, 7, 57.5, -19.52, 0.11742, 8, 12.87, -23.1, 0.247, 74, 21.55, 18.18, 0.376, 75, 0.45, 16.56, 0.25958, 7, 6, -45.79, -10.8, 0.01582, 7, 23.62, 22.23, 0.15232, 8, -8.68, 26.16, 0.1889, 9, -32.28, 33.73, 0.19438, 11, 198.72, -75.44, 0.00126, 12, 151.34, -100.01, 0.09533, 78, 4.2, -12.13, 0.352, 1, 8, 14.36, -1.16, 1, 6, 6, -74.03, -18.51, 0.00179, 7, 42.38, 29.75, 0.03614, 8, 11.4, 28.41, 0.00064, 9, -12.3, 30.66, 0.57651, 12, 265.6, -132.39, 0.08892, 78, 31.08, 1.27, 0.296, 3, 7, 91.79, 3.86, 0.00188, 8, 52.14, -9.7, 0.03514, 9, 17.08, -16.76, 0.96297, 2, 9, 40.31, 0.38, 0.99979, 12, 447.84, -297.82, 0.00021, 4, 6, -14.22, -71.82, 0.00364, 7, 7.69, 76.29, 0.00804, 9, -18.51, 88.37, 0.01004, 12, 17.82, -4.07, 0.97828, 1, 23, -34.67, -99.23, 1, 8, 6, 16.59, -8, 0.0813, 7, -37.83, 49.2, 0.00139, 9, -71.48, 88.2, 0.00042, 11, -4.95, -5.76, 0.07046, 10, 23.23, -5.51, 0.03875, 12, -38.61, 1.25, 0.01235, 23, -23, -127.21, 0.45133, 76, -4.73, 3.79, 0.344, 5, 6, 25.44, 3.82, 0.12029, 11, -43.93, -4.42, 0.00269, 10, -15.64, -2.32, 0.18898, 12, -76.88, 8.79, 5e-05, 23, -2.68, -110.63, 0.688, 2, 11, 16.06, 1.33, 0.776, 23, -9.67, -151.71, 0.224, 1, 23, -40.76, -66.55, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 14, 16, 10, 12], "width": 194, "height": 249}}, "tui1": {"images/tui1": {"type": "mesh", "uvs": [0.15252, 0.04697, 0.01137, 0.14146, 0, 0.21051, 0.06234, 0.30136, 0.14468, 0.3795, 0.19566, 0.47035, 0.23487, 0.54667, 0.21526, 0.66478, 0.22702, 0.7847, 0.17605, 0.83376, 0.06234, 0.87192, 0.03097, 0.93915, 0.08979, 1, 0.36426, 1, 0.56423, 1, 0.76029, 0.99185, 0.83087, 0.90826, 0.73284, 0.85012, 0.76029, 0.76471, 0.82302, 0.65024, 0.80342, 0.5394, 0.81126, 0.46671, 0.87792, 0.4122, 0.91321, 0.35406, 0.90929, 0.22504, 0.80734, 0.09603, 0.66618, 0.03062, 0.54463, 0, 0.30152, 0, 0.37994, 0.09785, 0.47405, 0.15236, 0.52894, 0.46308, 0.45445, 0.80287, 0.43484, 0.91553, 0.50542, 0.6357, 0.49758, 0.70112, 0.48189, 0.24867, 0.52502, 0.38313, 0.53679, 0.56847], "triangles": [15, 17, 16, 14, 13, 33, 14, 33, 17, 33, 13, 11, 13, 12, 11, 11, 9, 33, 9, 11, 10, 17, 15, 14, 33, 9, 32, 33, 32, 17, 17, 32, 18, 32, 35, 18, 32, 8, 35, 8, 7, 35, 18, 35, 19, 35, 34, 19, 35, 7, 34, 7, 6, 34, 34, 38, 19, 38, 20, 19, 34, 6, 38, 32, 9, 8, 38, 31, 20, 20, 31, 21, 5, 37, 31, 5, 4, 37, 22, 21, 37, 21, 31, 37, 22, 37, 23, 4, 36, 37, 37, 36, 23, 4, 3, 36, 36, 24, 23, 3, 2, 36, 29, 30, 2, 29, 2, 1, 29, 1, 0, 30, 36, 2, 24, 30, 25, 24, 36, 30, 30, 26, 25, 26, 29, 27, 26, 30, 29, 0, 28, 29, 29, 28, 27, 6, 5, 31, 6, 31, 38], "vertices": [2, 55, 82.63, 8.42, 0.592, 4, -13.16, -5.11, 0.408, 2, 55, 69.72, 22.16, 0.736, 4, -19.91, 12.48, 0.264, 2, 55, 58.83, 25.37, 0.824, 4, -28.78, 19.58, 0.176, 2, 55, 43.27, 23.85, 0.888, 4, -43.75, 24.07, 0.112, 3, 55, 29.43, 20.41, 0.9193, 54, 72.08, 27.57, 0.0087, 4, -57.86, 26.13, 0.072, 2, 55, 14.05, 19.74, 0.94134, 54, 57.5, 22.64, 0.05866, 3, 55, 1.18, 19.45, 0.4934, 54, 45.23, 18.77, 0.50564, 53, 66.86, 18.17, 0.00096, 1, 54, 25.8, 18.86, 1, 2, 54, 6.25, 16.55, 0.79916, 53, 27.88, 15.98, 0.20084, 2, 54, -2.05, 19.84, 0.4565, 53, 19.58, 19.27, 0.5435, 1, 53, 12.72, 27.44, 1, 1, 53, 1.55, 29.04, 1, 1, 53, -8.08, 23.87, 1, 1, 53, -6.6, 3.06, 1, 1, 53, -5.51, -12.1, 1, 1, 53, -3.12, -26.87, 1, 1, 53, 10.94, -31.24, 1, 2, 54, -1.69, -22.56, 0.344, 53, 19.92, -23.13, 0.656, 2, 54, 12.43, -23.63, 0.848, 53, 34.04, -24.21, 0.152, 2, 55, -24.78, -20.71, 0.06852, 54, 31.5, -27.04, 0.93148, 2, 55, -6.69, -23.06, 0.52417, 54, 49.53, -24.25, 0.47583, 2, 55, 4.84, -26.13, 0.88854, 54, 61.46, -23.98, 0.11146, 1, 55, 12.52, -32.96, 1, 1, 55, 21.29, -37.57, 1, 1, 55, 42.04, -41.71, 1, 2, 55, 64.35, -38.56, 0.928, 4, -47.87, -41.68, 0.072, 2, 55, 77.09, -30.31, 0.88, 4, -32.96, -38.87, 0.12, 2, 55, 83.93, -22.33, 0.8, 4, -23.6, -34.07, 0.2, 2, 55, 87.79, -4.26, 0.592, 4, -13.19, -18.81, 0.408, 2, 55, 70.85, -6.73, 0.8, 4, -29.8, -14.68, 0.2, 2, 55, 60.62, -11.86, 0.88, 4, -41.22, -15.55, 0.12, 2, 55, 9.91, -5.28, 0.944, 54, 60.51, -2.54, 0.056, 1, 54, 4.52, -0.9, 1, 1, 53, 7.6, -1.3, 1, 2, 55, -17.4, 2.39, 0.00052, 54, 32.14, -2.79, 0.99948, 1, 54, 21.4, -2.97, 1, 2, 55, 45.05, -9.13, 0.928, 4, -54.6, -7.13, 0.072, 1, 55, 22.8, -7.73, 1, 2, 55, -7.11, -2.24, 0.08005, 54, 43.31, -4.38, 0.91995], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 76, "height": 164}}, "tui2": {"images/tui2": {"type": "mesh", "uvs": [0.42221, 0.04074, 0.49687, 0.0146, 0.61103, 0, 0.75081, 0.02217, 0.87661, 0.08526, 0.94417, 0.18874, 0.96747, 0.28212, 0.99076, 0.38434, 0.94883, 0.50548, 0.92102, 0.65417, 0.79097, 0.68391, 0.74473, 0.74496, 0.74462, 0.83691, 0.74922, 0.94665, 0.57886, 0.99403, 0.33016, 0.99678, 0.0391, 1, 0, 0.92769, 0.03841, 0.87814, 0.1154, 0.86185, 0.17874, 0.86653, 0.23632, 0.83924, 0.27807, 0.82442, 0.33277, 0.73865, 0.38603, 0.67471, 0.39611, 0.55072, 0.36444, 0.45715, 0.37917, 0.38138, 0.35233, 0.2836, 0.30842, 0.16863, 0.34989, 0.10388, 0.57921, 0.0867, 0.63288, 0.22281, 0.57677, 0.50031, 0.4304, 0.81084, 0.56701, 0.64831, 0.35477, 0.96413, 0.40112, 0.87559, 0.64755, 0.36377], "triangles": [37, 21, 22, 36, 20, 37, 36, 37, 14, 15, 20, 36, 15, 36, 14, 14, 12, 13, 14, 37, 12, 37, 20, 21, 19, 17, 18, 20, 16, 17, 20, 17, 19, 16, 20, 15, 22, 23, 34, 37, 22, 34, 12, 37, 34, 10, 35, 33, 24, 25, 35, 11, 35, 10, 34, 24, 35, 34, 35, 11, 23, 24, 34, 12, 34, 11, 35, 25, 33, 25, 26, 33, 8, 10, 33, 8, 9, 10, 31, 1, 2, 31, 2, 3, 0, 1, 31, 4, 32, 31, 4, 31, 3, 32, 4, 5, 32, 5, 6, 32, 29, 31, 31, 30, 0, 31, 29, 30, 28, 29, 32, 38, 32, 6, 28, 32, 38, 27, 28, 38, 38, 6, 7, 33, 27, 38, 26, 27, 33, 8, 38, 7, 33, 38, 8], "vertices": [1, 49, 86.53, 14.83, 1, 1, 49, 90.63, 7.86, 1, 1, 49, 92.65, -2.62, 1, 1, 49, 88.4, -15.17, 1, 1, 49, 77.33, -26.17, 1, 1, 49, 59.71, -31.59, 1, 1, 49, 43.94, -33.05, 1, 1, 49, 26.7, -34.46, 1, 2, 49, 6.52, -29.8, 0.93603, 48, 68.66, -25.72, 0.06397, 3, 49, -18.33, -26.23, 0.48642, 48, 44.15, -31.15, 0.51315, 47, 66.35, -28.63, 0.00042, 3, 49, -22.83, -14.2, 0.20929, 48, 35.69, -21.48, 0.76509, 47, 57.58, -19.25, 0.02563, 1, 48, 24.63, -20.7, 1, 2, 48, 9.96, -25.54, 0.4663, 47, 32, -24.15, 0.5337, 2, 48, -7.41, -31.72, 0.0105, 47, 14.84, -30.9, 0.9895, 1, 47, 2.02, -19.1, 1, 2, 48, -27.37, 1.85, 0.01856, 47, -6.22, 1.99, 0.98144, 1, 47, -15.86, 26.66, 1, 1, 47, -5.68, 34.19, 1, 1, 47, 3.34, 33.78, 1, 2, 48, -11.97, 27.52, 0.00476, 47, 8.32, 28.15, 0.99524, 1, 47, 9.57, 22.47, 1, 2, 48, -4.91, 18.26, 0.29555, 47, 15.68, 19.13, 0.70445, 2, 48, -1.36, 15.43, 0.54729, 47, 19.33, 16.42, 0.45271, 1, 48, 13.88, 15.23, 1, 1, 48, 25.6, 13.99, 1, 2, 49, 1.02, 20.77, 0.57229, 48, 45.67, 19.65, 0.42771, 2, 49, 16.85, 23, 0.85148, 48, 59.69, 27.32, 0.14852, 2, 49, 29.51, 21.13, 0.97884, 48, 72.2, 30.04, 0.02116, 1, 49, 46.03, 22.89, 1, 1, 49, 65.49, 26.07, 1, 1, 49, 76.2, 21.85, 1, 1, 49, 78.22, 0.88, 1, 1, 49, 55.17, -3.05, 1, 2, 49, 8.8, 3.99, 0.93132, 48, 58.87, 6.7, 0.06868, 2, 48, 5.15, 2.99, 0.888, 47, 26.25, 4.2, 0.112, 3, 49, -16.01, 5.92, 0.00033, 48, 34.98, -0.25, 0.99958, 47, 56.17, 1.94, 0.0001, 2, 48, -21.46, 1.45, 0.01062, 47, -0.3, 1.78, 0.98938, 2, 48, -6.01, 2.1, 0.28078, 47, 15.12, 2.95, 0.71922, 1, 49, 31.45, -3.4, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 28, 30, 30, 32], "width": 91, "height": 168}}, "tui3": {"images/tui3": {"type": "mesh", "uvs": [0.86928, 0.03155, 0.93844, 0.10652, 0.96149, 0.26633, 0.94766, 0.49322, 0.93152, 0.67671, 0.90847, 0.76549, 0.93152, 0.87006, 0.89464, 0.96082, 0.68255, 0.98647, 0.38056, 0.97266, 0.35981, 0.92333, 0.4405, 0.80693, 0.54424, 0.78917, 0.58342, 0.73985, 0.59034, 0.60371, 0.59726, 0.50506, 0.57881, 0.40444, 0.48891, 0.21504, 0.52118, 0.09468, 0.6157, 0.03352, 0.7033, 0, 0.72405, 0.10652, 0.75863, 0.47547, 0.73788, 0.26239, 0.68486, 0.77536, 0.73096, 0.64909, 0.67564, 0.89374], "triangles": [21, 20, 0, 19, 20, 21, 1, 23, 21, 1, 21, 0, 23, 1, 2, 23, 17, 21, 21, 18, 19, 21, 17, 18, 16, 17, 23, 22, 23, 2, 16, 23, 22, 3, 22, 2, 15, 16, 22, 3, 25, 22, 25, 15, 22, 14, 15, 25, 4, 25, 3, 13, 14, 25, 5, 25, 4, 24, 13, 25, 24, 25, 5, 24, 12, 13, 26, 12, 24, 9, 10, 11, 9, 11, 12, 12, 26, 9, 24, 6, 26, 6, 24, 5, 7, 26, 6, 8, 26, 7, 8, 9, 26], "vertices": [1, 52, 45.79, -21.62, 1, 2, 51, 77.83, -17.07, 0.00046, 52, 36.52, -26.77, 0.99954, 2, 51, 60.26, -20.35, 0.05957, 52, 18.66, -26.09, 0.94043, 3, 50, 51.62, -20.16, 0.0346, 51, 35.04, -20.59, 0.69048, 52, -5.99, -20.75, 0.27492, 3, 50, 31.2, -19.99, 0.43324, 51, 14.62, -20.31, 0.56675, 52, -25.85, -15.97, 1e-05, 2, 50, 21.22, -18.46, 0.84578, 51, 4.65, -18.74, 0.15422, 2, 50, 9.78, -21.42, 0.99995, 51, -6.8, -21.64, 5e-05, 1, 50, -0.5, -18.59, 1, 2, 50, -4.68, 1.32, 0.97051, 51, -21.15, 1.17, 0.02949, 2, 50, -5.06, 30.05, 0.27941, 51, -21.39, 29.9, 0.72059, 2, 50, 0.27, 32.38, 0.26446, 51, -16.05, 32.21, 0.73554, 2, 50, 13.67, 25.59, 0.18776, 51, -2.68, 25.35, 0.81224, 2, 50, 16.3, 15.89, 0.09975, 51, -0.11, 15.64, 0.90025, 2, 50, 22.01, 12.54, 0.00301, 51, 5.59, 12.26, 0.99699, 2, 51, 20.71, 12.54, 0.91679, 52, -12.65, 14.72, 0.08321, 2, 51, 31.68, 12.55, 0.4098, 52, -1.95, 12.32, 0.5902, 2, 51, 42.72, 14.99, 0.00443, 52, 9.36, 12.26, 0.99557, 1, 52, 31.48, 17.32, 1, 1, 52, 44.17, 12.15, 1, 1, 52, 49.43, 2.2, 1, 1, 52, 51.77, -6.61, 1, 1, 52, 39.79, -6.66, 1, 3, 50, 52.39, -2.11, 0.00141, 51, 35.9, -2.54, 0.60057, 52, -1.16, -3.34, 0.39802, 2, 51, 59.39, 0.88, 0.00221, 52, 22.5, -5.19, 0.99779, 1, 51, 2.25, 2.4, 1, 2, 50, 32.99, -0.77, 0.01393, 51, 16.51, -1.11, 0.98607, 2, 50, 5.54, 2.66, 0.91789, 51, -10.92, 2.46, 0.08211], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 95, "height": 111}}, "tui4": {"images/tui4": {"type": "mesh", "uvs": [0.05113, 0, 0.10653, 0, 0.19623, 0.01445, 0.28065, 0.04541, 0.35715, 0.07963, 0.43893, 0.12688, 0.49697, 0.17739, 0.55501, 0.23279, 0.59986, 0.29471, 0.63943, 0.35989, 0.65526, 0.42018, 0.66053, 0.47558, 0.64998, 0.53749, 0.64734, 0.59778, 0.65548, 0.64425, 0.68696, 0.68212, 0.72839, 0.70259, 0.79799, 0.72408, 0.87588, 0.74762, 0.9289, 0.78242, 0.96702, 0.8336, 0.96205, 0.88989, 0.93222, 0.938, 0.86262, 0.96256, 0.78308, 0.98201, 0.72839, 0.9902, 0.73668, 0.94823, 0.74496, 0.91855, 0.72839, 0.87556, 0.70188, 0.83974, 0.6853, 0.79778, 0.63393, 0.74762, 0.5925, 0.7118, 0.54942, 0.66881, 0.53119, 0.62071, 0.53285, 0.54496, 0.52788, 0.48458, 0.51462, 0.41702, 0.46159, 0.34435, 0.41353, 0.28703, 0.32902, 0.22767, 0.28572, 0.20773, 0.2376, 0.18558, 0.17587, 0.16333, 0.11303, 0.14069, 0.04244, 0.1112, 0.00922, 0.06503, 0.00922, 0.02656, 0.06231, 0.04445, 0.15408, 0.06592, 0.22638, 0.09512, 0.29869, 0.12346, 0.3557, 0.15953, 0.41827, 0.21622, 0.47945, 0.26775, 0.5309, 0.33216, 0.56566, 0.39571, 0.57956, 0.46871, 0.58791, 0.53656, 0.59559, 0.61475, 0.60788, 0.66028, 0.65702, 0.70075, 0.7164, 0.74501, 0.77169, 0.77916, 0.83926, 0.83354, 0.84335, 0.88793, 0.79012, 0.94231], "triangles": [41, 42, 52, 42, 51, 52, 42, 43, 51, 43, 50, 51, 43, 44, 50, 44, 49, 50, 44, 45, 49, 4, 51, 3, 45, 48, 49, 45, 46, 48, 51, 50, 3, 50, 49, 3, 48, 1, 49, 49, 2, 3, 49, 1, 2, 46, 47, 48, 47, 0, 48, 48, 0, 1, 38, 39, 54, 55, 54, 8, 54, 7, 8, 39, 53, 54, 39, 40, 53, 54, 53, 7, 53, 6, 7, 53, 40, 52, 40, 41, 52, 6, 52, 5, 6, 53, 52, 5, 52, 4, 52, 51, 4, 57, 10, 11, 37, 56, 57, 57, 56, 10, 56, 9, 10, 56, 38, 55, 56, 37, 38, 56, 55, 9, 55, 8, 9, 38, 54, 55, 13, 58, 12, 35, 36, 58, 12, 58, 11, 36, 57, 58, 58, 57, 11, 36, 37, 57, 33, 59, 60, 33, 34, 59, 59, 13, 14, 34, 35, 59, 35, 58, 59, 59, 58, 13, 31, 61, 62, 31, 32, 61, 16, 61, 15, 32, 60, 61, 32, 33, 60, 61, 60, 15, 60, 14, 15, 60, 59, 14, 30, 31, 62, 63, 17, 18, 63, 62, 17, 62, 61, 16, 62, 16, 17, 21, 65, 20, 28, 64, 65, 65, 64, 20, 28, 29, 64, 29, 63, 64, 29, 30, 63, 64, 19, 20, 64, 18, 19, 64, 63, 18, 30, 62, 63, 27, 28, 65, 25, 26, 24, 24, 66, 23, 24, 26, 66, 22, 23, 65, 26, 27, 66, 23, 66, 65, 66, 27, 65, 22, 65, 21], "vertices": [1, 37, -8.51, 6.2, 1, 2, 38, -34.8, -3.55, 0, 37, -3.69, 9.45, 1, 2, 38, -27.22, 2.55, 0.01966, 37, 5.5, 12.67, 0.98034, 2, 38, -17.71, 6.54, 0.18648, 37, 15.79, 13.25, 0.81352, 2, 38, -8.26, 9.53, 0.6729, 37, 25.7, 12.9, 0.3271, 2, 39, -30.58, 1.86, 0.00412, 38, 3.31, 11.64, 0.99588, 2, 39, -20.51, 4.92, 0.08249, 38, 13.84, 11.42, 0.91751, 2, 39, -9.64, 7.73, 0.49498, 38, 25.03, 10.71, 0.50502, 2, 39, 1.83, 8.87, 0.07374, 38, 36.29, 8.22, 0.92626, 2, 39, 13.66, 9.3, 0.75557, 38, 47.67, 4.96, 0.24443, 2, 39, 23.9, 7.64, 0.99276, 38, 56.89, 0.19, 0.00724, 2, 40, 9.14, 8.21, 0.48967, 39, 33.01, 5.19, 0.51033, 3, 41, -0.1, 7.76, 0.00745, 40, 19.57, 6.4, 0.97892, 39, 42.65, 0.81, 0.01363, 3, 42, -4.27, 8.61, 0.00517, 41, 10.03, 6.21, 0.84646, 40, 29.78, 5.44, 0.14836, 4, 43, -10.3, 8.94, 0.00141, 42, 2.89, 5.17, 0.78868, 41, 17.98, 6.08, 0.20884, 40, 37.72, 5.77, 0.00106, 2, 43, -3.71, 5.95, 0.27526, 42, 10.1, 4.59, 0.72474, 1, 43, 1.86, 5.92, 1, 1, 43, 9.86, 7.59, 1, 2, 44, 3.12, 11.64, 0.16444, 43, 18.76, 9.53, 0.83556, 3, 46, -24.38, -2.46, 0.02561, 44, 10.98, 13.68, 0.58065, 43, 26.79, 8.35, 0.39374, 3, 46, -19.25, 5.63, 0.16808, 44, 20.54, 13.01, 0.73663, 43, 35.33, 4.01, 0.0953, 3, 46, -10.93, 10.39, 0.45704, 44, 28.68, 7.95, 0.53389, 43, 40.86, -3.82, 0.00906, 2, 46, -2.36, 12.2, 0.72976, 44, 34.34, 1.27, 0.27024, 2, 46, 5.11, 8.33, 0.93768, 44, 34.49, -7.15, 0.06232, 1, 46, 12.42, 3.12, 1, 1, 46, 16.71, -0.95, 1, 2, 46, 10.25, -4.09, 0.91572, 45, 19.11, -7.37, 0.08428, 3, 46, 5.54, -6.1, 0.40885, 45, 13.99, -7.34, 0.58703, 44, 21.99, -14.38, 0.00412, 3, 46, 0.35, -11.53, 0.00074, 45, 7.07, -10.26, 0.79536, 44, 14.74, -12.39, 0.2039, 4, 45, 1.52, -14.01, 0.3564, 44, 8.06, -11.91, 0.60409, 43, 14.15, -14.09, 0.03566, 42, 33.7, -8.21, 0.00385, 4, 45, -5.23, -16.9, 0.06782, 44, 0.97, -10, 0.50535, 43, 8.36, -9.58, 0.34921, 42, 26.72, -5.93, 0.07762, 4, 44, -9.1, -10.63, 0.0178, 43, -1.16, -6.24, 0.09151, 42, 16.63, -6.02, 0.88724, 41, 35.13, 1.66, 0.00346, 2, 42, 9.16, -6.5, 0.83787, 41, 28.55, -1.9, 0.16213, 2, 42, 0.57, -6.49, 0.00631, 41, 20.74, -5.49, 0.99369, 1, 41, 12.39, -6.37, 1, 1, 41, -0.37, -4.6, 1, 2, 41, -10.62, -3.85, 0.06914, 40, 9.74, -5.79, 0.93086, 2, 40, -1.81, -6.42, 0.41294, 39, 18.73, -6.2, 0.58706, 2, 39, 5.25, -7.58, 0.69826, 38, 34.43, -8.47, 0.30174, 1, 38, 23.59, -6.72, 1, 2, 38, 10.2, -7.84, 0.78151, 37, 37.3, -9.63, 0.21849, 2, 38, 4.77, -9.47, 0.27989, 37, 31.64, -9.35, 0.72011, 2, 38, -1.26, -11.28, 0.00743, 37, 25.34, -9.05, 0.99257, 1, 37, 17.85, -9.53, 1, 1, 37, 10.23, -10.02, 1, 1, 37, 1.28, -9.99, 1, 1, 37, -5.99, -5.43, 1, 1, 37, -9.64, 0, 1, 1, 37, -3.32, 0.59, 1, 2, 38, -22.83, -6.22, 0.00325, 37, 6.71, 2.94, 0.99675, 2, 38, -14.32, -3.07, 0.0387, 37, 15.78, 3.05, 0.9613, 2, 38, -5.93, 0.16, 0.34718, 37, 24.77, 3.29, 0.65282, 2, 39, -28.08, -8.19, 5e-05, 38, 2.57, 1.31, 0.99995, 2, 39, -16.86, -5, 0.0041, 38, 14.22, 0.85, 0.9959, 2, 39, -6.52, -1.68, 0.05664, 38, 25.08, 0.8, 0.94336, 1, 39, 5.58, -0.02, 1, 2, 39, 16.98, 0.03, 0.99944, 38, 47.95, -4.89, 0.00056, 2, 41, -12.62, 1.87, 0.00049, 40, 7.41, -0.2, 0.99951, 2, 41, -1.07, 1.31, 0.02301, 40, 18.98, -0.09, 0.97699, 3, 42, -4.68, 2.47, 0.00187, 41, 12.22, 0.46, 0.9956, 40, 32.29, -0.17, 0.00253, 2, 42, 2.57, -0.51, 0.81164, 41, 20.06, 0.78, 0.18836, 2, 43, -4.21, 1.51, 0.0146, 42, 11.14, 0.24, 0.9854, 4, 45, -14.62, -15.15, 0.00024, 44, -5.32, -2.83, 0.01001, 43, 5.35, -0.52, 0.97522, 42, 20.82, 1.57, 0.01453, 3, 45, -9.85, -8.47, 0.0019, 44, 2.56, -0.53, 0.99767, 42, 28.81, 3.44, 0.00043, 3, 46, -11.97, -5.64, 0.00378, 44, 14.08, 1.25, 0.97426, 43, 24.81, -4.31, 0.02196, 3, 46, -4.44, -0.26, 0.33985, 45, 7.16, 1.99, 0.50017, 44, 22.39, -2.82, 0.15998, 1, 46, 6.36, 0.07, 1], "hull": 48, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 88, 90, 90, 92, 92, 94, 0, 94, 84, 86, 86, 88, 80, 82, 82, 84], "width": 105, "height": 170}}, "xiaba": {"images/xiaba": {"type": "mesh", "uvs": [0.04108, 0.13343, 0.02449, 0.27846, 0.02449, 0.39328, 0.05213, 0.51212, 0.11019, 0.63297, 0.15443, 0.64909, 0.20972, 0.67326, 0.25949, 0.76793, 0.27884, 0.85253, 0.37772, 1, 0.42885, 0.8957, 0.3905, 0.80488, 0.49917, 0.72338, 0.64938, 0.69311, 0.85393, 0.67681, 0.90507, 0.53943, 0.95301, 0.4067, 0.97219, 0.27397, 0.93064, 0.10166, 0.82197, 0, 0.72609, 0.03879, 0.68774, 0.07604, 0.59185, 0.09234, 0.50556, 0.15289, 0.30421, 0.12727, 0.16997, 0.11796, 0.48319, 0.24603, 0.51515, 0.3741, 0.50236, 0.52546, 0.52154, 0.64189, 0.24668, 0.28795, 0.27544, 0.48587, 0.3074, 0.62792, 0.31379, 0.75366, 0.75485, 0.1785, 0.77722, 0.3741, 0.74846, 0.51615], "triangles": [34, 18, 17, 18, 20, 19, 21, 20, 34, 22, 34, 23, 26, 24, 23, 30, 25, 24, 30, 24, 26, 30, 26, 27, 30, 2, 1, 30, 1, 25, 25, 1, 0, 22, 21, 34, 18, 34, 20, 23, 34, 26, 35, 26, 34, 35, 34, 17, 31, 2, 30, 31, 30, 27, 35, 27, 26, 16, 35, 17, 31, 3, 2, 36, 35, 16, 36, 27, 35, 28, 31, 27, 28, 27, 36, 15, 36, 16, 32, 31, 28, 4, 3, 31, 5, 4, 31, 29, 28, 36, 14, 36, 15, 14, 13, 36, 32, 28, 29, 32, 5, 31, 6, 5, 32, 13, 29, 36, 12, 32, 29, 12, 29, 13, 33, 32, 12, 6, 32, 33, 7, 6, 33, 11, 33, 12, 8, 7, 33, 8, 33, 11, 8, 11, 10, 9, 8, 10], "vertices": [1, 32, -0.92, -13.7, 1, 2, 33, -3.3, -14.86, 0.1676, 32, 9.19, -14.94, 0.8324, 3, 34, -8.42, -17.06, 8e-05, 33, 4.71, -15.53, 0.46612, 32, 17.22, -15.26, 0.5338, 3, 34, -0.24, -15.02, 0.0498, 33, 13.12, -14.81, 0.7788, 32, 25.59, -14.17, 0.1714, 3, 34, 7.98, -11.43, 0.44891, 33, 21.79, -12.57, 0.48863, 32, 34.16, -11.55, 0.06246, 3, 34, 8.93, -9.09, 0.57065, 33, 23.1, -10.41, 0.38827, 32, 35.37, -9.34, 0.04107, 3, 34, 10.4, -6.15, 0.82072, 33, 25.02, -7.74, 0.16697, 32, 37.17, -6.59, 0.01231, 2, 34, 16.82, -3.12, 0.99919, 33, 31.84, -5.76, 0.00081, 1, 34, 22.65, -1.68, 1, 1, 34, 32.56, 4.13, 1, 1, 34, 25.08, 6.18, 1, 1, 34, 18.89, 3.74, 1, 2, 34, 12.78, 8.84, 0.91694, 33, 29.75, 6.68, 0.08306, 2, 34, 10.08, 16.31, 0.61294, 33, 28.27, 14.49, 0.38706, 3, 34, 8.15, 26.63, 0.40427, 33, 28, 24.98, 0.59571, 32, 38.71, 26.23, 2e-05, 3, 34, -1.63, 28.5, 0.25303, 33, 18.63, 28.38, 0.73425, 32, 29.2, 29.22, 0.01272, 3, 34, -11.08, 30.23, 0.07432, 33, 9.58, 31.58, 0.63497, 32, 20.01, 32.02, 0.29071, 3, 34, -20.42, 30.5, 0.00632, 33, 0.4, 33.33, 0.20371, 32, 10.77, 33.37, 0.78997, 3, 34, -32.29, 27.47, 0.00015, 33, -11.8, 32.22, 0.18161, 32, -1.37, 31.72, 0.81824, 1, 32, -8.69, 26.46, 1, 1, 32, -6.17, 21.47, 1, 1, 32, -3.64, 19.41, 1, 1, 32, -2.7, 14.48, 1, 1, 32, 1.37, 9.92, 1, 1, 32, -0.83, -0.27, 1, 1, 32, -1.75, -7.09, 1, 2, 33, -3.62, 8.64, 0.256, 32, 7.84, 8.52, 0.744, 3, 34, -11.66, 7.79, 0.00454, 33, 5.45, 9.52, 0.55555, 32, 16.86, 9.8, 0.43991, 2, 34, -1.05, 7.95, 0.34011, 33, 15.95, 7.99, 0.65989, 2, 34, 7, 9.54, 0.78212, 33, 24.16, 8.29, 0.21788, 2, 33, -1.7, -3.62, 0.152, 32, 10.3, -3.65, 0.848, 3, 34, -2.93, -3.8, 0.00374, 33, 12.23, -3.31, 0.80951, 32, 24.2, -2.72, 0.18675, 3, 34, 6.86, -1.42, 0.94961, 33, 22.28, -2.51, 0.04799, 32, 34.2, -1.48, 0.00241, 2, 34, 15.61, -0.43, 0.99988, 33, 31.07, -2.92, 0.00012, 3, 34, -26.24, 18.94, 0.00054, 33, -7.18, 22.84, 0.17018, 32, 3.66, 22.55, 0.82928, 3, 34, -12.68, 21.12, 0.06222, 33, 6.56, 22.84, 0.71536, 32, 17.38, 23.16, 0.22243, 3, 34, -2.65, 20.41, 0.26023, 33, 16.35, 20.55, 0.73023, 32, 27.26, 21.3, 0.00954], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 51, "height": 70}}, "xiaya": {"images/xiaya": {"x": -2.92, "y": 7.8, "rotation": 87.75, "width": 45, "height": 17}}, "yan1": {"images/yan1": {"x": 0.66, "y": 0.16, "rotation": 32.31, "width": 10, "height": 9}}, "yan2": {"images/yan2": {"x": 0.8, "y": 0.36, "rotation": -33.55, "width": 19, "height": 17}}, "yangguang2": {"images/yangguang2": {"x": -3.1, "y": 5.34, "rotation": -33.55, "width": 33, "height": 19}}, "yanguang1": {"images/yanguang1": {"x": 4.23, "y": -1.73, "rotation": 32.31, "width": 24, "height": 22}}, "ying": {"images/ying": {"x": 2.07, "y": 1.93, "width": 310, "height": 76}}, "zuili": {"images/zuili": {"type": "mesh", "uvs": [0.09137, 0.09608, 0, 0.20644, 0, 0.3444, 0.07698, 0.47546, 0.09496, 0.72379, 0.04822, 0.82036, 0.17407, 1, 0.59836, 1, 0.80332, 0.88934, 0.96153, 0.55824, 0.93636, 0.15126, 0.70624, 0, 0.34307, 0.03399, 0.47252, 0.54444, 0.4833, 0.26163, 0.46892, 0.82726], "triangles": [14, 10, 9, 0, 12, 14, 14, 12, 11, 14, 11, 10, 0, 14, 2, 2, 1, 0, 15, 13, 8, 6, 4, 15, 5, 4, 6, 6, 15, 7, 7, 15, 8, 13, 3, 14, 4, 3, 13, 15, 4, 13, 13, 14, 9, 8, 13, 9, 14, 3, 2], "vertices": [3, 36, -4.03, -13.92, 0.18157, 64, 11.17, 0.89, 0.41032, 65, 11.56, 21.43, 0.40811, 3, 36, 0.83, -18.82, 0.18157, 64, 5.69, 5.1, 0.41032, 65, 6.09, 25.64, 0.40811, 3, 36, 7.54, -19.63, 0.27872, 64, -1.06, 4.99, 0.31801, 65, -0.67, 25.53, 0.40327, 2, 36, 14.59, -17.37, 0.856, 31, 16.51, 9.01, 0.144, 2, 36, 27.28, -17.82, 0.648, 31, 3.87, 9.77, 0.352, 2, 36, 33.38, -20.2, 0.152, 31, -2.1, 12.12, 0.848, 2, 36, 42.02, -13.37, 0.096, 31, -10.69, 5.67, 0.904, 2, 36, 40.27, 7.01, 0.096, 31, -9.5, -14.23, 0.904, 2, 36, 34.35, 16.26, 0.184, 31, -3.99, -23.44, 0.816, 3, 36, 20.05, 23.46, 0.4882, 31, 9.84, -31.19, 0.43987, 80, -11.8, -7.99, 0.07193, 1, 80, 4.51, -4.16, 1, 2, 65, 16.71, -7.4, 0.904, 80, 9.45, 7.99, 0.096, 2, 64, 14.39, -10.9, 0.472, 65, 14.78, 9.64, 0.528, 2, 36, 19.58, 0.93, 0.856, 31, 11.01, -9.06, 0.144, 2, 36, 6.22, 3.41, 0.312, 65, 3.73, 2.88, 0.688, 2, 36, 32.25, 0.02, 0.152, 31, -1.52, -7.64, 0.848], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 47, "height": 49}}}}], "events": {"roar": {}}, "animations": {"hide": {"slots": {"yangguang2": {"color": [{"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}, "yanguang1": {"color": [{"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"tui10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 7, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 6.93, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -25.93, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -17.53, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -17.53, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.07, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -35.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 40.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 21.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "chi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shen3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -7.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -3.44, "y": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -3.44, "y": 2.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": -9.63, "y": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": -6.98, "y": 2.76}, {"time": 4}]}, "toumao": {"rotate": [{"curve": 0.724, "c3": 0.75}, {"time": 0.9, "angle": 32.68, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.41, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": 0.724, "c3": 0.75}, {"time": 0.9, "x": -6.7, "y": 30.09, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 26.11, "y": 3.13, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 39.31, "y": 12.96, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"curve": 0.724, "c3": 0.75}, {"time": 3, "x": 1.05, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.178, "y": 1.184, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.363, "y": 1.378, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiaba": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "xiaba3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "re2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.36, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 12.84, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.23, "y": -1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 15.9, "y": -3.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -7.35, "y": 10.19, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 5.35, "y": 9.05, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 1.228, "y": 1.137, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "re4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -3.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "re5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -3.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "re1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.4, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -8.66, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 3.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.9667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 1.228, "y": 1.137, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shen": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -13.42, "y": -5.03, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shen4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "tui4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.61, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 14.22, "curve": "stepped"}, {"time": 3.1333, "angle": 14.22, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.73, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -15.16, "curve": "stepped"}, {"time": 3.1333, "angle": -15.16, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -12.05, "curve": "stepped"}, {"time": 3.1333, "angle": -12.05, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tui9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.17, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 14.46, "curve": "stepped"}, {"time": 3.1333, "angle": 14.46, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "mao1": {"scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 0.873, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "mao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "mao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -4.82, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "ying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.943, "y": 0.943, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.033, "y": 1.033, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 0.957, "y": 0.957, "curve": "stepped"}, {"time": 3.6, "x": 0.957, "y": 0.957, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tou": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 15.31, "y": -6.19, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -17.82, "y": 19.27, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -1.14, "y": 16.76, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yan2": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.02, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -0.31, "y": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -0.46, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": -0.64, "y": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yan1": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.02, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 3.65, "y": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -4.77, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": -1.03, "y": 6.43, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tou2": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -1.33, "y": 1.08, "curve": "stepped"}, {"time": 2.2333, "x": -1.33, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 0.05, "y": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tou3": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -0.01, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -0.62, "y": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 3.1333}]}, "xiazui": {"translate": [{"x": 23.52, "y": 5.14, "curve": "stepped"}, {"time": 0.6667, "x": 23.52, "y": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -0.39, "y": -1.94, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 2, "x": -8.09, "y": -0.66, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "x": -18.92, "y": -7.13, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 23.52, "y": 5.14}], "scale": [{"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.979, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "y": 1.103, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "zuili": {"rotate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 14.75, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.083, "curve": 0.328, "c3": 0.662, "c4": 0.34}, {"time": 1.3667, "x": 1.029, "y": 1.116, "curve": 0.329, "c2": 0.22, "c3": 0.662, "c4": 0.56}, {"time": 1.4, "x": 0.998, "y": 1.083, "curve": 0.329, "c2": 0.27, "c3": 0.662, "c4": 0.6}, {"time": 1.4333, "x": 1.053, "y": 1.145, "curve": 0.329, "c2": 0.29, "c3": 0.663, "c4": 0.62}, {"time": 1.4667, "x": 0.994, "y": 1.083, "curve": 0.329, "c2": 0.3, "c3": 0.663, "c4": 0.63}, {"time": 1.5, "x": 1.029, "y": 1.123, "curve": 0.33, "c2": 0.31, "c3": 0.663, "c4": 0.64}, {"time": 1.5333, "x": 0.989, "y": 1.082, "curve": 0.33, "c2": 0.31, "c3": 0.663, "c4": 0.64}, {"time": 1.5667, "x": 1.047, "y": 1.148, "curve": 0.33, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 1.6, "x": 0.984, "y": 1.081, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.65}, {"time": 1.6333, "x": 1.045, "y": 1.151, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.65}, {"time": 1.6667, "x": 0.978, "y": 1.081, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.65}, {"time": 1.7, "x": 1.011, "y": 1.121, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.7333, "x": 0.971, "y": 1.08, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.7667, "x": 0.994, "y": 1.109, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.8, "x": 0.964, "y": 1.079, "curve": 0.331, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 1.8333, "x": 0.978, "y": 1.099, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.8667, "x": 0.956, "y": 1.078, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.9, "x": 0.988, "y": 1.118, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.9333, "x": 0.948, "y": 1.077, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.9667, "x": 0.973, "y": 1.11, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2, "x": 0.939, "y": 1.076, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.0333, "x": 0.97, "y": 1.116, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.0667, "x": 0.931, "y": 1.075, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.1, "x": 0.954, "y": 1.107, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.1333, "x": 0.922, "y": 1.074, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.1667, "x": 0.947, "y": 1.109, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.2, "x": 0.913, "y": 1.073, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.2333, "x": 0.915, "y": 1.08, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.2667, "x": 0.903, "y": 1.072, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 2.3, "x": 0.922, "y": 1.1, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 2.3333, "x": 0.894, "y": 1.071, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 2.3667, "x": 0.906, "y": 1.09, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 2.4, "x": 0.884, "y": 1.07, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.4333, "x": 0.91, "y": 1.106, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.4667, "x": 0.875, "y": 1.069, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.5, "x": 0.897, "y": 1.101, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.5333, "x": 0.865, "y": 1.068, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.5667, "x": 0.893, "y": 1.107, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.6, "x": 0.856, "y": 1.067, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.6333, "x": 0.855, "y": 1.07, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.847, "y": 1.065, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 2.7, "x": 0.847, "y": 1.071, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 2.7333, "x": 0.837, "y": 1.064, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.7667, "x": 0.843, "y": 1.078, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.8, "x": 0.828, "y": 1.063, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.8333, "x": 0.834, "y": 1.075, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.8667, "x": 0.819, "y": 1.062, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.9, "x": 0.823, "y": 1.072, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.9333, "x": 0.811, "y": 1.061, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.9667, "x": 0.811, "y": 1.067, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 3, "x": 0.802, "y": 1.06, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 3.0667, "x": 0.804, "y": 1.072, "curve": 0.338, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 3.1333, "x": 0.787, "y": 1.058, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 3.2, "x": 0.792, "y": 1.075, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 3.2667, "x": 0.773, "y": 1.057, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.5667, "x": 0.758, "y": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao7": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 0.32, "y": 0.22, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 0.05, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": -0.51, "y": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "tou4": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -0.47, "y": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -1.35, "y": -2.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1333}]}, "toumao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.83, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -4.45, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -0.54, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -0.54, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.25, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 11.71, "y": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 10.43, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 12.42, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 10.44, "y": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 10.2, "y": 1.45, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 7.65, "y": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 8.15, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 3.5333}]}, "toumao9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -9.18, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -7.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toumao16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shangzui5": {"rotate": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 3.1}], "translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 4.02, "y": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 4.21, "y": -3.07, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 4.13, "y": -3.16, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 4.4, "y": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 4.37, "y": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 4.83, "y": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 4.7, "y": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.17, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 5.08, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 5.74, "y": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 5.5, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 6.15, "y": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 5.95, "y": 0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 6.56, "y": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 6.41, "y": 2.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 6.91, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 6.87, "y": 3.08, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 7.38, "y": 3.65, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 7.32, "y": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 7.82, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 7.74, "y": 5.07, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 8.25, "y": 5.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 8.12, "y": 5.92, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 8.59, "y": 6.38, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 8.42, "y": 6.62, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 8.84, "y": 6.96, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 8.61, "y": 7.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.64, "y": 7.12, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 9.44, "y": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 8.74, "y": 6.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 9.22, "y": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 8.93, "y": 6.66, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 9.31, "y": 6.58, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 9.19, "y": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 9.98, "y": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 9.5, "y": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 10.18, "y": 5.73, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 9.84, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 11.31, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 10.2, "y": 4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 10.98, "y": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 10.59, "y": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 11.37, "y": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 10.98, "y": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 11.62, "y": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 11.38, "y": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 12.52, "y": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 11.77, "y": 2.14, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": 12.76, "y": 2.01, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 12.15, "y": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 13, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 12.41, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 13.02, "y": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 12.67, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": 13.28, "y": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 12.93, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 13.53, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 13.18, "y": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "x": 13.78, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 13.42, "y": -0.48, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 13.54, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 3.7667}], "scale": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 1.053, "y": 1.053}]}, "shangzui2": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 1.83, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 7.51, "y": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 11.74, "y": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 3.7667}]}, "shangchi": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.1, "y": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": -4.76, "y": -2.7, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shangzui6": {"rotate": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 3.1}], "translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.98, "y": 2.99, "curve": 0.322, "c3": 0.655, "c4": 0.34}, {"time": 1.1333, "x": 1.44, "y": 3.58, "curve": 0.323, "c2": 0.23, "c3": 0.657, "c4": 0.56}, {"time": 1.1667, "x": 1.13, "y": 3.11, "curve": 0.325, "c2": 0.27, "c3": 0.658, "c4": 0.61}, {"time": 1.2, "x": 1.66, "y": 3.41, "curve": 0.326, "c2": 0.29, "c3": 0.659, "c4": 0.63}, {"time": 1.2333, "x": 1.43, "y": 3.37, "curve": 0.327, "c2": 0.3, "c3": 0.66, "c4": 0.64}, {"time": 1.2667, "x": 2.21, "y": 3.72, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.64}, {"time": 1.3, "x": 1.85, "y": 3.71, "curve": 0.329, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 1.3333, "x": 2.66, "y": 4.12, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.3667, "x": 2.34, "y": 4.12, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.4, "x": 3.17, "y": 4.57, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.4333, "x": 2.88, "y": 4.57, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.4667, "x": 3.42, "y": 4.83, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.5, "x": 3.45, "y": 5.04, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 1.5333, "x": 4.5, "y": 5.39, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.5667, "x": 4.04, "y": 5.53, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.6, "x": 5.09, "y": 5.9, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 1.6333, "x": 4.63, "y": 6.02, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.6667, "x": 5.42, "y": 6.36, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 1.7, "x": 5.2, "y": 6.5, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 1.7333, "x": 6.29, "y": 6.92, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.7667, "x": 5.74, "y": 6.94, "curve": 0.338, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.8, "x": 6.47, "y": 7.28, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 1.8333, "x": 6.22, "y": 7.34, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 1.8667, "x": 7.07, "y": 7.71, "curve": 0.341, "c2": 0.37, "c3": 0.674, "c4": 0.71}, {"time": 1.9, "x": 6.61, "y": 7.67, "curve": 0.342, "c2": 0.39, "c3": 0.675, "c4": 0.73}, {"time": 1.9333, "x": 7.07, "y": 7.9, "curve": 0.343, "c2": 0.44, "c3": 0.677, "c4": 0.77}, {"time": 1.9667, "x": 6.86, "y": 7.87, "curve": 0.345, "c2": 0.66, "c3": 0.678}, {"time": 2, "x": 6.89, "y": 7.9, "curve": 0.324, "c3": 0.657, "c4": 0.34}, {"time": 2.0333, "x": 7.69, "y": 8.2, "curve": 0.325, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 2.0667, "x": 6.97, "y": 7.93, "curve": 0.326, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 2.1, "x": 7.42, "y": 8.11, "curve": 0.327, "c2": 0.29, "c3": 0.66, "c4": 0.63}, {"time": 2.1333, "x": 7.11, "y": 7.99, "curve": 0.327, "c2": 0.3, "c3": 0.661, "c4": 0.64}, {"time": 2.1667, "x": 7.46, "y": 8.13, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.64}, {"time": 2.2, "x": 7.3, "y": 8.06, "curve": 0.329, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 2.2333, "x": 8.05, "y": 8.39, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.2667, "x": 7.52, "y": 8.15, "curve": 0.33, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.3, "x": 8.16, "y": 8.42, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.3333, "x": 7.77, "y": 8.25, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.3667, "x": 9.2, "y": 8.59, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.4, "x": 8.04, "y": 8.35, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.4333, "x": 8.77, "y": 8.67, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.4667, "x": 8.32, "y": 8.46, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 2.5, "x": 9.05, "y": 8.78, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 2.5333, "x": 8.61, "y": 8.58, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.5667, "x": 9.2, "y": 8.83, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.6, "x": 8.91, "y": 8.69, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.6333, "x": 9.99, "y": 8.99, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.6667, "x": 9.2, "y": 8.8, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 2.7, "x": 10.13, "y": 9.04, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 2.7333, "x": 9.48, "y": 8.92, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.7667, "x": 10.29, "y": 9.27, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.8, "x": 9.67, "y": 8.99, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.8333, "x": 10.24, "y": 9.24, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.8667, "x": 9.86, "y": 9.06, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2.9, "x": 10.43, "y": 9.31, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 2.9333, "x": 10.04, "y": 9.14, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 2.9667, "x": 10.61, "y": 9.38, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 3, "x": 10.23, "y": 9.21, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 3.0333, "x": 10.8, "y": 9.45, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 3.0667, "x": 10.41, "y": 9.28, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 3.1, "x": 10.5, "y": 9.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7667}], "scale": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3}, {"time": 1.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7}, {"time": 1.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5}, {"time": 2.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9}, {"time": 2.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 1.053, "y": 1.053}]}, "shangzui": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 10.31, "y": 0.91, "curve": 0.344, "c2": 0.06, "c3": 0.678, "c4": 0.4}, {"time": 1.1667, "x": 10.32, "y": 0.9, "curve": 0.339, "c2": 0.27, "c3": 0.672, "c4": 0.61}, {"time": 1.2, "x": 10.95, "y": 0.88, "curve": 0.339, "c2": 0.29, "c3": 0.672, "c4": 0.62}, {"time": 1.2333, "x": 10.33, "y": 0.89, "curve": 0.339, "c2": 0.3, "c3": 0.672, "c4": 0.63}, {"time": 1.2667, "x": 10.96, "y": 0.89, "curve": 0.339, "c2": 0.31, "c3": 0.672, "c4": 0.64}, {"time": 1.3, "x": 10.35, "y": 0.88, "curve": 0.339, "c2": 0.31, "c3": 0.673, "c4": 0.64}, {"time": 1.3333, "x": 10.77, "y": 0.89, "curve": 0.339, "c2": 0.31, "c3": 0.673, "c4": 0.65}, {"time": 1.3667, "x": 10.37, "y": 0.87, "curve": 0.339, "c2": 0.32, "c3": 0.673, "c4": 0.65}, {"time": 1.4, "x": 10.8, "y": 0.89, "curve": 0.339, "c2": 0.32, "c3": 0.673, "c4": 0.65}, {"time": 1.4333, "x": 10.4, "y": 0.85, "curve": 0.784, "c2": 0.22, "c3": 0.686}, {"time": 3.4667, "x": 12.88, "y": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shangzui3": {"translate": [{"time": 0.4333}, {"time": 1.1, "x": 9.63, "y": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": 6.99, "y": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "shangzui4": {"translate": [{"time": 0.6667}, {"time": 1.1, "x": 4.2, "y": -0.45, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 4.1, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 4, "y": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 3.9, "y": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 3.8, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 3.69, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.59, "y": 0.83, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 3.49, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 4.19, "y": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 3.39, "y": 1.25, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.83, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 3.29, "y": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 4.04, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 3.18, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 3.61, "y": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.08, "y": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 3.66, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 2.98, "y": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 3.24, "y": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 2.88, "y": 2.32}, {"time": 2, "x": 2.83, "y": 2.43}, {"time": 2.0333, "x": 3.62, "y": 2.69}, {"time": 2.0667, "x": 2.87, "y": 2.37}, {"time": 2.1, "x": 3.28, "y": 2.5}, {"time": 2.1333, "x": 2.91, "y": 2.31}, {"time": 2.1667, "x": 3.19, "y": 2.4}, {"time": 2.2, "x": 2.95, "y": 2.26}, {"time": 2.2333, "x": 3.61, "y": 2.51}, {"time": 2.2667, "x": 2.99, "y": 2.2}, {"time": 2.3, "x": 3.52, "y": 2.4}, {"time": 2.3333, "x": 3.03, "y": 2.15}, {"time": 2.3667, "x": 5.05, "y": 2.38}, {"time": 2.4, "x": 3.07, "y": 2.09}, {"time": 2.4333, "x": 4.18, "y": 2.37}, {"time": 2.4667, "x": 3.11, "y": 2.03}, {"time": 2.5, "x": 4.43, "y": 2.23}, {"time": 2.5333, "x": 3.15, "y": 1.98}, {"time": 2.5667, "x": 4.3, "y": 1.75}, {"time": 2.6, "x": 3.19, "y": 1.92}, {"time": 2.6333, "x": 4.8, "y": 2.25}, {"time": 2.6667, "x": 3.23, "y": 1.86}, {"time": 2.7, "x": 4.04, "y": 2.01}, {"time": 2.7333, "x": 3.27, "y": 1.81}, {"time": 2.7667, "x": 4, "y": 2.09}, {"time": 2.8, "x": 3.31, "y": 1.75}, {"time": 2.8333, "x": 3.8, "y": 1.93}, {"time": 2.8667, "x": 3.35, "y": 1.69}, {"time": 2.9, "x": 3.84, "y": 1.87}, {"time": 2.9333, "x": 3.39, "y": 1.64}, {"time": 2.9667, "x": 3.89, "y": 1.82}, {"time": 3, "x": 3.43, "y": 1.58}, {"time": 3.0333, "x": 3.93, "y": 1.76}, {"time": 3.0667, "x": 3.47, "y": 1.53}, {"time": 3.1, "x": 3.49, "y": 1.5}, {"time": 3.7667}], "scale": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7}, {"time": 1.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5}, {"time": 2.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9}, {"time": 2.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 1.053, "y": 1.053}]}, "xiaya": {"translate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": -0.29, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.7, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.7, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.5, "y": -0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.65, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 0.54, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 0.54, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.54, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 0.54, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 0.54, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 0.88, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.4333}]}}, "deform": {"default": {"shipin1": {"images/shipin1": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "vertices": [0.15775, 5.39696, 0.1821, 3.71506, 0.1821, 3.71506, 0.92342, 2.57872, 0, 0, 0, 0, 0, 0, -0.45796, 2.51643], "curve": 0.25, "c3": 0.75}, {"time": 2, "vertices": [-3.48381, -1.27377, -3.63747, -4.16434, -2.93597, -5.88326, 0, 0, 0, 0, 0, 0, 0, 0, 0.43494, 0.45709], "curve": "stepped"}, {"time": 3, "vertices": [-3.48381, -1.27377, -3.63747, -4.16434, -2.93597, -5.88326, 0, 0, 0, 0, 0, 0, 0, 0, 0.43494, 0.45709], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "zuili": {"images/zuili": [{"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 22, "vertices": [0.28117, 0.82149, -0.0977, -0.91569, 0.91383, 2.66987, -0.31752, -2.97601], "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "offset": 26, "vertices": [1.67001, 3.77545, 0.62906, -3.72829, 0.55728, 1.25843, 0.2095, -1.24279, 0, 0, 0, 0, 4.18933, 3.76186, -1.08961, -4.47923], "curve": 0.25, "c3": 0.75}, {"time": 4}]}}}}, "hou": {"slots": {"yangguang2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3, "color": "ffffff00"}]}, "yanguang1": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"tui10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 7, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 22.87, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 22.66, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 6.93, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -25.93, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -8.02, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -17.53, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 18.07, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 23.94, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tui12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.07, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -4.89, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -31.04, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -12.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 13.45, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.11, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.23, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 23.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 27.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -22.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 31.63, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi9": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 15.45, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.66, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 3.17, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shen3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.62, "curve": 0.296, "c2": 0.89, "c3": 0.48}, {"time": 0.8333, "angle": -5.46, "curve": "stepped"}, {"time": 1.1667, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -9.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 3}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.4, "y": 1.12, "curve": 0.296, "c2": 0.89, "c3": 0.48}, {"time": 0.8333, "x": -3.44, "y": 2.76, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -3.44, "y": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -3.44, "y": 2.76, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -3.44, "y": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": -3.44, "y": 2.76}, {"time": 3}], "scale": [{"time": 0.5, "curve": 0.296, "c2": 0.89, "c3": 0.48}, {"time": 0.8333, "x": 1.02, "y": 1.02, "curve": "stepped"}, {"time": 2.6, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.41, "curve": 0.25, "c3": 0.75}, {"time": 3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.23, "y": 7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 8.91, "y": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 43.15, "y": 9.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 24.27, "y": 22.13, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 48.69, "y": -0.54, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 31.63, "y": -0.98, "curve": 0.25, "c3": 0.75}, {"time": 3}], "scale": [{"time": 0.5, "curve": 0.654, "c2": 0.02, "c3": 0.444, "c4": 0.99}, {"time": 0.6667, "x": 1.03, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 1.05, "y": 1.05}, {"time": 3}]}, "toumao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.076, "y": 1.204, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.35, "y": 1.395, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.087, "y": 1.386, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.449, "y": 1.587, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.216, "y": 1.192, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "xiaba": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "xiaba3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.77, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 8.11, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 3.25, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "re2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -10.57, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 12.84, "curve": 0.25, "c3": 0.75}, {"time": 3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 10.12, "y": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 19.28, "y": -5.3, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -2.77, "y": 9.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.49, "y": 7.47, "curve": 0.25, "c3": 0.75}, {"time": 3}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.228, "y": 1.137, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "re4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "re5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 3.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "chi2": {"scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.287, "y": 1.287, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 1.175, "y": 1.175, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "re1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -12.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -8.66, "curve": 0.25, "c3": 0.75}, {"time": 3}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.228, "y": 1.137, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shen": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 1.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.76, "curve": 0.654, "c2": 0.02, "c3": 0.444, "c4": 0.99}, {"time": 0.7667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -5.75, "y": 8.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -21.3, "curve": 0.654, "c2": 0.02, "c3": 0.444, "c4": 0.99}, {"time": 0.7667, "x": -13.42, "y": -5.03, "curve": "stepped"}, {"time": 2.6, "x": -13.42, "y": -5.03, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shen4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "tui4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 1.61, "curve": 0.25, "c3": 0.496}, {"time": 0.4333, "angle": -11.12, "curve": 0.595, "c3": 0.475}, {"time": 0.7333, "angle": 14.22, "curve": "stepped"}, {"time": 2.6, "angle": 14.22}, {"time": 3}]}, "tui3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.73, "curve": 0.25, "c3": 0.496}, {"time": 0.4333, "angle": 16.2, "curve": 0.595, "c3": 0.475}, {"time": 0.7333, "angle": -15.16, "curve": "stepped"}, {"time": 2.6, "angle": -15.16}, {"time": 3}], "translate": [{"curve": 0.245, "c3": 0.636, "c4": 0.55}, {"time": 0.1333, "x": 11.23, "y": 3.36, "curve": 0.25, "c3": 0.496}, {"time": 0.4333}]}, "tui8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.4, "curve": 0.25, "c3": 0.56}, {"time": 0.3, "angle": 42.76, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667, "angle": 34.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.01, "curve": "stepped"}, {"time": 2.6, "angle": -7.01}, {"time": 3}], "translate": [{"curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "x": 5.79, "y": 4.85, "curve": 0.25, "c3": 0.56}, {"time": 0.3, "x": -7.9, "y": -0.67, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 13.02, "y": 6.29, "curve": "stepped"}, {"time": 2.6, "x": 13.02, "y": 6.29}, {"time": 3}], "scale": [{"time": 0.1333, "curve": 0.25, "c3": 0.56}, {"time": 0.3, "x": 0.938, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667}]}, "tui9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.17, "curve": 0.25, "c3": 0.377, "c4": 1.01}, {"time": 0.3, "angle": -28.49, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667, "angle": -9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 19.51, "curve": "stepped"}, {"time": 2.6, "angle": 19.51}, {"time": 3}]}, "tui1": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.377, "c4": 1.01}, {"time": 0.3, "angle": 4.76, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667}], "translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.377, "c4": 1.01}, {"time": 0.3, "x": -22.02, "y": 15.95, "curve": 0.831, "c3": 0.859, "c4": 0.49}, {"time": 0.4667, "y": -4.48}, {"time": 0.5, "y": 0.9}, {"time": 0.5667}]}, "chi1": {"scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.287, "y": 1.287, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 1.175, "y": 1.175, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "ying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.943, "y": 0.943, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.033, "y": 1.033, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.957, "y": 0.957, "curve": "stepped"}, {"time": 2.7, "x": 0.957, "y": 0.957, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tou": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.592}, {"time": 0.6667, "x": 15.31, "y": -6.19, "curve": 0.25, "c3": 0.592}, {"time": 1.5, "x": -17.82, "y": 19.27, "curve": 0.414, "c3": 0.75}, {"time": 2.3333, "x": -1.14, "y": 16.76, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "yan2": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.02, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.31, "y": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.46, "y": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.64, "y": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "yan1": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.02, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.65, "y": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.77, "y": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -1.03, "y": 6.43, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shangzui2": {"translate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.83, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 7.51, "y": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 11.74, "y": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 2.8333}]}, "shangchi": {"translate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.1, "y": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": -4.76, "y": -2.7, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shangzui6": {"rotate": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}], "translate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.98, "y": 2.99, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.8667, "x": 1.31, "y": 3.45, "curve": 0.321, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.9, "x": 1.23, "y": 3.2, "curve": 0.323, "c2": 0.27, "c3": 0.657, "c4": 0.61}, {"time": 0.9333, "x": 1.69, "y": 3.56, "curve": 0.325, "c2": 0.29, "c3": 0.658, "c4": 0.63}, {"time": 0.9667, "x": 1.73, "y": 3.61, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.0333, "x": 2.38, "y": 4.15, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.0667, "x": 3.3, "y": 4.14, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.1, "x": 3.12, "y": 4.77, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.1333, "x": 3.91, "y": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 1.1667, "x": 3.9, "y": 5.42, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.2, "x": 4.82, "y": 5.83, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.2333, "x": 4.69, "y": 6.07, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 1.2667, "x": 5.49, "y": 6.64, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.3, "x": 5.45, "y": 6.7, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 1.3333, "x": 6.2, "y": 7.13, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 1.3667, "x": 6.14, "y": 7.27, "curve": 0.342, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 1.4, "x": 7.19, "y": 7.55, "curve": 0.343, "c2": 0.39, "c3": 0.677, "c4": 0.73}, {"time": 1.4333, "x": 6.66, "y": 7.71, "curve": 0.346, "c2": 0.43, "c3": 0.679, "c4": 0.77}, {"time": 1.4667, "x": 7.38, "y": 7.91, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.5, "x": 6.89, "y": 7.9, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 1.5333, "x": 7.71, "y": 8.04, "curve": 0.323, "c2": 0.23, "c3": 0.656, "c4": 0.56}, {"time": 1.5667, "x": 6.99, "y": 7.94, "curve": 0.324, "c2": 0.27, "c3": 0.658, "c4": 0.61}, {"time": 1.6, "x": 7.86, "y": 8.15, "curve": 0.325, "c2": 0.29, "c3": 0.659, "c4": 0.63}, {"time": 1.6333, "x": 7.2, "y": 8.02, "curve": 0.262, "c2": 0.28, "c3": 0.599, "c4": 0.62}, {"time": 1.7, "x": 9.64, "y": 8.29, "curve": 0.289, "c2": 0.3, "c3": 0.625, "c4": 0.63}, {"time": 1.7667, "x": 7.86, "y": 8.28, "curve": 0.304, "c2": 0.31, "c3": 0.639, "c4": 0.64}, {"time": 1.8333, "x": 9.1, "y": 8.8, "curve": 0.313, "c2": 0.32, "c3": 0.648, "c4": 0.65}, {"time": 1.9, "x": 8.48, "y": 8.52, "curve": 0.32, "c2": 0.32, "c3": 0.655, "c4": 0.66}, {"time": 1.9667, "x": 9.69, "y": 9.09, "curve": 0.325, "c2": 0.33, "c3": 0.66, "c4": 0.66}, {"time": 2.0333, "x": 9.09, "y": 8.76, "curve": 0.33, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.1, "x": 10.07, "y": 9.22, "curve": 0.334, "c2": 0.33, "c3": 0.669, "c4": 0.67}, {"time": 2.1667, "x": 9.7, "y": 9, "curve": 0.339, "c2": 0.34, "c3": 0.674, "c4": 0.67}, {"time": 2.2333, "x": 11.04, "y": 9.32, "curve": 0.344, "c2": 0.34, "c3": 0.679, "c4": 0.67}, {"time": 2.3, "x": 10.33, "y": 9.25, "curve": 0.341, "c2": 0.34, "c3": 0.675, "c4": 0.67}, {"time": 2.3333, "x": 10.5, "y": 9.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}, {"time": 1.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.4333}, {"time": 1.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.8333}, {"time": 1.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3}]}, "shangzui5": {"rotate": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}], "translate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 4.02, "y": -3.42, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.8667, "x": 4.34, "y": -2.9, "curve": 0.321, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.9, "x": 4.21, "y": -2.98, "curve": 0.323, "c2": 0.27, "c3": 0.657, "c4": 0.61}, {"time": 0.9333, "x": 4.63, "y": -2.4, "curve": 0.325, "c2": 0.29, "c3": 0.658, "c4": 0.63}, {"time": 0.9667, "x": 4.6, "y": -2.09, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 1.0333, "x": 5.11, "y": -0.93, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.0667, "x": 5.96, "y": -0.59, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.1, "x": 5.69, "y": 0.39, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.1333, "x": 6.4, "y": 1.16, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 1.1667, "x": 6.3, "y": 1.79, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.2, "x": 7.13, "y": 2.58, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.2333, "x": 6.92, "y": 3.2, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 1.2667, "x": 7.63, "y": 4.13, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.3, "x": 7.52, "y": 4.55, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 1.3333, "x": 8.19, "y": 5.32, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 1.3667, "x": 8.05, "y": 5.77, "curve": 0.342, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 1.4, "x": 9.03, "y": 6.33, "curve": 0.343, "c2": 0.39, "c3": 0.677, "c4": 0.73}, {"time": 1.4333, "x": 8.46, "y": 6.7, "curve": 0.346, "c2": 0.43, "c3": 0.679, "c4": 0.77}, {"time": 1.4667, "x": 9.15, "y": 7.06, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.5, "x": 8.64, "y": 7.12, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 1.5333, "x": 9.47, "y": 7.19, "curve": 0.323, "c2": 0.23, "c3": 0.656, "c4": 0.56}, {"time": 1.5667, "x": 8.78, "y": 6.91, "curve": 0.324, "c2": 0.27, "c3": 0.658, "c4": 0.61}, {"time": 1.6, "x": 9.68, "y": 6.89, "curve": 0.325, "c2": 0.29, "c3": 0.659, "c4": 0.63}, {"time": 1.6333, "x": 9.06, "y": 6.46, "curve": 0.262, "c2": 0.28, "c3": 0.599, "c4": 0.62}, {"time": 1.7, "x": 11.62, "y": 5.85, "curve": 0.289, "c2": 0.3, "c3": 0.625, "c4": 0.63}, {"time": 1.7667, "x": 9.95, "y": 5.03, "curve": 0.304, "c2": 0.31, "c3": 0.639, "c4": 0.64}, {"time": 1.8333, "x": 11.31, "y": 4.76, "curve": 0.313, "c2": 0.32, "c3": 0.648, "c4": 0.65}, {"time": 1.9, "x": 10.79, "y": 3.7, "curve": 0.32, "c2": 0.32, "c3": 0.655, "c4": 0.66}, {"time": 1.9667, "x": 12.12, "y": 3.49, "curve": 0.325, "c2": 0.33, "c3": 0.66, "c4": 0.66}, {"time": 2.0333, "x": 11.62, "y": 2.38, "curve": 0.33, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.1, "x": 12.72, "y": 2.06, "curve": 0.334, "c2": 0.33, "c3": 0.669, "c4": 0.67}, {"time": 2.1667, "x": 12.46, "y": 1.06, "curve": 0.339, "c2": 0.34, "c3": 0.674, "c4": 0.67}, {"time": 2.2333, "x": 13.91, "y": 0.57, "curve": 0.344, "c2": 0.34, "c3": 0.679, "c4": 0.67}, {"time": 2.3, "x": 13.32, "y": -0.32, "curve": 0.341, "c2": 0.34, "c3": 0.675, "c4": 0.67}, {"time": 2.3333, "x": 13.54, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 2.8333}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}, {"time": 1.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.4333}, {"time": 1.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.8333}, {"time": 1.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3}]}, "shangzui": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 10.31, "y": 0.91, "curve": 0.882, "c2": 0.01, "c3": 0.75}, {"time": 2.6, "x": 12.88, "y": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tou2": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.92, "y": 2.86, "curve": 0.467, "c3": 0.362}, {"time": 2, "x": 0.99, "y": -1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.05, "y": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tou3": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.04, "y": -0.23, "curve": 0.467, "c3": 0.362}, {"time": 2, "x": -1.91, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 2.5}]}, "shangzui3": {"translate": [{"time": 0.3333}, {"time": 0.8333, "x": 9.63, "y": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 6.99, "y": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "xiazui": {"translate": [{"x": 23.52, "y": 5.14, "curve": "stepped"}, {"time": 0.5, "x": 23.52, "y": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -12.3, "y": -2.96, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 1.5, "x": -21.27, "y": -3.53, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 2.3333, "x": -24.46, "y": -5.2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 23.52, "y": 5.14}], "scale": [{"time": 0.5}, {"time": 1, "y": 1.144}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.035, "y": 1.089, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "zuili": {"rotate": [{"time": 0.5, "curve": 0.341, "c2": 0.36, "c3": 0.688, "c4": 0.74}, {"time": 1.1, "angle": 8.44, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 21, "curve": 0.25, "c3": 0.75}, {"time": 3}], "scale": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": 1.083, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 1.1333, "x": 1.025, "y": 1.112, "curve": 0.326, "c2": 0.23, "c3": 0.659, "c4": 0.56}, {"time": 1.1667, "x": 0.996, "y": 1.083, "curve": 0.326, "c2": 0.27, "c3": 0.66, "c4": 0.61}, {"time": 1.2, "x": 1.067, "y": 1.164, "curve": 0.327, "c2": 0.29, "c3": 0.66, "c4": 0.62}, {"time": 1.2333, "x": 0.988, "y": 1.082, "curve": 0.328, "c2": 0.3, "c3": 0.661, "c4": 0.64}, {"time": 1.2667, "x": 1.05, "y": 1.156, "curve": 0.328, "c2": 0.31, "c3": 0.662, "c4": 0.64}, {"time": 1.3, "x": 0.977, "y": 1.081, "curve": 0.329, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.3333, "x": 1.004, "y": 1.117, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.3667, "x": 0.964, "y": 1.079, "curve": 0.33, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.4, "x": 0.995, "y": 1.121, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.4333, "x": 0.95, "y": 1.077, "curve": 0.331, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.4667, "x": 0.971, "y": 1.11, "curve": 0.331, "c2": 0.33, "c3": 0.664, "c4": 0.66}, {"time": 1.5, "x": 0.934, "y": 1.076, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.5333, "x": 0.979, "y": 1.137, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.5667, "x": 0.918, "y": 1.074, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 1.6, "x": 0.948, "y": 1.118, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6333, "x": 0.901, "y": 1.072, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6667, "x": 0.932, "y": 1.118, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 1.7, "x": 0.883, "y": 1.07, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.7333, "x": 0.892, "y": 1.09, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.7667, "x": 0.866, "y": 1.068, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 1.8, "x": 0.882, "y": 1.097, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 1.8333, "x": 0.849, "y": 1.066, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 1.8667, "x": 0.882, "y": 1.118, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 1.9, "x": 0.832, "y": 1.064, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.9333, "x": 0.841, "y": 1.085, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.9667, "x": 0.815, "y": 1.062, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.68}, {"time": 2, "x": 0.847, "y": 1.112, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.0333, "x": 0.8, "y": 1.06, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 2.0667, "x": 0.835, "y": 1.115, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 2.1, "x": 0.786, "y": 1.058, "curve": 0.338, "c2": 0.35, "c3": 0.671, "c4": 0.69}, {"time": 2.1333, "x": 0.805, "y": 1.092, "curve": 0.338, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 2.1667, "x": 0.774, "y": 1.057, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.7}, {"time": 2.2, "x": 0.811, "y": 1.114, "curve": 0.34, "c2": 0.38, "c3": 0.673, "c4": 0.71}, {"time": 2.2333, "x": 0.764, "y": 1.056, "curve": 0.34, "c2": 0.39, "c3": 0.674, "c4": 0.73}, {"time": 2.2667, "x": 0.788, "y": 1.093, "curve": 0.341, "c2": 0.44, "c3": 0.674, "c4": 0.77}, {"time": 2.3, "x": 0.759, "y": 1.055, "curve": 0.342, "c2": 0.66, "c3": 0.675}, {"time": 2.3333, "x": 0.758, "y": 1.055, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao7": {"translate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.32, "y": 0.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.05, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.51, "y": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "tou4": {"translate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.47, "y": -1.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.35, "y": -2.54, "curve": 0.25, "c3": 0.75}, {"time": 2.3333}]}, "toumao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.83, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -0.86, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -0.71, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -0.54, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.25, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.88, "curve": 0.25, "c3": 0.75}, {"time": 3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 11.71, "y": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 10.43, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 9.24, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 8.15, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -9.18, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -7.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.11, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 10, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "toumao16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "shangzui4": {"translate": [{"time": 0.5}, {"time": 0.8333, "x": 4.2, "y": -0.45}, {"time": 0.8667, "x": 5.04, "y": 0.02}, {"time": 0.9, "x": 4.07, "y": -0.16}, {"time": 0.9333, "x": 4.24, "y": 0.16}, {"time": 0.9667, "x": 3.93, "y": 0.13}, {"time": 1, "x": 5.72, "y": -0.07}, {"time": 1.0333, "x": 3.79, "y": 0.41}, {"time": 1.0667, "x": 4.29, "y": 0.25}, {"time": 1.1, "x": 3.65, "y": 0.7}, {"time": 1.1333, "x": 5.24, "y": 0.75}, {"time": 1.1667, "x": 3.52, "y": 0.99}, {"time": 1.2, "x": 4.34, "y": 1.17}, {"time": 1.2333, "x": 3.38, "y": 1.28}, {"time": 1.2667, "x": 4.09, "y": 1.64}, {"time": 1.3, "x": 3.24, "y": 1.56}, {"time": 1.3333, "x": 4.06, "y": 1.83}, {"time": 1.3667, "x": 3.1, "y": 1.85}, {"time": 1.4, "x": 3.82, "y": 1.41}, {"time": 1.4333, "x": 2.96, "y": 2.14}, {"time": 1.4667, "x": 4.2, "y": 2.42}, {"time": 1.5, "x": 2.83, "y": 2.43}, {"time": 1.5333, "x": 4.6, "y": 1.6}, {"time": 1.5667, "x": 2.88, "y": 2.35}, {"time": 1.6, "x": 3.68, "y": 2.49}, {"time": 1.6333, "x": 2.93, "y": 2.28, "curve": 0.25, "c2": 0.24, "c3": 0.588, "c4": 0.58}, {"time": 1.7, "x": 5.09, "y": 2.32, "curve": 0.286, "c2": 0.29, "c3": 0.623, "c4": 0.62}, {"time": 1.7667, "x": 3.07, "y": 2.09, "curve": 0.303, "c2": 0.31, "c3": 0.639, "c4": 0.64}, {"time": 1.8333, "x": 4.06, "y": 2.4, "curve": 0.314, "c2": 0.32, "c3": 0.65, "c4": 0.66}, {"time": 1.9, "x": 3.19, "y": 1.91, "curve": 0.322, "c2": 0.33, "c3": 0.658, "c4": 0.67}, {"time": 1.9667, "x": 4.16, "y": 2.28, "curve": 0.329, "c2": 0.34, "c3": 0.665, "c4": 0.68}, {"time": 2.0333, "x": 3.31, "y": 1.74, "curve": 0.336, "c2": 0.35, "c3": 0.671, "c4": 0.69}, {"time": 2.1, "x": 4.05, "y": 2, "curve": 0.342, "c2": 0.37, "c3": 0.678, "c4": 0.71}, {"time": 2.1667, "x": 3.41, "y": 1.6, "curve": 0.35, "c2": 0.4, "c3": 0.686, "c4": 0.74}, {"time": 2.2333, "x": 4.48, "y": 1.73, "curve": 0.361, "c2": 0.48, "c3": 0.697, "c4": 0.82}, {"time": 2.3, "x": 3.48, "y": 1.5, "curve": 0.354, "c2": 0.66, "c3": 0.688}, {"time": 2.3333, "x": 3.49, "y": 1.5}, {"time": 2.8333}], "scale": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}, {"time": 1.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.4333}, {"time": 1.4667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.8333}, {"time": 1.8667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.2333}, {"time": 2.2667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 2.3}]}, "xiaya": {"translate": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -1.01, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.7, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.7, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 1.7, "y": -0.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 1.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.97, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.4}]}}, "deform": {"default": {"shipin1": {"images/shipin1": [{"time": 0.3333, "curve": 0.25, "c3": 0.592}, {"time": 0.6667, "vertices": [0.15775, 5.39696, 0.1821, 3.71506, 0.1821, 3.71506, 0.92342, 2.57872, 0, 0, 0, 0, 0, 0, -0.45796, 2.51643], "curve": 0.25, "c3": 0.592}, {"time": 1.5, "vertices": [-5.47159, -12.38513, -6.07541, -16.24365, -5.17926, -14.61821, 0, 0, 0, 0, 0, 0, 0, 0, -0.32022], "curve": 0.414, "c3": 0.75}, {"time": 2.3333, "vertices": [-3.30389, -1.75638, -3.32086, -3.95306, -2.86333, -5.10538], "curve": 0.25, "c3": 0.75}, {"time": 3}]}, "zuili": {"images/zuili": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "vertices": [-1.05786, -4.03562, -0.88531, 4.24146, -0.88531, 4.24153, -1.05786, -4.03562, -0.88531, 4.24146, -0.88531, 4.24153, -1.05786, -4.03562, -0.88531, 4.24146, -0.88531, 4.24153, -1.15372, -6.19744, -2.03902, 5.74007, -0.13928, -5.32749, -2.31247, 4.67191, 9.40399, -2.09716, -7.12402, -1.07462, 0, 0, 0, 0, 2.95123, 16.84346, 6.28204, -14.98727, -3.85999, 7.96557, 6.48561, -5.9919, -3.72327, 4.6216, 4.55696, -3.09839, 5.12141, -2.71861, -3.08662, -5.36188], "curve": 0.25, "c3": 0.75}, {"time": 3}]}}}, "events": [{"time": 0.7667, "name": "roar"}]}, "wait": {"bones": {"tui10": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.4, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 5.7333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 8.3667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui20": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 1, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 19.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 19.54, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6.3333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 19.54, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 19.54, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui17": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.9333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 28.77, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 28.77, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6.2667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 28.77, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": 28.77, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui18": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.8667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 28.55, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 28.55, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6.2, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "angle": 28.55, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 10.1, "angle": 28.55, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui15": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.8, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6.1333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 8.7667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui16": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.7333, "angle": -23.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -23.7, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6.0667, "angle": -23.7, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -23.7, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui13": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.6667, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 12.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 12.53, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 6, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 12.53, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": -6.81, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "angle": 12.53, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui14": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.6, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 6.47, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 6.47, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 5.9333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 6.47, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 6.47, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui11": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.5333, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 5.8667, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 8.5, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "tui12": {"rotate": [{"curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 0.4667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 5.3333, "curve": 0.201, "c2": 0.17, "c3": 0.75}, {"time": 5.8, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 2.58, "curve": 0.25, "c3": 0.813, "c4": 0.81}, {"time": 10.6667}]}, "chi4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -15.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -15.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -15.51, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -15.51, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 19.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 19.78, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 19.78, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 19.78, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 19.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 19.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "angle": 19.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "angle": 19.76, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "chi7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "angle": 11.24, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "shen3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.264}, {"time": 0.8667, "x": -1.91, "y": 3.29, "curve": 0.665, "c3": 0.75}, {"time": 1.4333}]}, "toumao": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.9, "y": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.9, "y": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 4.9, "y": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 4.9, "y": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "curve": 0.25, "c3": 0.75}, {"time": 7.8, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.3, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "curve": 0.25, "c3": 0.75}, {"time": 9.8, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.0667, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 8.9667, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "angle": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "toumao8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 9.1, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "zuili": {"scale": [{"x": 0.832}]}, "xiaba": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "xiaba3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "re2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "re4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -2.24, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "re5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.7, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "shen": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 1.21, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "tui2": {"rotate": [{"time": 0.3333, "curve": 0.426, "c3": 0.567}, {"time": 0.6, "angle": 8.17, "curve": 0.426, "c3": 0.567}, {"time": 0.8667}], "translate": [{"time": 0.3333, "curve": 0.426, "c3": 0.567}, {"time": 0.6, "x": 3.42, "y": 8.4, "curve": 0.426, "c3": 0.567}, {"time": 0.8667}]}, "tui3": {"rotate": [{"time": 0.3333, "curve": 0.426, "c3": 0.567}, {"time": 0.6, "angle": 16.5, "curve": 0.426, "c3": 0.567}, {"time": 0.8667}]}, "tui4": {"rotate": [{"time": 0.3333, "curve": 0.426, "c3": 0.567}, {"time": 0.6, "angle": -17.09, "curve": 0.426, "c3": 0.567}, {"time": 0.8667}]}, "tui1": {"rotate": [{"time": 0.8667, "curve": 0.37, "c3": 0.623}, {"time": 1.1667, "angle": 2.54, "curve": 0.37, "c3": 0.623}, {"time": 1.4667}], "translate": [{"time": 0.8667, "curve": 0.37, "c3": 0.623}, {"time": 1.1667, "x": -4.36, "y": 7.64, "curve": 0.37, "c3": 0.623}, {"time": 1.4667}]}, "tui9": {"rotate": [{"time": 0.8667, "curve": 0.37, "c3": 0.623}, {"time": 1.1667, "angle": -9.15, "curve": 0.37, "c3": 0.623}, {"time": 1.4667}]}, "tui8": {"rotate": [{"time": 0.8667, "curve": 0.37, "c3": 0.623}, {"time": 1.1667, "angle": 9.64, "curve": 0.37, "c3": 0.623}, {"time": 1.4667}]}, "yan1": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.35, "y": 0.06, "curve": "stepped"}, {"time": 3.3333, "x": 0.35, "y": 0.06, "curve": 0.25, "c3": 0.75}, {"time": 3.7667}]}, "yan2": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -0.91, "y": -1.3, "curve": "stepped"}, {"time": 3.3333, "x": -0.91, "y": -1.3, "curve": 0.25, "c3": 0.75}, {"time": 3.7667}]}, "xiazui": {"translate": [{"x": 23.52, "y": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 18.02, "y": 3.94, "curve": "stepped"}, {"time": 3.3333, "x": 18.02, "y": 3.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 23.52, "y": 5.14}]}}}}}