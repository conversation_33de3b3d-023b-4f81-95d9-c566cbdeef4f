[{"Name": "start", "Index": 0, "DurTime": 3, "DelayTime": 0, "ActionOperate": "EmptyNode", "ActionOperateParam": []}, {"Name": "CreatRoot", "Index": -1, "DurTime": 0, "DelayTime": 1, "ParentNode": "start", "ActionOperate": "CreateObject", "ActionOperateParam": ["1703_chunv_effect_root", "", "1703_root"]}, {"Name": "CreatScreenEffect", "Index": -1, "DurTime": 0, "DelayTime": 1, "ParentNode": "start", "ActionOperate": "CreateObject", "ActionOperateParam": ["1703_chunv_effect_screen", "", "1703_screen_effect"]}, {"Name": "CreatModelPrefab", "Index": -1, "DurTime": 0, "DelayTime": 1, "ParentNode": "start", "ActionOperate": "CreateObject", "ActionOperateParam": ["1703_chunv<PERSON><PERSON>_skin_prefab", "1703_root", "1703_skin_prefab"]}, {"Name": "loop1", "Index": 1, "DurTime": 0, "DelayTime": 3, "ParentNode": "loop1", "ActionOperate": "DestroyObject", "ActionOperateParam": ["effect_prefab_cylg_1"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 1, "ConditionType": 2, "Target": null}}, {"Name": "end1", "Index": 1, "DurTime": 5, "DelayTime": 0, "ActionOperate": "DestroyObject", "ActionOperateParam": ["carrier_prefab_cylg"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 1, "ConditionType": 2, "Target": null}}, {"Name": "loop2", "Index": 2, "DurTime": 5, "DelayTime": 0, "ActionOperate": "CreateObject", "ActionOperateParam": ["effect_prefab_cylg", "", "effect_prefab_cylg_2"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 2, "ConditionType": 3, "Target": null}}, {"Name": "end2", "Index": 2, "DurTime": 5, "DelayTime": 0, "ActionOperate": "DestroyObject", "ActionOperateParam": ["carrier_prefab_cylg"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 2, "ConditionType": 2, "Target": null}}, {"Name": "loop3", "Index": 3, "DurTime": 5, "DelayTime": 0, "ActionOperate": "CreateObject", "ActionOperateParam": ["effect_prefab_cylg", "", "effect_prefab_cylg_3"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 3, "ConditionType": 3, "Target": null}}, {"Name": "end3", "Index": 3, "DurTime": 5, "DelayTime": 0, "ActionOperate": "DestroyObject", "ActionOperateParam": ["carrier_prefab_cylg"], "Conditional": {"ParameterName1": "_round", "ParameterName2": null, "ParameterValue": 3, "ConditionType": 2, "Target": null}}]