{"name": "com.ourpalm.ilruntime", "displayName": "ILRuntime", "version": "2.1.0", "unity": "2018.1", "description": "ILRuntime\nC#热更解译器\n中文在线文档: https://ourpalm.github.io/ILRuntime/\n\rQQ群：512079820\n## 更新记录\n\r### V2.1.0\r\n\r\n#### Breaking Change\r\n- 由于调试协议变更，2.1.0以上的版本调试插件不和以前版本相兼容，需要从VS和VSCode插件商店安装新版本调试插件\r\n\r\n#### 新功能\r\n- 调试器支持多工程同时调试\r\n- 调试器新增自动发现可调式实例的功能，在同一局域网网络时可自动发现所有可调式实例\r\n- 新增VSCode调试插件， 同时支持Windows和Mac\r\n- 新增VS2022调试器支持\r\n\r\n#### Bug修正\r\n- 修复一个实例化时不能找到正确的构造函数的Bug\r\n- Prewarm接口可预热async方法\r\n- 修复一些情况下使用泛型约束会报错的问题\r\n- 修复寄存器模式有时在for循环中使用值类型行为不符合预期的问题\r\n- 修复一些用发下GetCustomAttribute不能获取指定Attribute的问题\r\n- 修复Stfld与Ldfld当对象为适配器时的异常\r\n- 修复热更中无法使用Delegate.CreateDelegate的问题\r\n- 修复CLR绑定漏分析async方法和event加减的问题\r\n- 修复纯热更内event依然需要适配器的问题\r\n- 修复寄存器模式下构造函数参数比较多时报错的问题\r\n- 优化异常日志输出，避免热更内栈信息被丢弃\r\n- 修复一些用法下interface强转结果不符合预期的问题\r\n- 修复MethodInfo.CreateDelegate报错的问题\r\n- 修复寄存器模式有些情况下foreach内部运行异常的问题\r\n- 修复寄存器模式有时候被内联的方法中值类型被破坏的问题\r\n- 修复寄存器模式下有时值类型复制出错的问题\r\n- 修复寄存器模式使用JITImmediately时产生预期外的GC Alloc的问题\r\n- 修复寄存器模式偶现栈损坏的问题\r\n- 修复寄存器模式调用多态方法时偶现报错的问题\r\n- 修复寄存器模式下一些情况ref设置变量值不生效的问题\r\n- 修复寄存器模式下一个委托里使用值类型报错的问题\r\n- 修复一个隐式强转值类型时报错的问题\r\n- 修复泛型方法内断点无法命中的问题\r\n- 修复在调试async方法时卡死的问题\r\n- 修复泛型类型中添加委托出错的问题\r\n- 修复Type.GetField接口同个字段返回多次的问题\r\n- 修复Delegate.Target无法使用的问题\r\n- 修复多层嵌套值类型出现异常的问题\r\n- 修复嵌套using时无法正确调用Dispose的问题\r\n- 修复一个导致弃用Appdomain内存无法正确释放的问题\r\n- 修正一个值类型在无值类型绑定时字段赋值不正确的问题\r\n- 修正一个访问主工程类型字段偶现读错字段的问题\r\n- 修复反射时指定DeclaredOnly依然会返回父类字段的问题\r\n- 修复一些用法下Property.SetValue报错的问题\r\n- 修复通过反射创建泛型方法报错的问题\r\n- 修复泛型方法内对变量赋null后运行报错的问题\r\n- 修复一个由于哈希碰撞导致的字段读取串行的问题\r\n- 修复FieldInfo.GetRawConstantValue报错的问题", "keywords": ["ILRuntime", "ilrt", "hotfix"], "author": {"name": "Ourpalm", "email": "<EMAIL>", "url": "https://github.com/Ourpalm/ILRuntime"}, "category": "Hotfix", "samples": [{"displayName": "Demo", "description": "示例中包含了ILRuntime的基础用法，反射的用法，CLR重定向/绑定，值类型绑定，Json序列化等的使用方式", "path": "Samples~/Basic Demo"}], "type": "library", "upmCi": {"footprint": "b6900b8f86f0824758fdc4df8d94ed671f568ecd"}, "repository": {"url": "https://github.com/Ourpalm/ILRuntimeU3D", "revision": "c51c1db6c09f3b0de6cbfbd1bbde64eb513b3de2"}}