{"FileSign": "596f6f41727453657474696e67", "FileVersion": "1.0", "Scanners": [{"ScannerGUID": "b4d32d9f-1315-4a65-b1d0-e09308785968", "ScannerName": "Bitmap Scanner", "ScannerDesc": "Font", "ScannerSchema": "0861299f7eb22384ab76ca7692d93851", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetBitmap", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/Font"}], "WhiteList": []}, {"ScannerGUID": "93e31de5-7157-49ab-b4ed-0dcad653560c", "ScannerName": "Texture Scanner", "ScannerDesc": "UI图集", "ScannerSchema": "607fdaf24052a0e49ab0e86d474209da", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/Atlas"}], "WhiteList": ["06e76cc72ee89664f8fdd64cfc8b7f03"]}, {"ScannerGUID": "c2297913-292e-49dc-b338-8ba5701c2311", "ScannerName": "Texture Scanner", "ScannerDesc": "UI图片", "ScannerSchema": "6ad7a7dca4dcfbb4ca7a57e1bfcfd3ec", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/Images"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArt"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArtCommon"}], "WhiteList": ["16165168fd36f6e4fbacf42618bc53c4", "58b9bc4bb1bf34e498764e446c8e9bfe", "5238a6830ff601e4099442dd791fc2aa", "fd6465fd76f2b7e47952b194dc9721dc", "c4fe4fdea2386544dbb35592a3c7d411", "5170fd84b912abe448b7e32732f81097"]}, {"ScannerGUID": "86ccfd7a-9529-4590-acc3-eaea1931d269", "ScannerName": "Texture Scanner", "ScannerDesc": "BOSS", "ScannerSchema": "9bc4eb18fb8cb7d4f845f0db26f32aa7", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/FishBoss/PrefabArt"}, {"CollectPath": "Assets/Fishes/GameBase/PrefabArt"}], "WhiteList": ["6f369be2f9749eb4c89bf9072967d296", "0a91613e2d9477b4eba87a924680b0d8", "65b154ff670ee5d4394032f31cdecd1d", "78b2ef915f0beec418f472c54d19b30d", "75f39e12db64348418d766ce42e19a26", "2d7228c87836f574380e5857981a00c5", "5fdfa7f75690b0143b1db0e4e189580e", "dfb5d10fc7529d349b784de39938ee3c", "70f0e0dc392dec845ae4286b5f917c0f", "cd81492fbd71f0945b7a78f1600eabda", "3047ef17db01d5f4399390bdedb985b5", "acee28331be83c84993f5e4abf84218b", "a5158a3ed4424704091d4ee141668b30", "3101e4f72fc1e80429ea752467e34335", "fab01d150bed5bb43b42cd23bce2486d", "3d43b77804513df44a049e501114b137", "4fa2e2cd96075874c8715d2c155b7177", "bb74d6077c2fed34282d1605e1b71e3d", "7f1e23a325c7ddd41939b0f2170fbfd7", "bc9715f7923e69c4ca1dafab1f8afa7c", "647f375823e9f0f4780aab51ed9cfa72", "63290a8d1f01ef244961850372bb9699", "25fa734187f108546bc32649f0b985f7", "b8d5e1b9e8ad3234bbe99431bd3b29f6", "08eba6678d7ae0b42998f1d0f69ceb48", "c183ab1c75195ac4f9f539b39afcd1c5", "affbd2474197c1846a471a5bcd32f77e", "ba9e5c1b60e8ce942aadc1906f236962", "257da8a9e13d05d489f053bf234ef23e", "d78f347457ae3b449b989bfa899940e6", "4c1340984a5babf4cb950d02bd64dbce", "26b1a59e3b6c5974ebd41da9e7eda7c5", "f7f8d91401d57814b9443feac86f6748", "00f2a20df0d378d448538867e80c5c69", "8b8781406544689408606bcb3608af62", "0450dc3363aba134389a7c1cde3c77e8", "27ed736a4a9027f45beb1e14fbb8a4a7", "a0c5cc7ef77432f42833b5cc166a5fe4", "e76d72fea5a7e384f9aab6afc278fc96", "cad4c251de3358b49a001fdf15cbdf22", "cabe01f8b6bfb85438e9d00c99976b38", "a7421fb70af20ff4c8e09c7033d86e71", "c98203be813f28546abefc716f336e13", "36596c93f21ccb346994e53f611efeab", "36ac56e2e38e8df44965dbfe685529c8", "2cd0864b1ff4d4e4c986cc9ff3c3f72b", "e66e3c48882feaf4696136e17cc02d37", "8012b439d70db01488cc01a3a82e0bf6", "d653c839bc8996a4d85117e0d3add835", "3b0e998efdb818a4ba04a66d87992f1f", "94348a5b4efe8814781c995f51954197", "d5ecfc0e7c3128d4a85245dc91681dd0", "34be1b30effa065418a9a205894d6956", "cbc617268bba4b943bb31c9435afc86f", "c3e1e23ed1e586a49b2ade7259b4984c", "91fbcb74ad18a4b4e8a93ad127a478a6", "4e2baffe42207ce409d718320f0fec0d", "d71c8af9a4daa9f4b96b29aa7294ebaf", "d356276f801afa9478599593e8a60872", "ff061cda238d30243b27b3e419f1e504", "8fc54c85431b9b14d8774e305303e85a", "e61da6c0bd27a0843a7f30811882b24e", "e626eab49f93893438a46662eb2aea38", "7b7d2c3b7bbaa2f4c8579d108ebfac89", "ee900103f310e04488445e704a3d9ecb", "c4594eab332500f40b8d7de62b2ea5a4", "392a47b94fec73449a0a00b27eb03f13", "c86925351faea9b47802404d2eeb61ef", "624a73a906a44ba47a8d552578b861d4", "0b71d2a0f94f36a46b493eb3fc19be8d", "269426da26c880d4c9f7c346f24a5d58", "140e9f8cac8070646b3470dcdfc3e65c", "a753a5fba391a8a4b8e173dea9e3f138", "1f2d8f4f6c858e446b2f46df2c27bc81", "d6f544555b764f2439f4c40c4827c846", "b570dca72e2cd0a49b722a2ca6b2cff0", "43e5f0deb387f6942a386f6502d93461", "cbd3a3dd2064a73499660c54346bd482", "6e383cfd151aaf341a2f4f0ac57cd966", "659bebb412428e14187bf557ba5f50d0", "26b7471a925dd244290bbca76ffd2dcb", "0ef3bade203adaf40b8c6589a4db4ca2", "ce5907baa17081447abcaf5bda1813fc", "7a8db0bec8df2ba4fb26c4e214ec3d4b", "e8025c10e036d50488144f123ea2d509", "e9ea0f0e91456764796542b5f52ed065", "c9cd1ac1070f07743b28ba8534cf536f", "a665676beeb2a0b4cb8a2fa9b3dfc029", "6cadf63a32fc4d944b19ce3d938c563a", "8c78fe2c434e2e848b38821f0b858df5", "eab5a079de2fad64396270bff88d4696", "004d6fdf94c4bff4ab380e8f1c0ad6f1", "8b4e1653c6990564fa89dfe591ba9c53", "680cc96594a283d41b9341b795b4e047"]}, {"ScannerGUID": "d356aeb8-a746-4943-8c75-8e312ed07dac", "ScannerName": "Texture Scanner", "ScannerDesc": "TA", "ScannerSchema": "f820e89a133aef8429549aeab2804f92", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Effect/texture_reflect"}], "WhiteList": []}, {"ScannerGUID": "2681ccbd-d110-48a0-ba97-9637ae8b4f86", "ScannerName": "Texture Scanner", "ScannerDesc": "特效", "ScannerSchema": "85e31112a09b61146841f5c7734968a9", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Effect/texture"}, {"CollectPath": "Assets/Effect/texture_base"}], "WhiteList": ["cd82cade07884424a8a9e8fb199a90d6", "5fa7e68644e08bf41aa53a322e306df2", "e1399a5b90e058e49aafdec77b812481", "515af54589942304faefd75eff997011", "cd809b793936ae8499d0debb74984907", "197f8d97651700a4bbbc5272273ea869", "fbe963d8db3fd5d4e9a56c6765768f9d", "1fcb07f2b4f1e9343a353830e3c59f63", "087afa50afe59ff46a1322394338345b", "bd49796c29f3f9c4ab69161a3e91d887", "e005eb0a35cc2e34dbb48f36d885ac50", "8f8825a46f7297e4aa14c38f9ed12ea9", "1870c23baf455e3478b2a562d8b5af6b", "3e9c7d6771c4a5f41b7dbe886314c2a9", "c574bccf838b17843b89815af5ea1043", "80efa04feeefbb341850365eef5ed036", "676d03c6e3304734eaa06a7c47e0468b", "2d010e812f81e4140a6f1dd613e644b9", "97525acd000712a4ea124e172e37e08e", "fb8c27a2429b37e4aaf1859daf66e180", "b1d35a4f44dc68e45a8b539bd948a271", "02c4f6284ba196743aca6b98e6c28760", "7998c312d87859c44927e071f47fee61", "fd8f9312b7bac6142a7a5fa1a17880cd", "c2fde82473c69e242a296f5c89171098", "3c1c8e8abcc8d764f8d7115817deb6f0", "7478384f333266d46a2152077852d0f6", "213082abbb7fa2d4d867292af94badc9", "f3a84b4114f5ddb42b49effaf8607385", "7c7c4c10b9f8a384495eb2354cbbdc2e", "9a6614d952302c8498f073cb6ec94992", "baf6898f3a2c9ca44b4ad82e72229537", "08b2e79a6b4c3504395a858adef967e2", "5603b1dcbf0b8d5448cfffc2f74ff9be", "1a8366868515b1e47bdf60322db601e4", "2ce25c39c8edbcc42a08a0557777b9c0", "7152d63fe26a6e2408239c32ec1c1f2f", "061c8ee472e6b6043b137344ef3aa85b", "eba60d95e0c149c4d987031244b3c835", "c136886b54d407644811e1bd09660a37", "c91e5abbfe70b35468835629c75cc607", "fcc623372aca0c2409f742ba8c28e65c", "b0c88513ec6a26a46983f47625bf218c", "2858e0d8f135023488ba44034e8d9729", "60ab1c0de90556348993b6b9a6482273", "a30da4fcfe81fb140a7bff34b55e064b", "8a1ce4bcd6246cb47972451c6a2ec097", "c8b604e76d2c7e44988451238212c3d8", "2077bd1843be9b84a86d3ada0b7699de", "5a653103a44c6cb418d35b9a45933313", "aca636c19f24cf543a19ad82eaa6e8dd", "be7dddd7bef25144aa72bc695baf7463", "419134f3ee9bac541a3938ed7bff82f9", "853bd82f2c582e140b07124620cc9753", "0595f9548f62bb54a8afbb1c32e0c96d", "44ae99eaee0270c48a5bbd26cba3f566", "bf7cee8c41703c54e92498d721320f7d", "d3e99068603250c40b9dfbbc2d4dd678", "fa7b09446c810a549a10072387b14587", "bd10d62d538ec1e46b9a9b7c95ea60da", "7ac85ccdd1027614285b58eb9b08158a", "666ad4c3ea1022d4eb5248d10ce62782", "4f4c84a63a50edf4cbedcebc61dc26f2", "c332b3c61cd31df4c8480212c671eff5", "4cdaa49926448394aa84cd33bf966167", "3f1dbd2a728227648b6b3d1653999b6b", "dcffc99d305bbf6418029fcfa83de6b5", "1203ee578a6cbb04cb70866aa90109e0", "7eada4c51e95e1745a061708ecbbf9a2", "18db48d5d2f7c27469d2753798c6e6e1", "ba510465c1672d84f9ea39f4dda6ce3b", "f9c89c7b883287143acef1de7f0849fb", "b0a3c17157b7915469d3f98ed327bb3d", "38f566ee6ac5c734581f16db572ff29a", "b0feab4294ef54c4f90b5c0a1c513d14", "106cb8f68d8448a44bbb70c4dba3bc7c", "21772d131ef2aae4f93648eaee8d7c49", "63f765abb4672054db38fed1cb5b1008", "adb8e1869913bf846be49cc8f78c9a4c", "7d39c9e799f2644488e002febb7cbd1b", "b611f27085f773140938700c7bc662d2", "aa73dbef15e517b4dbd2cbab9763cf77", "3e522f9f6e6d1354f88b660a86b51f83", "f68e1460c7661844bab24a40df55ae3d", "e0d5fc151a86013469df029edb53ce74", "632da490e62c827489a14b8ab69b4cd5", "6e82a1fe791c21f44985c38d7543bf73", "14ff3cd5b5ab0424e81d796f4403bac0", "672b7de97fdd88c4fa5895838c518873", "1e0b78910d680e24daaf3a16bc4c22f1", "551aeeef6e66a4048a3830d7af52d66c", "0d76ffbac2e7a914eb41bc4c9005e06b", "e756b08e0b2b9804d8556f64315c73e3", "e4859846fc90dd447879b9ad6b5b9c47", "9a7e9c62c608ca84abdbdd63306322f7", "65994b4450a389a439a49b3ad1612459", "746c37219a1dad746b7cd6ea56f65cd8", "01da25d9d27084347b406471b3a3a361", "3ff71dec9805f054eac041fb76d0da87", "57fe8e6e55c498f4f8bf99f64aef0e7b", "55fafaa3f082a204d94e5e03847cab23", "79975a8c95c4d194fa3f15fb4dc231b3", "7c2ed8abd72251b48ac29dee53e59829", "95d7f9c3a1d2261478e295ba17539a30", "baaa9aa09619f9644bc70f6ff70b9b0b", "d0805db2069cce740b32a30663453526", "03e2d8ea358ea844383e9cee23fa2a87", "b8c03d14e20e9cf4dba4cc6931b6eeeb", "70101d970bf2a1444b66ddd2b7656c84", "4b87689769f15b148b144e76214ba0ce", "86c822fd90765b24f96c7e44bf40f3e8", "8477b8e7afcb8894ca9bf271b7ee4a8d", "cdccc6d82a9b10e4bad054944221f810", "338c65ac4e0c69d41b577a1ec37f359a", "e697310914913c54abd9ada4c591a8a3", "4551482d534979348adc279fee564757", "e45289886c5c8dc4a85290f258750ef3", "e3c74d4aac2e2f14b8d9a63f3e52e25c", "08f7fa7607508414aa864a026a021969", "13d16e34e483b1442b1c7137ff7b5160", "af306ee33fa2d964b81f5299443f63f6", "98fc7203a97c9e346946132bcc5a7cce", "5d31d1e1cb266b140bdf04057d632c5d", "f2a60847204053547a7223c4e0951dfc", "8bc9ff1e37c0aac4788155005f15b056", "81ccab52cb7d0ec4b900a7cd5aa922aa", "1a09c6513dbb8d248a876f92767a2dae", "ae7d72211b9670b4eadf489fcbfef9c0", "7fca0534bcf373a4184fd6fcc871ceef", "8d58dd0c7ff07ed459b8e2a65f4b4bd6", "1eadd6e98629e5b42a3cc3356fe6deaa", "be18e697199557b4fb19c620a722eeba", "e761d1b5f99e4c840b58081a41de20d6", "de5173ea15284c24ca5ad4542315e784", "dc4b2254ee625c346966f070e258f451", "f99e1f537718c6542a5603133a1b4876", "bf12a444e80a4e4459d2e3675aeebcc2", "78ff061c2a9ada9469686ba2ba4cc66b", "3ef4b2857a094e449a433cad8e9cf36b", "427376e5dd3838144bd8f86e216107f5", "bb0f2cd4639c6604eb6abbd367f8940e", "2cf97a72f1c1cdc4c94ddd1ebe9e7390", "51afcd4ed8dc2824cb75dcd45eff7349", "469680a2ac6bd4d43a4fa3d60ad65b39", "755513c85e9422b4190173be5be57cc1", "eaf27b41ea5a4f64abf7d63238c35aef", "d8253579b5e713748ae73d1337cf3464", "4d2a054227767194eace536656a2586f", "43dc439bc2165864a95bd4e41310e4cf", "cfa3705a69339844597b2c044cc4ef0e", "810c0752dd5878e4a856add872ff6e14", "ad119aff9141f9b42bf40bd882136155", "98d01613e67618141802b565f9edb564", "db14012171fe0d84db417d3aa9463beb", "21fabdb06cc10f54ab8e93937004e046", "e1b617fdf5e3da14fba34b2e83904bf1", "fa0be82a79a9e01479aca8e7d72e7b6d", "ddc89aa69dbec7244be982ad63f23498", "59f51f735167363409056d3dadf1d738", "3d69b9b2287289e4e8fdc705a9a18f93", "0ccc833e8ad76cb42823affcdf26ac4d", "1d034999e30af1a4dac0f11edece2945", "aa70e09c191a42648ae8cb0e645c661e", "1fca29fea17ab374ba004d0d720b7ca4", "79222b3b3811b9545bdb566a252614a5", "5d6a1c2e2d5844f479ca3a6641a4bf7b", "e80527b7a9db43b4a8315d3893288b9e", "a6cd65ffb9bc5394ab7ee0148e0faa2e", "a31d65f7d6c93e14fa598e28c62e1387", "23217348b541a3b4baa51827e974704c", "bc0d947be21bdce4fb66f000b7166dd3", "aea68a456c1abb243a9ada7f52c079d4", "e75b8a6f4dc24b049967e0ed472d024d", "763a478ff13070b4abae1790b80f4f93", "489b036cb36243344aa02759042e5584", "e25756a49c6663d4cbb762f32356b54a", "b84d90f379035fc479667cf392ab0465", "cd99126f396c25742b76888d5b800b74", "48d35d8841583e848bb65453593b861d", "707ba244fceaf4141adc87e82eab7cc8", "f15a2cce635960e4a814b1a0a052db74", "a8d06a6ec85e351418cfd43c4685165b", "25e9f7686bdc72d4f82a1a9fd5c2c6d4", "e7f768eb292920e4b988ad29165974db", "1924b438066f2264a91ff00c749f5b53", "0cfa641f1e6bbf546a427e9f15047470", "48afee34c424e874583a0b7ed9e6bc02", "c3679d4efaf216d4989048aacb1c40aa", "119c1a542b1b4424798ac8aad55ae2db", "b539b40b6cd43e146bf3d8704fe59622", "7d424098e541da44da0d1d49fdc34b2c", "1ae20dfc2d4d24644a54cea4a3cd6694", "3d9ff6c159fc6104b8bb18b5d1b7ac34", "aac6e5aeedb5bed4aa1e82f75779f692", "39642ea1bb4fe0b468e38ce10385f6a6", "8559157d4e9e7544ba80c4d6296b8350", "54344e1f8a7153042bd779c41d6e67c8", "0b3f15e70c5577845a07bb6fee9884a5", "91e67aca0c9a45d44887d57059ac42cd", "e1379d0d62cc48f4cbb9385187cdfbdb", "9a3c1ef60753df940ad18f40f6752005", "cc67d2d0fc6c46d4da0fffb5f677173a", "795e589fe4efcf1479939ea4f9fbb79a", "016a6ecfb4435534d8e879f9f8d56b85", "7c0c8162c7288df429f076bb36be2aaa", "fa920970a43782e46992e231a2f1ed54", "a591aa448414c3d499f69f9c93d7c07a", "384dd00b81df8864daeb9e0d2a5989e5", "4a3fbaeafad8cf342a176beb1b52a24d", "77d7e4265e64b7d469b2619302a4f5d8", "af4a7e73baa43ca42aaf7e364919f8b5", "75fee62af3c73d54b9c6ccc0d76d9cb7", "3fc1fd890a6c3c74a8b9bc274f82ed12", "cbed33dffb773cb46baf514f98885775", "74038cba3d628d442a5cf4bdb7621a55", "c427a0868cecf5b479a2c5e265c866ae", "520b8caf5d3a358489f9f2e9b76f2d22", "edca7c021b172fd45800718471dad49d", "9b0769268e71c7e418a91a3ec29b66b6", "53ccf5f457381594e9339e2b28f2afa3", "de0d4394ac8c02f46a2865912f25542b", "77e00e89926306d44b450e69d58ad9aa", "20dc6f39c8e69db4590cc202aada9aac", "97323a956bd7bdf44af88a066eebbd0a", "bde97e266c852b44eaa2a91c43890b6d", "98bdb555da516104194becbf295bcced", "cce5229ee4dafd84293ba9e296d37c54", "f139e98be88de3b49af8a322add8e7db", "13c5e1add1c25da49a4be2485b91eabb", "16df03a099ebb1e47afe4f95f7779db5", "098ebddadfc612c43a6b952b54096d9c", "c90c4926424b20446aca6326107c195f", "c96aa96686fd7464d9b9a60975c5b38e", "49daaf8ee1b811542891d56329761a3b", "1d24fcdacc1f2e24384c791a047aa9f2", "8604f5fc6175a40419337a0684f3052c", "255dfbe9caa3d8142a59a1be8b9328d3", "554d5d4f261ba9742a874b76620d3bd8", "df7f15fc66ce30b468e521e10cd79feb", "c1f1b4d2615a5844ba6863d3f29d904d", "3e04a6da854fcf643afb15b9f7cb89a1", "fa071acf1968a0647a54cfc80684290d", "833648af00ba8de4bb21c48e84433c55", "1c7a39b161f5a2248aa0e31068f6bc66", "d50df8605b484ae418bd312fe0e29f5e", "6ff4a7c2ab5fd2143ae36ed6f8c4486a", "dea9a76fc1c95f64d98353ec6d7a292d", "fdbac8830e55be44b8b1b4820e5f2e46", "5b20f007aa8381d4cb4deaa43fc59abb", "3a531bfea4a07ab44bec2d6cddb0bf11"]}, {"ScannerGUID": "19899cd4-d9df-4e12-a068-010ba63a6a7a", "ScannerName": "Texture Scanner", "ScannerDesc": "2D炮台", "ScannerSchema": "4ade26b2ab4ae7c44889293a83d6353d", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon/PrefabArt"}, {"CollectPath": "Assets/Fishes/Cannon/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "d5f773a7-bdd2-46e4-9263-a51db09d1e8a", "ScannerName": "Texture Scanner", "ScannerDesc": "3D炮台", "ScannerSchema": "32b6851b215923948ba13391649bae62", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon/PrefabArt3D"}], "WhiteList": ["1e361df26fb5d6445b7ded89d9f3bf8b", "36a7be4321297194e90e20746a19ecc9", "813445fbd17452543a53b099660c1560", "7e64b39e695d10041a21dbbe323f0519", "aae4e69ec620a8341bd7d118bcb41eaa", "a012322c7f24ea94086094e28c7300d0", "4c9c94813039a534a8653b4ac63e754c", "7bfe6765f14a43243887deef7e30663c", "54d84b1119c4cff40a34b1e38c7ccb70", "21832c93123617b439f2adab0bc6184c", "982ce24c746b12b4499facf603a4ffbc", "245fe3211b5e2bd45844808dfef62f7b", "4b78b93f21973254285641078d318704", "9a79eb8768b074f4d955f43ed319b396", "572a48e698faa194bb6f815d95a8cc65", "19abe3e367bc56a4d839a3dc95124af5", "95b94544aaae4794da1eb71c9507e07c", "afccfaf1e140cfe488233283bd49c281", "7f0519eb06d3a464c85eed50909037ff", "97a8232e699c66445a134a681039fb7a", "b16dcba3100b2d34faedb64527739221", "ea51d953fdc63f5449b5914f11c5a3ed", "e49d4ab8c6b5f9743b53c7ce968e9b17", "84296e1600924564eabdef3f3d70d2be", "a90203e278b919f43b6bbf31ee32ca79", "1067f1c2c6445804eb92b34908acbf87", "73e835353d92d4e49a73987d1b9d040b", "acd56de8891c89a4499815c13c5e08af", "d0565246d3de6de4aa7a83b73914b190", "f127ace00aebc7344a619c9811f4886d", "815d819ced4acba4da2a1dc31d17e1bf", "a61522e81529c7e4aa4b278b43f2c1ec"]}, {"ScannerGUID": "26eedaf7-f0a5-4554-a98e-2b15ed9b8954", "ScannerName": "Texture Scanner", "ScannerDesc": "翅膀", "ScannerSchema": "60af06d3a2593bb41b864b7f9b99ae26", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/Wings/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "c42b072c-89ef-4769-aec0-4d502c2d1d13", "ScannerName": "Texture Scanner", "ScannerDesc": "场景", "ScannerSchema": "f6cbbf146aa7ca4458e85ff58eecdf6f", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/RoomScene/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "151d6f80-c7ba-4ad4-9c9d-19eb92e829ff", "ScannerName": "Texture Scanner", "ScannerDesc": "战神", "ScannerSchema": "2fd08c93a32fbeb43abde62d33c24100", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/CannonSkin/PrefabArt"}], "WhiteList": ["155a64554c18c104588d3ef5a52ddd3d", "6cfe92dcbb91dc94a82773618360373c", "3da6d46743452e54daa4725f2de9f110", "a1b87707b4ce0b54d9ee0bd99ccb2b25", "b12daa1777919f048936d945cb076f3b", "abc00000000014645181316691743357", "73f6d676974da1248a9581826493fe97"]}, {"ScannerGUID": "9c24cef1-df81-43fd-885d-d639429532e1", "ScannerName": "Texture Scanner", "ScannerDesc": "神卫", "ScannerSchema": "bcc7265b8b0f2664886b13ecc97c409f", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/Turret/PrefabArt"}, {"CollectPath": "Assets/Fishes/TurretSystem/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "9c59d5aa-ca62-49b6-8b16-3b098cf936b7", "ScannerName": "Texture Scanner", "ScannerDesc": "展示", "ScannerSchema": "4e23f6264fa412241a54baeade4e8ddd", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetTexture", "Collectors": [{"CollectPath": "Assets/Fishes/BossShow/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "f0ee3c2f-9ccc-4b40-9dc0-4554532880e0", "ScannerName": "FBX Scanner", "ScannerDesc": "UI面板", "ScannerSchema": "c7468e954248a134dbeb459336207a05", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/PrefabArt"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "0639583b-7b86-4047-b1cf-877896e4dfef", "ScannerName": "FBX Scanner", "ScannerDesc": "BOSS", "ScannerSchema": "27239bb531dd91b479f5ef3600f74f1f", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/FishBoss/PrefabArt"}, {"CollectPath": "Assets/Fishes/GameBase/PrefabArt"}], "WhiteList": ["abf80fade1ddb99468fbbb6724fa45fe", "ad535dd99e7869b4aa8bae2a84eeed65"]}, {"ScannerGUID": "6dc65098-0017-447a-b6bd-03791ec28d12", "ScannerName": "FBX Scanner", "ScannerDesc": "特效", "ScannerSchema": "905ea46903013f84ba00b7afb0371353", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Effect/model"}], "WhiteList": ["3d260a18681186f4faa1113c32bb8d28", "ab977bf137d2af841b23fcb4313de0fb"]}, {"ScannerGUID": "0861e94a-0c0d-426e-adc0-90a27ccdcd4b", "ScannerName": "FBX Scanner", "ScannerDesc": "神卫", "ScannerSchema": "e5dfa0c628b13de49bce6825e5c54606", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/Turret/PrefabArt"}, {"CollectPath": "Assets/Fishes/TurretSystem/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "18b4bfa1-5e06-4db2-87d5-e42bc7c19909", "ScannerName": "FBX Scanner", "ScannerDesc": "战神", "ScannerSchema": "2b54a6ea2aee043408cd15c0fa11ea04", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/CannonSkin/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "c2678fe7-7c9c-4f56-9e3d-b51dbfe63d7c", "ScannerName": "FBX Scanner", "ScannerDesc": "展示", "ScannerSchema": "27239bb531dd91b479f5ef3600f74f1f", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/BossShow/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "1717efb6-6fb8-44aa-9c46-e222ecafab4d", "ScannerName": "FBX Scanner", "ScannerDesc": "2D炮台", "ScannerSchema": "f1b29c28864f05740ac075341a21fa97", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon/PrefabArt"}, {"CollectPath": "Assets/Fishes/Cannon/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "95210723-2e68-4943-8a44-5c7cfa09846a", "ScannerName": "FBX Scanner", "ScannerDesc": "3D炮台", "ScannerSchema": "74feedf1424bd344c9e8426bfdc2e23d", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetFBX", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon/PrefabArt3D"}], "WhiteList": []}, {"ScannerGUID": "30d336ce-b3f5-4112-8a5e-4d234cccc6c7", "ScannerName": "Material Scanner", "ScannerDesc": "Boss", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/FishBoss/PrefabArt"}, {"CollectPath": "Assets/Fishes/GameBase/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "71259a80-0b80-4c85-81bc-5226e784bfe9", "ScannerName": "Material Scanner", "ScannerDesc": "无引用", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Effect/material/Material_NoRef"}], "WhiteList": []}, {"ScannerGUID": "02dd222d-2225-495e-9a93-9eb7869288d0", "ScannerName": "Material Scanner", "ScannerDesc": "炮台", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon/PrefabArt"}, {"CollectPath": "Assets/Fishes/Cannon/PrefabArt3D"}, {"CollectPath": "Assets/Fishes/Cannon/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "544d1c56-4073-49bc-848f-ec3ae3810399", "ScannerName": "Material Scanner", "ScannerDesc": "翅膀", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/Wings/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "17eaa390-ca7c-494e-8092-7e3c93267d8b", "ScannerName": "Material Scanner", "ScannerDesc": "场景", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/RoomScene/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "a46ab668-3ade-4f25-bfaf-8fe7081622d1", "ScannerName": "Material Scanner", "ScannerDesc": "战神", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/CannonSkin/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "1d4dced6-1307-465c-a5d1-88380405203a", "ScannerName": "Material Scanner", "ScannerDesc": "神卫", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/Turret/PrefabArt"}, {"CollectPath": "Assets/Fishes/TurretSystem/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "0f280514-b698-4743-b098-78807368bd83", "ScannerName": "Material Scanner", "ScannerDesc": "UI面板", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/PrefabArt"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "88fe3847-2bee-48a4-acd0-986b7b7c4756", "ScannerName": "Material Scanner", "ScannerDesc": "展示", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Fishes/BossShow/PrefabArt"}], "WhiteList": []}, {"ScannerGUID": "fdaee6ca-a9fb-41b1-877d-dc425b84ab36", "ScannerName": "Material Scanner", "ScannerDesc": "特效", "ScannerSchema": "b1214d3a4c00fc94bbc58a3f12b84fc5", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetMaterial", "Collectors": [{"CollectPath": "Assets/Effect/material/Material_Fish"}, {"CollectPath": "Assets/Effect/material/Material_Fish_UIPanel"}, {"CollectPath": "Assets/Effect/material/Material_UIPanel"}], "WhiteList": []}, {"ScannerGUID": "8a9869c7-9391-4bbb-bc6f-8516cd0a994c", "ScannerName": "Prefab Scanner", "ScannerDesc": "UI面板", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel/Prefab"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArt"}, {"CollectPath": "Assets/GameRes/UIPanel/PrefabArtCommon"}], "WhiteList": []}, {"ScannerGUID": "3fd14acf-621b-42f5-be11-75299cabdee7", "ScannerName": "Prefab Scanner", "ScannerDesc": "BOSS", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/FishBoss/Prefab"}, {"CollectPath": "Assets/Fishes/GameBase/Prefab"}], "WhiteList": []}, {"ScannerGUID": "e3758a21-0797-44d7-8723-f9fbca6f6734", "ScannerName": "Prefab Scanner", "ScannerDesc": "炮台", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/Cannon"}], "WhiteList": []}, {"ScannerGUID": "ae6df3a5-b19e-4c4c-b39c-b2a8c3f8ee09", "ScannerName": "Prefab Scanner", "ScannerDesc": "翅膀", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/Wings/Prefab"}], "WhiteList": []}, {"ScannerGUID": "26aa039a-1bcc-4db4-bdf8-fb462821c2a9", "ScannerName": "Prefab Scanner", "ScannerDesc": "场景", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/RoomScene/Prefab"}], "WhiteList": []}, {"ScannerGUID": "b84d8442-0fff-4311-bbc8-f147b9797da7", "ScannerName": "Prefab Scanner", "ScannerDesc": "神卫", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/Turret/Prefab"}, {"CollectPath": "Assets/Fishes/TurretSystem/Prefab"}], "WhiteList": []}, {"ScannerGUID": "46b48195-87b1-44cb-a245-049c24d056df", "ScannerName": "Prefab Scanner", "ScannerDesc": "战神", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/CannonSkin/Prefab"}], "WhiteList": []}, {"ScannerGUID": "dd565bba-d4db-4615-9ba8-0e43395814e0", "ScannerName": "Prefab Scanner", "ScannerDesc": "展示", "ScannerSchema": "dd4231fb843583b499cb906b1ab0526e", "SaveDirectory": "Assets/GameSetting/ScanReport/AssetPrefab", "Collectors": [{"CollectPath": "Assets/Fishes/BossShow/Prefab"}], "WhiteList": []}, {"ScannerGUID": "814080fc-bbb7-4ddd-b0f4-1e85d6072b70", "ScannerName": "Reference Scanner", "ScannerDesc": "Fish", "ScannerSchema": "36866235e2aad1547a939dec160aab90", "SaveDirectory": "Assets/GameSetting/ScanReport/Reference", "Collectors": [{"CollectPath": "Assets/GameRes/UIPanel"}], "WhiteList": []}, {"ScannerGUID": "caed68ad-7dc3-4f7d-ac15-7bc1a58393b9", "ScannerName": "Reference Scanner", "ScannerDesc": "UIPanel", "ScannerSchema": "21add3d17e927de459850a431486df1d", "SaveDirectory": "Assets/GameSetting/ScanReport/Reference", "Collectors": [{"CollectPath": "Assets/Fishes"}], "WhiteList": []}, {"ScannerGUID": "f69d3b40-ac4e-43b8-954b-5acf32b729a6", "ScannerName": "Dependency Effect", "ScannerDesc": "Effect", "ScannerSchema": "f0eeafcab071e1c448f198dc84475ef7", "SaveDirectory": "Assets/GameSetting/ScanReport/Dependency", "Collectors": [{"CollectPath": "Assets/Effect"}], "WhiteList": []}]}