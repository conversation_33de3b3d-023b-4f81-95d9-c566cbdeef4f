{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetFBXSchema", "ScannerGUID": "18b4bfa1-5e06-4db2-87d5-e42bc7c19909", "ReportTitle": "扫描所有模型", "ReportDesc": "规则介绍：检测模型的面数，骨骼数，可读写开关，优化配置等", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "顶点数", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "三角面", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "骨骼数", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "19b538d2bddc9ca4a8d015d48fffa719", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Model/fish_1714_aiyagesi@show_enter.FBX"}, {"Title": "顶点数", "Info": "31032"}, {"Title": "三角面", "Info": "38109"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "757ae91b61cf2db44b9d9854a363e057", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Model/fish_1714_aiyagesi@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6fae616aaabe14745b1dd9fbeb6baa51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Model/fish_1714_aiyagesi@show_enter_yanqiu.FBX"}, {"Title": "顶点数", "Info": "16101"}, {"Title": "三角面", "Info": "28160"}, {"Title": "骨骼数", "Info": "120"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1e6039781df1de4abdce563f5d3e20f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Model/fish_1714_aiyagesi@show_idle.FBX"}, {"Title": "顶点数", "Info": "30994"}, {"Title": "三角面", "Info": "38109"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ec6e3c40b0cc7247a199a3ff62f9aa2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Model/fish_1714_aiyagesi@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79c530ac03010a6419596cb0063366b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Model/aiyagesi_beijing_bantou.fbx"}, {"Title": "顶点数", "Info": "66"}, {"Title": "三角面", "Info": "64"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6dd7921fbfe496b4e9e5e47baa2de2a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Model/aiyagesi_touying_jianbian.fbx"}, {"Title": "顶点数", "Info": "4"}, {"Title": "三角面", "Info": "2"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a18753331dedf344a263ff23a760631", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Model/aiyagesi_zhanshen_beijingpian.fbx"}, {"Title": "顶点数", "Info": "330"}, {"Title": "三角面", "Info": "384"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eccca6c4d5c58974e9e990dc0ca1455b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Model/aiyagesi_zhanshen_diban01.fbx"}, {"Title": "顶点数", "Info": "88"}, {"Title": "三角面", "Info": "116"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a87a31fd5e7472846a94b17db5c722ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Model/aiyagesi_zhanshen_shizhu01.fbx"}, {"Title": "顶点数", "Info": "531"}, {"Title": "三角面", "Info": "692"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "672e10bc8fc043549bf10a00c08fda2d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYang<PERSON>uo/Model/zhan<PERSON><PERSON>_bai<PERSON><PERSON><PERSON>@show_enter_qiu.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "349671853d71afd468acc33380204760", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYang<PERSON>uo/Model/zhan<PERSON><PERSON>_bai<PERSON><PERSON><PERSON>@show_shensheng_to_idle_qiu.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5f57230e1c5b5e548a44391fd6570430", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYang<PERSON>uo/Model/zhanshen_baiyangzuo_skin.FBX"}, {"Title": "顶点数", "Info": "15596"}, {"Title": "三角面", "Info": "22443"}, {"Title": "骨骼数", "Info": "121"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "16d42a232217bca4d90b938f79ab088c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/CaiShen/model/<PERSON><EMAIL>"}, {"Title": "顶点数", "Info": "6766"}, {"Title": "三角面", "Info": "8692"}, {"Title": "骨骼数", "Info": "55"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f36761a863e86a942b11ccfd1bca2d66", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/caocaozhanshen_dizuo.FBX"}, {"Title": "顶点数", "Info": "4250"}, {"Title": "三角面", "Info": "3710"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f525983e8dcb3304ba40280fb477ba03", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/caocao_jianzhu_zifaguang.fbx"}, {"Title": "顶点数", "Info": "24"}, {"Title": "三角面", "Info": "12"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "86d79c0c69b73f247a2fadc0d28645c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h6.FBX"}, {"Title": "顶点数", "Info": "1654"}, {"Title": "三角面", "Info": "920"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a720dc4d091b73845b90025d85cbecd5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h7.FBX"}, {"Title": "顶点数", "Info": "3262"}, {"Title": "三角面", "Info": "1814"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43ac93f6ee08e89499858f02d3d190fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h9.FBX"}, {"Title": "顶点数", "Info": "1590"}, {"Title": "三角面", "Info": "830"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7c87af48c9c7e0346b9ff440223aea4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h10.FBX"}, {"Title": "顶点数", "Info": "3760"}, {"Title": "三角面", "Info": "1970"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8010371bd82701a42a7ffa6dbc893839", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h11.FBX"}, {"Title": "顶点数", "Info": "5054"}, {"Title": "三角面", "Info": "2718"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8949adef5b27ca745a1b6cf64c73e22d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h13.FBX"}, {"Title": "顶点数", "Info": "1678"}, {"Title": "三角面", "Info": "858"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92eed24a1cc6fb04caadddb886b1bfec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h15.FBX"}, {"Title": "顶点数", "Info": "1346"}, {"Title": "三角面", "Info": "806"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b590f37ca0a2854a949279e45c8bb45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h16.FBX"}, {"Title": "顶点数", "Info": "2057"}, {"Title": "三角面", "Info": "1172"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17f777d8484514b41af1bb4b29618c3b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h18.FBX"}, {"Title": "顶点数", "Info": "2550"}, {"Title": "三角面", "Info": "1406"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b66aa3b141aaf6438a78614a8f26645", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Model/changjing/SM_city_h19.FBX"}, {"Title": "顶点数", "Info": "664"}, {"Title": "三角面", "Info": "316"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a815f2d5b8441cc4591a2d5101b24c5f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChiTianShi/Model/1660_zhanshen_chitianshi@show_idle.fbx"}, {"Title": "顶点数", "Info": "32678"}, {"Title": "三角面", "Info": "40015"}, {"Title": "骨骼数", "Info": "183"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "317bb59e9719a0e4fac4fd3836288ed2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChiTianShi/Model/1660_zhanshen_chitianshi@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22c939a52699780448f913dcdf2431f0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChiTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "32690"}, {"Title": "三角面", "Info": "40099"}, {"Title": "骨骼数", "Info": "183"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "4bd6b58e0b672c042bd5ac28f288d4ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChiTianShi/Model/1660_zhanshen_chitianshi@skill1_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c47c6d03a2d7e1749b2d70635aa5b708", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChiTianShi/Model/1660_zhanshen_chitianshi_skin.fbx"}, {"Title": "顶点数", "Info": "32913"}, {"Title": "三角面", "Info": "40272"}, {"Title": "骨骼数", "Info": "183"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "4a7f56ba387a4424094be469ab6d1b22", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Model/zhanshen_chunvzuo_1703_skin.FBX"}, {"Title": "顶点数", "Info": "48524"}, {"Title": "三角面", "Info": "64424"}, {"Title": "骨骼数", "Info": "235"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "f39596cb37428bb40a047bec9cebcf70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Model/fish_1548_daoshou@show_idle.FBX"}, {"Title": "顶点数", "Info": "23238"}, {"Title": "三角面", "Info": "29014"}, {"Title": "骨骼数", "Info": "114"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7e8424f7d989354894e3d568d85c9b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Model/fish_1548_daoshou@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d4bda05526856134bb60333a7e5be82c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Model/mx_shandian_003.FBX"}, {"Title": "顶点数", "Info": "96"}, {"Title": "三角面", "Info": "90"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "29f3b66e853af4643bf785fdfc02d892", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Model/mx_sphere01.FBX"}, {"Title": "顶点数", "Info": "296"}, {"Title": "三角面", "Info": "480"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "d4f58efd072e8b04aac622d822e6e286", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaTianShi/Model/zhanshen_1658_da<PERSON>shi@show_idle.fbx"}, {"Title": "顶点数", "Info": "30032"}, {"Title": "三角面", "Info": "41757"}, {"Title": "骨骼数", "Info": "155"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "bfa4345a5c6819c4198cb6693b7c0f2e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaTianShi/Model/zhanshen_1658_da<PERSON><PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37095dada08519d4780c24de1e299cd7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaTianShi/Model/zhanshen_1658_datianshi_skin.fbx"}, {"Title": "顶点数", "Info": "30039"}, {"Title": "三角面", "Info": "41761"}, {"Title": "骨骼数", "Info": "153"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "e79b92513f7381b4e82c8dc041799aea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DuoTianShi/model/zhanshen_duotianshi_2.0@show_idle.fbx"}, {"Title": "顶点数", "Info": "33925"}, {"Title": "三角面", "Info": "45066"}, {"Title": "骨骼数", "Info": "263"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "b0b0e48d59792de419737b1fb5e40e0d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DuoTianShi/model/zhanshen_duotianshi_2.0@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ce1b6c8b1061f34882c2ce3e4cd5dee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DuoTianShi/model/zhanshen_duotianshi_2.0_skin.fbx"}, {"Title": "顶点数", "Info": "33915"}, {"Title": "三角面", "Info": "45047"}, {"Title": "骨骼数", "Info": "258"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "24646013cf07ad34db171cbe55684a71", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Model/eye_guanyu_001.FBX"}, {"Title": "顶点数", "Info": "16"}, {"Title": "三角面", "Info": "12"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "aadc11c798726884397541ff63860ecb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Model/sanguo_shenqi_guanyu_skin.FBX"}, {"Title": "顶点数", "Info": "39448"}, {"Title": "三角面", "Info": "49776"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "5d3be18473eb9984e9e1a491b2f9e93f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/dizuo/S_floor_1_low.FBX"}, {"Title": "顶点数", "Info": "5413"}, {"Title": "三角面", "Info": "3338"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bfec1c03d573f0d48aa35258ee607b4e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shu/RedLeaves_01.FBX"}, {"Title": "顶点数", "Info": "8102"}, {"Title": "三角面", "Info": "8946"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db9fcab5865289b48962d1df7c2eb52c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shui/water_01.FBX"}, {"Title": "顶点数", "Info": "36"}, {"Title": "三角面", "Info": "50"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9e52b29223c2a744a79032340edf88b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/taijie/Ladder_Mesh.FBX"}, {"Title": "顶点数", "Info": "952"}, {"Title": "三角面", "Info": "500"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "55e8cea577dae4343834de3d139fb464", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/Toro_Lantern_Mesh/Toro_Lantern_Mesh.FBX"}, {"Title": "顶点数", "Info": "857"}, {"Title": "三角面", "Info": "986"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "34cfc45ac7596344db4f40471169523c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/chibang_logo.fbx"}, {"Title": "顶点数", "Info": "4056"}, {"Title": "三角面", "Info": "2644"}, {"Title": "骨骼数", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b813d5a924e538744889a1d06d0b7e1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi@show_enter.fbx"}, {"Title": "顶点数", "Info": "31891"}, {"Title": "三角面", "Info": "33334"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08158e6ec95d1fd45b0b59e5419c7096", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi@show_enter_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd8234e70e2d61146aaa44ff66ee50bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi@show_idle.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09175b2cb4f9a274e9b12a695743a8cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b220b4e8e223c84096dc17f23b4924f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi_guancai.FBX"}, {"Title": "顶点数", "Info": "1450"}, {"Title": "三角面", "Info": "2054"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "51759732f72e5084292dfc67f09b5704", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/fish_1713_hadisi_guancai_skin.fbx"}, {"Title": "顶点数", "Info": "1659"}, {"Title": "三角面", "Info": "2081"}, {"Title": "骨骼数", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "254b0f91bdc83724ea21ccaa15c31c47", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/guancai_anim_v002.fbx"}, {"Title": "顶点数", "Info": "4314"}, {"Title": "三角面", "Info": "2785"}, {"Title": "骨骼数", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "57639b31a736f1849ab0ae70976a62d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/guancai_anim_v004.fbx"}, {"Title": "顶点数", "Info": "1754"}, {"Title": "三角面", "Info": "2446"}, {"Title": "骨骼数", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "584bcf24db59f7c449427d7f108e3128", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_diban01.fbx"}, {"Title": "顶点数", "Info": "20"}, {"Title": "三角面", "Info": "10"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "daa08572e84d19a4989d504326433652", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_fangjian01.fbx"}, {"Title": "顶点数", "Info": "236"}, {"Title": "三角面", "Info": "118"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b0bf488a28e96644b39e24ae6829c30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_gaotai.FBX"}, {"Title": "顶点数", "Info": "30819"}, {"Title": "三角面", "Info": "20224"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "139e24d12e2855146a95c96c93b3d363", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_gaotai02.fbx"}, {"Title": "顶点数", "Info": "31279"}, {"Title": "三角面", "Info": "20448"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cdacf38b5d3d4d24da7494585b1fa6c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_gaotai_02.fbx"}, {"Title": "顶点数", "Info": "5930"}, {"Title": "三角面", "Info": "3022"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d141cb9d53886f4080ab6390dedd38f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Model/hadisi_changjing_qiangmian.fbx"}, {"Title": "顶点数", "Info": "4"}, {"Title": "三角面", "Info": "2"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a1ac2c071bc3ef94aa81eebbb70722c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Scene/Model/Cj_zhan<PERSON><PERSON>_hadisi_dimianbaobian.fbx"}, {"Title": "顶点数", "Info": "29"}, {"Title": "三角面", "Info": "36"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6e8f42ad39d53764d8a63b6abaf7578d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Scene/Model/Cj_zhan<PERSON><PERSON>_hadisi_wujian01.fbx"}, {"Title": "顶点数", "Info": "3746"}, {"Title": "三角面", "Info": "1920"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cb647ea4caec358408c6046ac6549634", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Scene/Model/Cj_zhan<PERSON><PERSON>_hadisi_wujian02.fbx"}, {"Title": "顶点数", "Info": "296"}, {"Title": "三角面", "Info": "148"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1a35cf28f1daebe449bbda7d1edefadb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Scene/Model/hadisi_damen01.fbx"}, {"Title": "顶点数", "Info": "1686"}, {"Title": "三角面", "Info": "1392"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e511a8e64e35205419f9a068995465e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HaDiSi/Scene/Model/hadisi_damen02.fbx"}, {"Title": "顶点数", "Info": "1686"}, {"Title": "三角面", "Info": "1392"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43ff1663f3a8235488d241c6b9287b6b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON>/Model/hz_gong_bianyuan02.FBX"}, {"Title": "顶点数", "Info": "168"}, {"Title": "三角面", "Info": "240"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "430fe0915e702dd498ef825c50cfb5b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON>/Model/pifu_huang<PERSON><PERSON>_<PERSON>@show_idle.fbx"}, {"Title": "顶点数", "Info": "22570"}, {"Title": "三角面", "Info": "33868"}, {"Title": "骨骼数", "Info": "92"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef087e0eb641c5d4f8e8f6e72697ed24", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON>/Model/pifu_huang<PERSON><PERSON>_<PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d2d382b8e9176e145bcd819102415316", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON>/Model/yuanhuan_keji_pian_001.FBX"}, {"Title": "顶点数", "Info": "60"}, {"Title": "三角面", "Info": "48"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "e0e48573b65b6a94ab1d420f336c0f29", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Fbx/CJ_huangzhou_dizuo01.FBX"}, {"Title": "顶点数", "Info": "1404"}, {"Title": "三角面", "Info": "1552"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4553f5dd3aaaa2049a4cc734c0437931", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Fbx/CJ_huangzhou_dizuo02.FBX"}, {"Title": "顶点数", "Info": "40"}, {"Title": "三角面", "Info": "40"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "255c74f667f42e645a15b485fba489f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Fbx/CJ_huangzhou_dizuo03.FBX"}, {"Title": "顶点数", "Info": "160"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0c71406ae120a504bac01c78658f6894", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Fbx/gulou_low.fbx"}, {"Title": "顶点数", "Info": "26443"}, {"Title": "三角面", "Info": "27754"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec8c0a2b82c4cdf449d8fe011f305191", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Fbx/huangzhong_dimian.FBX"}, {"Title": "顶点数", "Info": "324"}, {"Title": "三角面", "Info": "162"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "28c3f8993e7e91d4aac024c160d57192", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Model/EF_<PERSON>u.FBX"}, {"Title": "顶点数", "Info": "544"}, {"Title": "三角面", "Info": "512"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "98305858e29d5b44b8c9c6041f2f0fba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Model/pifu_ji<PERSON><PERSON>_<PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b605b1382cd31744b9ad516c679fa3cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Model/sanguo_shenqi_jiang<PERSON>_skin.fbx"}, {"Title": "顶点数", "Info": "27983"}, {"Title": "三角面", "Info": "29438"}, {"Title": "骨骼数", "Info": "85"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd1da7b62189cb34d991160ac5e6c312", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/changjing/huoshanshi_5.fbx"}, {"Title": "顶点数", "Info": "2467"}, {"Title": "三角面", "Info": "3638"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dfe684a263734874ca010700430bfbde", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/changjing/shitou1.fbx"}, {"Title": "顶点数", "Info": "1470"}, {"Title": "三角面", "Info": "2448"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4af46faf24c11f34ba57ea8e4253ea21", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/changjing/shitou7.fbx"}, {"Title": "顶点数", "Info": "65"}, {"Title": "三角面", "Info": "91"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "201d3c57dac891841bfcb2c5483a640c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/changjing/SM_Skybox_Mesh.FBX"}, {"Title": "顶点数", "Info": "2208"}, {"Title": "三角面", "Info": "3968"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6c02f4cee6349f043ab96ea5484b5946", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/changjing/yanjiang1.fbx"}, {"Title": "顶点数", "Info": "55"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "04068c4d7839d624fb6eeb5e35861e45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/pifu_jiaxu_<PERSON>@show_enter.FBX"}, {"Title": "顶点数", "Info": "26553"}, {"Title": "三角面", "Info": "25177"}, {"Title": "骨骼数", "Info": "76"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6f6a469d6d2dc84c863cfc1369ad26b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Model/zhanshen_jiaxu_low_skin.FBX"}, {"Title": "顶点数", "Info": "13292"}, {"Title": "三角面", "Info": "10427"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2e5378310cd8acb48ae7f3c1c3bfb883", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/Changjing_JX_suolian/Changjing_JX_suolian.FBX"}, {"Title": "顶点数", "Info": "1339"}, {"Title": "三角面", "Info": "1196"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bf50e4b04a5a4bb4eb3c83f665731af1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/Changjing_JX_zhuzi/Changjing_JX_zhuzi.FBX"}, {"Title": "顶点数", "Info": "915"}, {"Title": "三角面", "Info": "992"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b9699745a1aa5c469900ec35cd891fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/jiaxuzhanshen_dizuo/jiaxuzhanshen_dizuo.FBX"}, {"Title": "顶点数", "Info": "2021"}, {"Title": "三角面", "Info": "2772"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd7017706d6a1334d95a246a3c245cfa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/model_changjing/huoshanshi_5.fbx"}, {"Title": "顶点数", "Info": "2467"}, {"Title": "三角面", "Info": "3638"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "48acd0004c47a3f44967f310e28bb872", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/model_changjing/shitou1.fbx"}, {"Title": "顶点数", "Info": "1470"}, {"Title": "三角面", "Info": "2448"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2a98fb578c2a3cf4186ff4d414cc7682", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/model_changjing/shitou7.fbx"}, {"Title": "顶点数", "Info": "65"}, {"Title": "三角面", "Info": "91"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e285845b071581c4bae8eafd2c816a2e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/model_changjing/SM_Skybox_Mesh.FBX"}, {"Title": "顶点数", "Info": "2208"}, {"Title": "三角面", "Info": "3968"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c4fd3bbdcc93564587251eea4ce687d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/model_changjing/yanjiang1.fbx"}, {"Title": "顶点数", "Info": "55"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53dc8bf758f02904181cd7b826f44e3e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/jingling_mesh.FBX"}, {"Title": "顶点数", "Info": "26251"}, {"Title": "三角面", "Info": "32242"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "328513b9f04044148a99ca0896bb0634", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/zhanshen_jingling@show_idle.fbx"}, {"Title": "顶点数", "Info": "26272"}, {"Title": "三角面", "Info": "32262"}, {"Title": "骨骼数", "Info": "192"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fab840a84a1984440a300f569ade5791", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/zhanshen_jingling@show_idle2.fbx"}, {"Title": "顶点数", "Info": "26299"}, {"Title": "三角面", "Info": "32356"}, {"Title": "骨骼数", "Info": "191"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a55f56194f2f15048b7e3c394d8cd8a4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/zhanshen_jingling@show_idle2_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38de0a737872bf846bae037eb0e6e09f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/zhanshen_jingling@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "60efad28c51474842a8bd891679672a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jingling/Model/zhanshen_jingling_skin.fbx"}, {"Title": "顶点数", "Info": "33583"}, {"Title": "三角面", "Info": "32356"}, {"Title": "骨骼数", "Info": "191"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "004d155d6988b664bad8eb20f94ecac7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Jin<PERSON><PERSON>/Model/zhanshen_jinniu<PERSON><PERSON>_skin.FBX"}, {"Title": "顶点数", "Info": "14572"}, {"Title": "三角面", "Info": "21076"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e9ab9027bd69124ba3ebd0ce6f9e3ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Model/zhanshen_juxiezuo_skin.FBX"}, {"Title": "顶点数", "Info": "20257"}, {"Title": "三角面", "Info": "29738"}, {"Title": "骨骼数", "Info": "123"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eeafbf4e05993a44a85d892ef4468bef", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Model/fish_1716_ladamandisi@show_enter.fbx"}, {"Title": "顶点数", "Info": "35872"}, {"Title": "三角面", "Info": "42919"}, {"Title": "骨骼数", "Info": "138"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "f2a5554e98f3d1b4b9f6b4428d164802", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Model/fish_1716_ladamandisi@show_enter_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "004f60ae9eb6c2243924c4b21cdcc040", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Model/fish_1716_ladamandisi@show_idle.fbx"}, {"Title": "顶点数", "Info": "38749"}, {"Title": "三角面", "Info": "46175"}, {"Title": "骨骼数", "Info": "160"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "a0b2e783be8d8ea419fdde9b68d671f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Model/fish_1716_ladamandisi@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "34e00c20aad57ff469889e9d6d40a89d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Model/fish_1716_ladamandisi_skin.FBX"}, {"Title": "顶点数", "Info": "34385"}, {"Title": "三角面", "Info": "38621"}, {"Title": "骨骼数", "Info": "158"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9b3ab0a1204ba6409098c5a7b6b36fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Model/fish_1587_lijing_skin.FBX"}, {"Title": "顶点数", "Info": "21550"}, {"Title": "三角面", "Info": "31124"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95a954b2a238c9a4081f96d3f15eec42", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Model/fish_1587_lijing_ta_youdizuo_skin.FBX"}, {"Title": "顶点数", "Info": "9968"}, {"Title": "三角面", "Info": "10805"}, {"Title": "骨骼数", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b28d97d291db6d14d8c25382f849e16c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Model/fish_1587_lijing_yuanshen_skin.FBX"}, {"Title": "顶点数", "Info": "21526"}, {"Title": "三角面", "Info": "31124"}, {"Title": "骨骼数", "Info": "145"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6579aab6885f0cb42992fe09e9e58c1c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Model/pifu_lijing_<PERSON>@show_idle_ta.FBX"}, {"Title": "顶点数", "Info": "9548"}, {"Title": "三角面", "Info": "10697"}, {"Title": "骨骼数", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "befc9c03353ef394bb2c7d825bd197be", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/feiji_skin.fbx"}, {"Title": "顶点数", "Info": "59016"}, {"Title": "三角面", "Info": "40572"}, {"Title": "骨骼数", "Info": "6"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "84e64ccc50c20bd48906f4bd99a1cc90", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/fx_mod_cone_jiguang.FBX"}, {"Title": "顶点数", "Info": "36"}, {"Title": "三角面", "Info": "32"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "44a57dbf9db3a874c835e28cc9d330ea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/fx_mod_huanxing2.FBX"}, {"Title": "顶点数", "Info": "231"}, {"Title": "三角面", "Info": "384"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "2e0116de7d5908a4aba1a8bbddc90bb4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/m_fengpian01.FBX"}, {"Title": "顶点数", "Info": "63"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "c5215482b2521de488d8719c87671c4a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/p_longgui_shine05.FBX"}, {"Title": "顶点数", "Info": "36"}, {"Title": "三角面", "Info": "48"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "8117647199a67a84eaca25cb9ceb22a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Model/sanguo_shenqi_liubei_skin.fbx"}, {"Title": "顶点数", "Info": "22868"}, {"Title": "三角面", "Info": "28264"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000015305752110870806957", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Model/SM_Props1_Starbase.fbx"}, {"Title": "顶点数", "Info": "1517"}, {"Title": "三角面", "Info": "742"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 没有开启Mesh优化"}]}, {"GUID": "7df0ba254c0d7c24bb600db62e6677b1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Model/ef_dfw_zhengfang.FBX"}, {"Title": "顶点数", "Info": "16"}, {"Title": "三角面", "Info": "8"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "7951e23d06221344babf37ea4aa227b3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Model/jiaocha_plane_cy_001.FBX"}, {"Title": "顶点数", "Info": "16"}, {"Title": "三角面", "Info": "8"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "167ff14525c9557449e1167704b2c564", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Model/pifu_lvbu_Cannon@show_idle.fbx"}, {"Title": "顶点数", "Info": "24584"}, {"Title": "三角面", "Info": "25080"}, {"Title": "骨骼数", "Info": "90"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f3ff3082a3f61841b3a1c056e2a013b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Model/pifu_lvbu_<PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d62a69eb1f3b44547b8503c1d28b058f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Ma<PERSON><PERSON>/Model/sanguo_shenqi_machao_skin.FBX"}, {"Title": "顶点数", "Info": "44759"}, {"Title": "三角面", "Info": "48280"}, {"Title": "骨骼数", "Info": "94"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "74a74b68cf80a9747852ae7952ae8d4b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/1.fbx"}, {"Title": "顶点数", "Info": "3490"}, {"Title": "三角面", "Info": "3396"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5af2ce679419a09409b9d03e8910ac69", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/2.fbx"}, {"Title": "顶点数", "Info": "8884"}, {"Title": "三角面", "Info": "4578"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec98a022894a41647b8d2ebebaa353d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/3.fbx"}, {"Title": "顶点数", "Info": "5776"}, {"Title": "三角面", "Info": "5929"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "46e7b3153223a834789357a65c87d97d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/ditai/taizi.fbx"}, {"Title": "顶点数", "Info": "13075"}, {"Title": "三角面", "Info": "16138"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "572bfeeb73e5536428d0b85542d93287", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/shitou/shitou.fbx"}, {"Title": "顶点数", "Info": "487"}, {"Title": "三角面", "Info": "916"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7b6b6e3767095442aa71469796e29e1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/Sky/xingkong_ding.FBX"}, {"Title": "顶点数", "Info": "161"}, {"Title": "三角面", "Info": "288"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "0950dfdb886bf2e4b93824bac175e638", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/Sky/xingkong_huan.FBX"}, {"Title": "顶点数", "Info": "162"}, {"Title": "三角面", "Info": "260"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "c12967315d464a04c8e03a02422878fc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/fish_1715_minuosi@show_enter.FBX"}, {"Title": "顶点数", "Info": "33919"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef69b41fbc5c3ca4b9cd1c4d268dd96b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/fish_1715_minuosi@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b46e0591be999b43bef206662a20723", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/fish_1715_minuosi@show_idle.FBX"}, {"Title": "顶点数", "Info": "33919"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1d11256451b2d9449ad6a2fbfc34fb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/fish_1715_minuosi@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37dbf5c8653aadb48950002169fe8cd7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/fish_1715_minuosi_skin.FBX"}, {"Title": "顶点数", "Info": "33919"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24df3f0169ed3464e864e5b74d398fcc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "45150"}, {"Title": "三角面", "Info": "49978"}, {"Title": "骨骼数", "Info": "1174"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b251cb4b30e462442b65e2b00372392b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Mo<PERSON>ie<PERSON>/Model/zhanshen_mojiezuo_skin.FBX"}, {"Title": "顶点数", "Info": "15712"}, {"Title": "三角面", "Info": "23013"}, {"Title": "骨骼数", "Info": "108"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81dae47eb44afae489b320004387f30b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Model/zhan<PERSON><PERSON>_she<PERSON><PERSON><PERSON><PERSON>_skin.FBX"}, {"Title": "顶点数", "Info": "18799"}, {"Title": "三角面", "Info": "25809"}, {"Title": "骨骼数", "Info": "97"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0022acf848f3024a96fb75671225d7e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/fish_shj_NvWa@show_enter_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "392fc37e6b4caa7429cfff3e5e31602d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/fish_shj_NvWa_skin.FBX"}, {"Title": "顶点数", "Info": "14370"}, {"Title": "三角面", "Info": "20624"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6a49761ea00daf42a2d54c8f1deebf6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_cj_xingpan01.FBX"}, {"Title": "顶点数", "Info": "657"}, {"Title": "三角面", "Info": "1104"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "6d4621042218e1c4da1aca074c813473", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_cj_xingpan02.FBX"}, {"Title": "顶点数", "Info": "40"}, {"Title": "三角面", "Info": "54"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "0748d979dd9a91a46a250d709480503a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_cylinder002.FBX"}, {"Title": "顶点数", "Info": "1573"}, {"Title": "三角面", "Info": "2880"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "613c68968f8a5664ba1e876567d6fd73", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_kuosanhuan01.FBX"}, {"Title": "顶点数", "Info": "363"}, {"Title": "三角面", "Info": "640"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "600eb10a74464d146866df388fad134c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_piaodai01.FBX"}, {"Title": "顶点数", "Info": "510"}, {"Title": "三角面", "Info": "800"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "1ecac2d97820c2a46bdeab127e24a15d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Model/mx_xingkong01.FBX"}, {"Title": "顶点数", "Info": "81"}, {"Title": "三角面", "Info": "128"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "b02d74068e21d2e478959e882ceb65c8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShengTianShi/Model/fish_1666_shengtianshi@show_idle.fbx"}, {"Title": "顶点数", "Info": "32329"}, {"Title": "三角面", "Info": "42837"}, {"Title": "骨骼数", "Info": "182"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "a1fecd08b5709704e8e5724606240f4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShengTianShi/Model/fish_1666_sheng<PERSON><PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0bb6afd7c9a37724ba9356241fac5699", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShengTianShi/Model/fish_1666_shengtianshi_skin.FBX"}, {"Title": "顶点数", "Info": "32329"}, {"Title": "三角面", "Info": "42837"}, {"Title": "骨骼数", "Info": "182"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "51b9955ee57a47a42aa6d1840daf9885", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZ<PERSON>/Model/fish_1706_sds_sheshou<PERSON><PERSON>@sds_Show_enter.FBX"}, {"Title": "顶点数", "Info": "18766"}, {"Title": "三角面", "Info": "25752"}, {"Title": "骨骼数", "Info": "30"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11c5e8478971fa0409ba3fee933ddb13", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Model/tanxizhiqiang@tanxizhiqiang_Show_enter.FBX"}, {"Title": "顶点数", "Info": "4983"}, {"Title": "三角面", "Info": "3641"}, {"Title": "骨骼数", "Info": "51"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2f9ae29bd428f9749aeecf410ac96eef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/She<PERSON>houZ<PERSON>/Model/zhanshen_sheshouzuo2_1706_skin.FBX"}, {"Title": "顶点数", "Info": "21212"}, {"Title": "三角面", "Info": "28996"}, {"Title": "骨骼数", "Info": "97"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b45d39964646b7944805174eded674b9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/She<PERSON>hou<PERSON><PERSON>/Model/zhan<PERSON><PERSON>_shesh<PERSON><PERSON><PERSON>_1706_enter_skin.FBX"}, {"Title": "顶点数", "Info": "31072"}, {"Title": "三角面", "Info": "43525"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "d7b8004fd45059d42a089722eb2116cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/She<PERSON>hou<PERSON><PERSON>/Model/zhan<PERSON><PERSON>_shesh<PERSON><PERSON><PERSON>_1706_skin.FBX"}, {"Title": "顶点数", "Info": "18789"}, {"Title": "三角面", "Info": "25818"}, {"Title": "骨骼数", "Info": "97"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24c8b00e0cb7a974fbd34bce9ddfa967", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON><PERSON><PERSON>/Model/shizizu<PERSON>_shengyi_skin.fbx"}, {"Title": "顶点数", "Info": "14992"}, {"Title": "三角面", "Info": "22948"}, {"Title": "骨骼数", "Info": "120"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9bc67a856602d53429adecd4dbe98f72", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYu<PERSON><PERSON>/Model/zhan<PERSON><PERSON>_shuang<PERSON><PERSON><PERSON>@show_enter_meigui_daoju.FBX"}, {"Title": "顶点数", "Info": "26046"}, {"Title": "三角面", "Info": "37062"}, {"Title": "骨骼数", "Info": "47"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a4e5b1eee49abf46b61b215cd4a1dcc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Model/zhanshen_shuangyuzuo_skin.FBX"}, {"Title": "顶点数", "Info": "27941"}, {"Title": "三角面", "Info": "43071"}, {"Title": "骨骼数", "Info": "180"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "c2e6908aeffe0bc46b6005f843c63160", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>/Model/zhan<PERSON><PERSON>_shuang<PERSON><PERSON><PERSON>@show_enter.FBX"}, {"Title": "顶点数", "Info": "20618"}, {"Title": "三角面", "Info": "28788"}, {"Title": "骨骼数", "Info": "137"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd6489122f1d7984f995e9ad276bbf02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>/Model/zhan<PERSON><PERSON>_shuang<PERSON><PERSON><PERSON>@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74beda54ef4f91c40bb1bce0b149e77d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>/Model/zhanshen_shuangzizuo_skin.FBX"}, {"Title": "顶点数", "Info": "20618"}, {"Title": "三角面", "Info": "28788"}, {"Title": "骨骼数", "Info": "137"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a467fde0ded330d4492bc260aa4c839f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/fx_mod_bingkuai001.FBX"}, {"Title": "顶点数", "Info": "120"}, {"Title": "三角面", "Info": "176"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "39f9bbfa44669cc4388825e4c13aad8f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/fx_mod_bingkuai002.FBX"}, {"Title": "顶点数", "Info": "96"}, {"Title": "三角面", "Info": "148"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "4896e570ab318b145be7c2e1045d71b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/zhanshe<PERSON>_shuipingzuo_1708@show_enter_to_idle.FBX"}, {"Title": "顶点数", "Info": "17265"}, {"Title": "三角面", "Info": "24706"}, {"Title": "骨骼数", "Info": "101"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "58b25c3011cb22645af240bf891f2551", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/zhanshe<PERSON>_shuipingzuo_1708@show_idle.FBX"}, {"Title": "顶点数", "Info": "17265"}, {"Title": "三角面", "Info": "24706"}, {"Title": "骨骼数", "Info": "101"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2e24c1e6067ed944685956a7a71f5906", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/zhanshe<PERSON>_shuipingzuo_1708@show_idle_to_shensheng.FBX"}, {"Title": "顶点数", "Info": "17265"}, {"Title": "三角面", "Info": "24706"}, {"Title": "骨骼数", "Info": "101"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4c62ac0d0a7c0a4c8905070d028115c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/zhan<PERSON><PERSON>_shuipingzuo_1708@show_shensheng_to_idle.FBX"}, {"Title": "顶点数", "Info": "17265"}, {"Title": "三角面", "Info": "24706"}, {"Title": "骨骼数", "Info": "101"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b62a614b7faddcd499d397fb13ef4904", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Model/zhanshen_shuipingzuo_1708_skin.FBX"}, {"Title": "顶点数", "Info": "17269"}, {"Title": "三角面", "Info": "24706"}, {"Title": "骨骼数", "Info": "101"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2fea9b0a771948c468e1f03ac28d2f59", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/changjing/sima<PERSON><PERSON><PERSON><PERSON><PERSON>_dizuo.FBX"}, {"Title": "顶点数", "Info": "5754"}, {"Title": "三角面", "Info": "4648"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3241e849cad8f4d4e978c41a4491194b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Model/pifu_simayi_<PERSON>@show_enter_shanzisuipian.FBX"}, {"Title": "顶点数", "Info": "14850"}, {"Title": "三角面", "Info": "12618"}, {"Title": "骨骼数", "Info": "29"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05b7fa6b2eeb15f45816ffedb9c29635", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Model/pifu_simayi_Cannon_skin.FBX"}, {"Title": "顶点数", "Info": "32993"}, {"Title": "三角面", "Info": "29954"}, {"Title": "骨骼数", "Info": "94"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fe22ed948daed68428fb9ca7db511f71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/<PERSON><PERSON>/Model/simayi_shitou1.obj"}, {"Title": "顶点数", "Info": "1121"}, {"Title": "三角面", "Info": "1996"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ddf30c3b9ad5de46b9ad83eee61d3c0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiShe/Model/zhanshe<PERSON>_sishe@show_idle.fbx"}, {"Title": "顶点数", "Info": "43876"}, {"Title": "三角面", "Info": "43797"}, {"Title": "骨骼数", "Info": "154"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "63e3c6c1767869d47ba9b0e1eafc0d01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiShe/Model/zhanshe<PERSON>_sishe@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92acd55386d241b4e8b39d7f00435101", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiShe/Model/zhanshe<PERSON>_sishe@show_to_idle.fbx"}, {"Title": "顶点数", "Info": "43876"}, {"Title": "三角面", "Info": "43797"}, {"Title": "骨骼数", "Info": "154"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "ac060484a95c57847b039b8e90867766", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiShe/Model/zhanshe<PERSON>_sishe@show_to_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bae61b1c348b87844a40ab92a1825a23", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiShe/Model/zhanshen_sishe_skin.fbx"}, {"Title": "顶点数", "Info": "43884"}, {"Title": "三角面", "Info": "43805"}, {"Title": "骨骼数", "Info": "154"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "093504b1aee54bc4781a35f80a02b933", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_01.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "1636b87171898c146863147a4d6fa28e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_01_cam.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b1b1d4c10cb6a484aa5bb40c60a243b8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_02.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "91dee427e88ccff4ebccd49d60f8b9c5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_02_cam.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef07cdf02c5437c439d57c8ed8655b2d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_03.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "f2ae3097467dbfd4fafba147c69784f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_03_cam.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef6108462fc92f64080949655f1149fd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_04.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "90fdbc2cb0a72d44f902c71ee1f635d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_04_cam.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ba4cd3f5a0788f4fa79d38dc6d97430", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_05.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "f0a442b20b9caa6438039aacbf7b429e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_enter_05_cam.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3016d952339138f4b9ebdd8683960b02", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_idle.fbx"}, {"Title": "顶点数", "Info": "31857"}, {"Title": "三角面", "Info": "47641"}, {"Title": "骨骼数", "Info": "189"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "1b455e3fdc59f48478d768b9cd57abcc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e76af180a3f2e004fa69d2e6254e1e44", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/fish_1588_laojun_skin.fbx"}, {"Title": "顶点数", "Info": "18291"}, {"Title": "三角面", "Info": "26885"}, {"Title": "骨骼数", "Info": "195"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a4c2aeeea3176c4d908ad489e1c350b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_guofengpiaodai01.FBX"}, {"Title": "顶点数", "Info": "220"}, {"Title": "三角面", "Info": "344"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "e722efed217c2af488376c0d16a3eb23", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_jiguang_kuan.FBX"}, {"Title": "顶点数", "Info": "200"}, {"Title": "三角面", "Info": "336"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "bb2724ecc5a9ac94cbee732756e8ff83", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodai02.FBX"}, {"Title": "顶点数", "Info": "125"}, {"Title": "三角面", "Info": "192"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "1ab716619b1c66d43ba8782b830ebe47", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodai04.FBX"}, {"Title": "顶点数", "Info": "185"}, {"Title": "三角面", "Info": "288"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "0b7b7ea18e27cce4dbe017aada14802f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodai05.FBX"}, {"Title": "顶点数", "Info": "155"}, {"Title": "三角面", "Info": "240"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "bde0c9d20b50a1541bbbce6f00eab1ac", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodai06.FBX"}, {"Title": "顶点数", "Info": "125"}, {"Title": "三角面", "Info": "192"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "3d66a64353c124941b1c9ef20c5417f2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodai07.FBX"}, {"Title": "顶点数", "Info": "155"}, {"Title": "三角面", "Info": "240"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "13ca5abd14b38134d847f7cd394cb3df", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/mx_piaodaiyan01.FBX"}, {"Title": "顶点数", "Info": "210"}, {"Title": "三角面", "Info": "320"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "aab0cb57b1531d341b762abd384a9be3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Model/pifu_ta<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa0e31a64001d164990f6489104bf856", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi.FBX"}, {"Title": "顶点数", "Info": "24753"}, {"Title": "三角面", "Info": "27866"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "183d9e64460c4854ca652ed310a114c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi@show_enter.FBX"}, {"Title": "顶点数", "Info": "24751"}, {"Title": "三角面", "Info": "27866"}, {"Title": "骨骼数", "Info": "134"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f23beb346e381f44a69fb144ebf7263", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f412f081ecbe26f45acea23345217f0c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi@show_enter_特效示意.FBX"}, {"Title": "顶点数", "Info": "37309"}, {"Title": "三角面", "Info": "51060"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "1d89800cda8524041acee4527d0bbda5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi@show_idle.FBX"}, {"Title": "顶点数", "Info": "24751"}, {"Title": "三角面", "Info": "27866"}, {"Title": "骨骼数", "Info": "134"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d21e2bcc8bffca4ab557763fab596c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6113000e4ef74a4429c2628a5a579694", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi_skin.FBX"}, {"Title": "顶点数", "Info": "24751"}, {"Title": "三角面", "Info": "27866"}, {"Title": "骨骼数", "Info": "134"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "322f37fc404ae8b49962e49ae8a6c169", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>/Model/zhanshen_tiancheng<PERSON>o_skin.FBX"}, {"Title": "顶点数", "Info": "20905"}, {"Title": "三角面", "Info": "30333"}, {"Title": "骨骼数", "Info": "121"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81b1d69d98f6c8140ac13db1f681616e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Model/zhanshen_tianxiezuo_skin.FBX"}, {"Title": "顶点数", "Info": "27069"}, {"Title": "三角面", "Info": "38677"}, {"Title": "骨骼数", "Info": "138"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1267a5bf4f6023c46adc5dd6229aaa37", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Model/FL_Mesh_007.fbx"}, {"Title": "顶点数", "Info": "10"}, {"Title": "三角面", "Info": "8"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "dcfdb85de1e469847a36a3f25cafbfee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Model/FL_Mesh_008.FBX"}, {"Title": "顶点数", "Info": "25"}, {"Title": "三角面", "Info": "32"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "75263ef3c0bfe7348ac5debb2946a9ec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Model/tiesuoelong@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "3c05e8b4e7b237e429ebb338a0ed738f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Model/tiesuoelong_skin.fbx"}, {"Title": "顶点数", "Info": "8819"}, {"Title": "三角面", "Info": "12304"}, {"Title": "骨骼数", "Info": "109"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000001330559568302957148", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/S_column_2_low.fbx"}, {"Title": "顶点数", "Info": "795"}, {"Title": "三角面", "Info": "378"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000014545339597261333361", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/S_Pine_Bark_Piece_wjswad0_lod3_Var1.fbx"}, {"Title": "顶点数", "Info": "115"}, {"Title": "三角面", "Info": "92"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "a3d8820f27a0b504b802bade42cbf161", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/S_support_1_low 1.FBX"}, {"Title": "顶点数", "Info": "1727"}, {"Title": "三角面", "Info": "1106"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000010134528453259857876", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/<PERSON>_<PERSON><PERSON>_Small_Stone_vizvchy_lod3_Var1.fbx"}, {"Title": "顶点数", "Info": "820"}, {"Title": "三角面", "Info": "1124"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000016642228828584078825", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Env_Fire_Grass_Patch.fbx"}, {"Title": "顶点数", "Info": "16"}, {"Title": "三角面", "Info": "8"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000007330086298481426015", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Env_Fire_Jagged_Rock_4.fbx"}, {"Title": "顶点数", "Info": "424"}, {"Title": "三角面", "Info": "609"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000014643435231469774440", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Env_Fire_OddsnEnds_Chain2.fbx"}, {"Title": "顶点数", "Info": "1038"}, {"Title": "三角面", "Info": "792"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000004607663505738070047", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Forge_ChainsB.fbx"}, {"Title": "顶点数", "Info": "1119"}, {"Title": "三角面", "Info": "1124"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000004799643688299916756", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Forge_ChainsC.fbx"}, {"Title": "顶点数", "Info": "1145"}, {"Title": "三角面", "Info": "1146"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "abc00000000008925218219170466647", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/SM_Forge_Sword3.fbx"}, {"Title": "顶点数", "Info": "597"}, {"Title": "三角面", "Info": "646"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 没有开启Mesh优化"}]}, {"GUID": "ab2d8f142ce19644e94ffd7ea54a8e50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/xingkong_ding.FBX"}, {"Title": "顶点数", "Info": "161"}, {"Title": "三角面", "Info": "288"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5543e5b19c37c274ebd8143e73f99d8c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/xingkong_huan 1.FBX"}, {"Title": "顶点数", "Info": "126"}, {"Title": "三角面", "Info": "200"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "325993ca7238aa0499603132c49b88a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Model/xingkong_huan.FBX"}, {"Title": "顶点数", "Info": "162"}, {"Title": "三角面", "Info": "260"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e0e6f802aeed62e4ab970a51b0d93d57", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiat<PERSON>i@show_enter.FBX"}, {"Title": "顶点数", "Info": "16917"}, {"Title": "三角面", "Info": "19732"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d28ec6ab8e62f1447878ba6798706be4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiat<PERSON><PERSON>@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9925e99e4edddf04eb368b58d6831039", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiat<PERSON>i@show_enter_luobopao.FBX"}, {"Title": "顶点数", "Info": "2634"}, {"Title": "三角面", "Info": "2808"}, {"Title": "骨骼数", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32acb7ffe123497468e6db453bde6761", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiatuzi@show_idle.FBX"}, {"Title": "顶点数", "Info": "16921"}, {"Title": "三角面", "Info": "19688"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e2640dfd6e638904faf8aa07195e2a1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiat<PERSON>i@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e97d2e15e20a553499afd1a01ba45211", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiatuzi_zhanshen_jijiatuzi_luobopao_skin.FBX"}, {"Title": "顶点数", "Info": "2634"}, {"Title": "三角面", "Info": "2808"}, {"Title": "骨骼数", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a3266a3afce6c44aba62db8422c6e48", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Model/fish_1584_jijiatuzi_zhanshen_skin.FBX"}, {"Title": "顶点数", "Info": "16969"}, {"Title": "三角面", "Info": "19840"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f55affd645c12844a806c63caa45fe6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/<PERSON><PERSON>/Model/tusiji<PERSON><PERSON><PERSON>n_dizuo.fbx"}, {"Title": "顶点数", "Info": "1465"}, {"Title": "三角面", "Info": "1568"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eff70fbaddb59814da0d460d4de81ba6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Model/zhanshen_wuyinxm@show_enter.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "142"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "670cb47d3acd68e4d867e54121817b01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Model/zhanshen_wuyinxm@show_enter_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "72b3071c5ea16634398255ec8efc1c95", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Model/zhanshen_wuyinxm@show_idle.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "142"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1648038cd653e0a4f98b8888ce4dbf01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Model/zhanshen_wuyinxm@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c72296ea20693e4c97b244df5e930f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Model/zhanshen_wuyinxm_skin.fbx"}, {"Title": "顶点数", "Info": "19524"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "142"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "86e44256640965e43b759dd020b77986", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Scene/CJ_wuyinxiongmao_beijing/CJ_wuyinxiongmao_beijing.fbx"}, {"Title": "顶点数", "Info": "215"}, {"Title": "三角面", "Info": "328"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2a221b84f8064d4bb0b09dd0b6fa3ea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/WuYinXiongMao/Scene/CJ_wuyinxiongmao_dizuo/CJ_wuyinxiongmao_dizuo.fbx"}, {"Title": "顶点数", "Info": "962"}, {"Title": "三角面", "Info": "1248"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000014006974217091519782", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Model/SM_MillHorizontal.fbx"}, {"Title": "顶点数", "Info": "7764"}, {"Title": "三角面", "Info": "8001"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 没有开启Mesh优化"}]}, {"GUID": "abc00000000011707256475033754557", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Model/Yarrow_Mesh.fbx"}, {"Title": "顶点数", "Info": "3156"}, {"Title": "三角面", "Info": "3545"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 没有开启Mesh优化"}]}, {"GUID": "e0a659d777613ec4fb4361d624f78dbb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Model/pifu_x<PERSON><PERSON><PERSON>_<PERSON>@show_enter.fbx"}, {"Title": "顶点数", "Info": "44728"}, {"Title": "三角面", "Info": "53544"}, {"Title": "骨骼数", "Info": "71"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "d9a49128f0c7f704285f0349e4fda576", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Model/pifu_x<PERSON><PERSON><PERSON>_<PERSON>@show_enter_dao.fbx"}, {"Title": "顶点数", "Info": "24820"}, {"Title": "三角面", "Info": "26300"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b210555ca6c7b7c469321179f0c23be1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Model/pifu_x<PERSON><PERSON><PERSON>_<PERSON>@show_idle.fbx"}, {"Title": "顶点数", "Info": "22349"}, {"Title": "三角面", "Info": "29884"}, {"Title": "骨骼数", "Info": "62"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41eea39b9f05f3845947e699bf1556a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Model/pifu_x<PERSON><PERSON><PERSON>_<PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17c8969fa554dc64880595d63f1b3292", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON><PERSON>_xia<PERSON><PERSON><PERSON>@show_idle.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f503e166d21f224e98435ff2924a62f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON><PERSON>_xia<PERSON><PERSON><PERSON>@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "808207f011b29894b9e749d10ce07985", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON><PERSON>_xia<PERSON><PERSON><PERSON>@show_idle_to_mo.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1789131d5feb1bd40b8394778af50ac6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON><PERSON>_xia<PERSON><PERSON><PERSON>@show_to_idle.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa2109ff075b561408e6283e045fd547", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON><PERSON>_xia<PERSON><PERSON><PERSON>@show_to_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c994c619b8b8b54ba51de1dc9733b67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaoYaoJian<PERSON>ian/Model/zhan<PERSON>n_xia<PERSON>xian_skin.fbx"}, {"Title": "顶点数", "Info": "19094"}, {"Title": "三角面", "Info": "28459"}, {"Title": "骨骼数", "Info": "163"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "82908ab6616908549b50fd8e88f18897", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/changjing/taizi.fbx"}, {"Title": "顶点数", "Info": "21558"}, {"Title": "三角面", "Info": "16233"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53162c7305aa17c44bb1ff7d80f6007f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/fish_1602_xiongmao@Show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c62323e9a5f94f4280d586e659f1b51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/fish_1602_xiongmao@Show_enter_majiang.FBX"}, {"Title": "顶点数", "Info": "900"}, {"Title": "三角面", "Info": "900"}, {"Title": "骨骼数", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "310080cb274e77d409894477583bddf2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/fish_1602_xiongmao_skin.FBX"}, {"Title": "顶点数", "Info": "10391"}, {"Title": "三角面", "Info": "16014"}, {"Title": "骨骼数", "Info": "105"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a1e2f22588856648a38772a722584c7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/fx_mod_xiongmao_lujing.fbx"}, {"Title": "顶点数", "Info": "165"}, {"Title": "三角面", "Info": "256"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "6dfe5a965637b094286c969c08fff7ea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhan<PERSON><PERSON>_xiupunuosi@show_enter.fbx"}, {"Title": "顶点数", "Info": "23826"}, {"Title": "三角面", "Info": "26452"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24eca9805f9c0c843be42088a07daf89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhan<PERSON><PERSON>_xiupunuosi@show_enter_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8d6bcc16093fe146b2ff4c4bc53b80f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhan<PERSON><PERSON>_xiupunu<PERSON><PERSON>@show_enter_特效参考.fbx"}, {"Title": "顶点数", "Info": "7370"}, {"Title": "三角面", "Info": "11472"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ae97326829ecf654cb932f255b3df104", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhanshen_xiupunuosi@show_idle.fbx"}, {"Title": "顶点数", "Info": "23819"}, {"Title": "三角面", "Info": "26452"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "599414cfad592c6458b715b1a60da249", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhan<PERSON>n_xiupunuosi@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70d8e3acd6dbdb54bbb0c8a990f46f1c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiuPuNuoSi/Model/zhanshen_xiupunuosi_skin.fbx"}, {"Title": "顶点数", "Info": "23819"}, {"Title": "三角面", "Info": "26452"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bad8ccba119e97c4b8d724bb72bda642", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Model/mx_juqi01.FBX"}, {"Title": "顶点数", "Info": "408"}, {"Title": "三角面", "Info": "320"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "1f12ea227f057dc49b784fca1c92cfbd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Model/mx_juqi02.FBX"}, {"Title": "顶点数", "Info": "408"}, {"Title": "三角面", "Info": "320"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "9737202310c9e424b926b613103624c5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Model/mx_yuanhuan_di.FBX"}, {"Title": "顶点数", "Info": "74"}, {"Title": "三角面", "Info": "72"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "31b1e4d0bb36aec40a78e241e4a3dedc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Model/pifu_xuchu_Cannon@show_idle.fbx"}, {"Title": "顶点数", "Info": "30745"}, {"Title": "三角面", "Info": "33121"}, {"Title": "骨骼数", "Info": "85"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cb37bacb38c1df7499cda2f023661109", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Model/pifu_xuchu_Cannon@show_idle_camera.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2ce8646175676d4a8560afeb1462d2f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/scene/FBX/xuchu_dizhuan_01.FBX"}, {"Title": "顶点数", "Info": "10715"}, {"Title": "三角面", "Info": "7527"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9f5366e04ee6d446911f1fcf2c8bc6a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Model/effect_yangjian001.FBX"}, {"Title": "顶点数", "Info": "926"}, {"Title": "三角面", "Info": "1297"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "9f2f0d42403206d429034550c3ad2088", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Model/Moxing12.FBX"}, {"Title": "顶点数", "Info": "55"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "da5aa81dc490e0148a5acb8621c8276d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/Model/pifu_langtou_skin.FBX"}, {"Title": "顶点数", "Info": "5050"}, {"Title": "三角面", "Info": "8000"}, {"Title": "骨骼数", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3fc567a7acd1ed440826433755be8385", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON>jian/Model/pifu_yangjian_skin.FBX"}, {"Title": "顶点数", "Info": "17802"}, {"Title": "三角面", "Info": "27232"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca2ab9f5133154946878596c8299e8f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/Model/show_enter_hand(1).FBX"}, {"Title": "顶点数", "Info": "1889"}, {"Title": "三角面", "Info": "3270"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1fb54e2acc62cda44930c948023ba61a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/Model/sanguo_shenqi_z<PERSON><PERSON>_skin.FBX"}, {"Title": "顶点数", "Info": "27146"}, {"Title": "三角面", "Info": "35816"}, {"Title": "骨骼数", "Info": "73"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6373b96aa0e94c4439c92409a381de83", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Model/shandian_cy_001.FBX"}, {"Title": "顶点数", "Info": "84"}, {"Title": "三角面", "Info": "80"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "8ff0490bdb08a79409d68b9a569fcb28", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuan2/feichuan2.fbx"}, {"Title": "顶点数", "Info": "10559"}, {"Title": "三角面", "Info": "9319"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "206b7c8e1a343a349b569e8152b3b55d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuanjin/feichuan.fbx"}, {"Title": "顶点数", "Info": "5446"}, {"Title": "三角面", "Info": "4699"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "651ec251074c7124ead7d208e771b782", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuanjin/xuanzhuan.fbx"}, {"Title": "顶点数", "Info": "4813"}, {"Title": "三角面", "Info": "4273"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b9092966353b6ad40a621ffbcc5c753e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Model/fx_mod_banyuanzhu01.FBX"}, {"Title": "顶点数", "Info": "102"}, {"Title": "三角面", "Info": "150"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "b07f424ad6e75d54eb48db969a5fbd50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Model/fx_mod_luoxuanxian01.FBX"}, {"Title": "顶点数", "Info": "243"}, {"Title": "三角面", "Info": "312"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39af8ee08532fa240899ebc9c35eedc9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Model/fx_mod_xiepian01.FBX"}, {"Title": "顶点数", "Info": "4"}, {"Title": "三角面", "Info": "2"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": " | 不要开启可读写"}]}, {"GUID": "c9d2ef04ded2eaa4f88c10b9ec5be24b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Model/fx_mod_yuantou01.FBX"}, {"Title": "顶点数", "Info": "114"}, {"Title": "三角面", "Info": "180"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0adc070e3140d844f9982db26c53ed74", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Model/sanguo_shenqi_zhugeliang_skin.fbx"}, {"Title": "顶点数", "Info": "28931"}, {"Title": "三角面", "Info": "30518"}, {"Title": "骨骼数", "Info": "96"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "251490aae84c35a4198fdfb620c643b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter.FBX"}, {"Title": "顶点数", "Info": "25429"}, {"Title": "三角面", "Info": "33762"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "864e43f403a7f21459aa5c6100f8e3f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95d917b8463856b4f97d0980c0156408", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter_long.FBX"}, {"Title": "顶点数", "Info": "10368"}, {"Title": "三角面", "Info": "15407"}, {"Title": "骨骼数", "Info": "45"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c77a38298219d0745ba2d2823d0458f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter_qiang.FBX"}, {"Title": "顶点数", "Info": "18524"}, {"Title": "三角面", "Info": "23232"}, {"Title": "骨骼数", "Info": "44"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9774c2b773e21dd429f1f102ec1f5844", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter_qiang_L.FBX"}, {"Title": "顶点数", "Info": "9262"}, {"Title": "三角面", "Info": "11616"}, {"Title": "骨骼数", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "12330ec303938394385e71bd19beaeee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_enter_qiang_R.FBX"}, {"Title": "顶点数", "Info": "9262"}, {"Title": "三角面", "Info": "11616"}, {"Title": "骨骼数", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e5f6b6422278e1a449515649dd255aac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@show_haiou_idle1.fbx"}, {"Title": "顶点数", "Info": "14470"}, {"Title": "三角面", "Info": "21420"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9707b87de3b266f468c7380ac5a107ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@show_haiou_idle2.fbx"}, {"Title": "顶点数", "Info": "14470"}, {"Title": "三角面", "Info": "21420"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dc6d1824780848b4da089218e22ab081", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@show_idle.FBX"}, {"Title": "顶点数", "Info": "25429"}, {"Title": "三角面", "Info": "33762"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ead7a7b16ccf434c8ed85ee736d7a0d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9bcec0de60235404193858ad236ed21b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_idle_to_shensheng.FBX"}, {"Title": "顶点数", "Info": "25429"}, {"Title": "三角面", "Info": "33762"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ca50cd67fa77ab49b3865e830502569", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_idle_to_shensheng_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "565ede7225051d24e9125775a8ef81d2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@show_shensheng.FBX"}, {"Title": "顶点数", "Info": "25427"}, {"Title": "三角面", "Info": "33770"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "826132798a1d03b40ac21ba97588c605", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_shensheng_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3abf84f10038fe84da5cc4213bd44468", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_shensheng_to_idle.FBX"}, {"Title": "顶点数", "Info": "25429"}, {"Title": "三角面", "Info": "33762"}, {"Title": "骨骼数", "Info": "157"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01cbd401deabb6e489a6ac38366e7f6b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_x<PERSON><PERSON><PERSON><PERSON><PERSON>@show_shensheng_to_idle_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "46884bb8c7386074e910f53728776392", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiarzhanshen_haiou_skin.fbx"}, {"Title": "顶点数", "Info": "14470"}, {"Title": "三角面", "Info": "21420"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "be27703baabf8864aa61adbc1523eafa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/fish_14316_xiarzhanshen_skin.FBX"}, {"Title": "顶点数", "Info": "25500"}, {"Title": "三角面", "Info": "33762"}, {"Title": "骨骼数", "Info": "156"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5564c8206b11b684cb54c15292515ed0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/qiang.FBX"}, {"Title": "顶点数", "Info": "18524"}, {"Title": "三角面", "Info": "23232"}, {"Title": "骨骼数", "Info": "44"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41eb155126c7ea845b291fad6cc275ae", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Model/qieping.FBX"}, {"Title": "顶点数", "Info": "4"}, {"Title": "三角面", "Info": "2"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39bb9c12749ef7e45be181c91b6e589e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Anim/xiari_taiyang<PERSON>@idle.fbx"}, {"Title": "顶点数", "Info": "1727"}, {"Title": "三角面", "Info": "683"}, {"Title": "骨骼数", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8fa1fc891c17c7146a5d4ba43d51ecba", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Anim/xiaripaidui@shu_cao.FBX"}, {"Title": "顶点数", "Info": "34196"}, {"Title": "三角面", "Info": "18484"}, {"Title": "骨骼数", "Info": "414"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "a2f184666003d0848b74ef57b50680e6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_cao.fbx"}, {"Title": "顶点数", "Info": "2691"}, {"Title": "三角面", "Info": "2698"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32d343895826a7547bb96fd10abf4c02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_caomao.fbx"}, {"Title": "顶点数", "Info": "413"}, {"Title": "三角面", "Info": "681"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "efc23aa913f6ae4489329e9e271119f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_dengzi.fbx"}, {"Title": "顶点数", "Info": "781"}, {"Title": "三角面", "Info": "784"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3248d6b9f123aed46b22b645fc1796a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_dimian.fbx"}, {"Title": "顶点数", "Info": "360"}, {"Title": "三角面", "Info": "664"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c60c9c8ed748e524ea7f695bdd128ebe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_haiou.fbx"}, {"Title": "顶点数", "Info": "2894"}, {"Title": "三角面", "Info": "4284"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22278d1164362104790ba04a0ab04d10", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_haixing.fbx"}, {"Title": "顶点数", "Info": "380"}, {"Title": "三角面", "Info": "612"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f50ec4503c37c4c42bf4c5ac2e70f436", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_paiqiu.fbx"}, {"Title": "顶点数", "Info": "752"}, {"Title": "三角面", "Info": "1200"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c05c1723c4fea404eb51c0fa3c612edb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shikuai01.fbx"}, {"Title": "顶点数", "Info": "220"}, {"Title": "三角面", "Info": "256"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0b77566f6b12e43459343a44b89c7ef6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shikuai02.fbx"}, {"Title": "顶点数", "Info": "388"}, {"Title": "三角面", "Info": "403"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f4d7ce55b6fb2df48a28755c6109f5f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shugan01.fbx"}, {"Title": "顶点数", "Info": "1484"}, {"Title": "三角面", "Info": "2067"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b54d409a8b420d40841db189d0548dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shugan02.fbx"}, {"Title": "顶点数", "Info": "1478"}, {"Title": "三角面", "Info": "2101"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "18d43ed0d9094d441a3ec8d622646fee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shutouying.fbx"}, {"Title": "顶点数", "Info": "142"}, {"Title": "三角面", "Info": "133"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dfc4adda78176254d93c8f2cb10f8d2d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_shuye.fbx"}, {"Title": "顶点数", "Info": "978"}, {"Title": "三角面", "Info": "1188"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb34d60c8b2831947ae566d57a1f6e01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_taiyangsan.fbx"}, {"Title": "顶点数", "Info": "694"}, {"Title": "三角面", "Info": "683"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e66bdb9e2ba51274687be98945cca2ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_tangyi01.fbx"}, {"Title": "顶点数", "Info": "566"}, {"Title": "三角面", "Info": "483"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c2a7cd45023cef64193b9f7c5577d2fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_tangyi02.fbx"}, {"Title": "顶点数", "Info": "1687"}, {"Title": "三角面", "Info": "1699"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}]}