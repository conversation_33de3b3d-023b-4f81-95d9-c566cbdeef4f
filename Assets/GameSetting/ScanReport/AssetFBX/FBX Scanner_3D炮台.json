{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetFBXSchema", "ScannerGUID": "95210723-2e68-4943-8a44-5c7cfa09846a", "ReportTitle": "扫描所有模型", "ReportDesc": "规则介绍：检测模型的面数，骨骼数，可读写开关，优化配置等", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "顶点数", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "三角面", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "骨骼数", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "88f2d1d5067594240b3939b31aa451db", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fire1.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "747c5a3d194b00b4ebe8a61c6c917ffe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fire2.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "90819e3bc0721b647b58280a68606282", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fire3.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b94ed4e0feab38f42ade5a154be3fc60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fire4.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4476436361367b848b4da6565e914ef2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3eab23d60d06c9349bd0a5e7a1fec5be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c707dbc1f4195542bf911a6f32120e2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_idle.fbx"}, {"Title": "顶点数", "Info": "6022"}, {"Title": "三角面", "Info": "7434"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "cb7ff7fb51ce2d94e893f1247b0bada6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0877c6579c5b3c043a18de0dcf58eb36", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fish_1714_aiyagesi_low_skin.fbx"}, {"Title": "顶点数", "Info": "9620"}, {"Title": "三角面", "Info": "11491"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a4625a6d47ef36a42bf693653b81d804", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Model/fx_mod_jiao01.FBX"}, {"Title": "顶点数", "Info": "40"}, {"Title": "三角面", "Info": "36"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "826acafe335847a44ba7aad843e8e5f2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "efc0ff32d7200014a848f5a980b55c23", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "60cd57eaa11239d4ea559eccc30f1b92", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3d5d98f5bd1e0a14ea0cdad226d33c42", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8da876e172cb7a3458d98db1197eee2b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/fish_1662_antianshi@room_fury.FBX"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "513f56c597128934ab66ec44e3620b34", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/fish_1662_antianshi@room_fury_to_room_idle.FBX"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "386983144e9f4444ebd1c1c9c20159b6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/fish_1662_antianshi@room_idle.FBX"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5ab0fa1d180c9954580c9068b39942b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/fish_1662_antianshi@room_idle_to_room_fury.FBX"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b4b7eca254d13fb45abd9e1befcac4b6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AnTianShi/Model/fish_1662_antianshi_skin_low.FBX"}, {"Title": "顶点数", "Info": "11055"}, {"Title": "三角面", "Info": "12705"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "143344affb57f9f44a6d1789c950d1db", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON><PERSON>_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "8045"}, {"Title": "三角面", "Info": "10448"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a648f5197e1bd1643bcfd8a4baae75db", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON><PERSON>_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "8045"}, {"Title": "三角面", "Info": "10448"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4fd1d56fa7072fe4cbfba5f606ea3801", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON>o_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "8045"}, {"Title": "三角面", "Info": "10448"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cb76281216a057e43b4e2020351bb591", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON>o_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "8045"}, {"Title": "三角面", "Info": "10448"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8b871b5d408fb7a449a37881a3f54a01", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON>o_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "8045"}, {"Title": "三角面", "Info": "10448"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0abea1091683af747b7520da3023a35c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Model/zhanshen_baiyang<PERSON>o_low_skin.FBX"}, {"Title": "顶点数", "Info": "8466"}, {"Title": "三角面", "Info": "11218"}, {"Title": "骨骼数", "Info": "92"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2e878fe34a2547f478129e2bc036771b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_CaoCao/Model/zhanshen_caocao_low_skin.FBX"}, {"Title": "顶点数", "Info": "12170"}, {"Title": "三角面", "Info": "11238"}, {"Title": "骨骼数", "Info": "69"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2313aa1bfe0ec4d4aabd99fe5d957c54", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhanshe<PERSON>_chiti<PERSON><PERSON>@room_fire1.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8bb9e53bb27d385419745e7e63005b00", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_fire2.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "47ddf8b0995c62d4399a386e7162e913", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_fire3.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "de50c0b3ff872b74f9d8d8f2b64778e8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_fire4.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "253a5d87219a4064eb141565135b2e54", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON><PERSON><PERSON>/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "79075cf25438d3e4cac5195198f7da42", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON><PERSON><PERSON>/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "79f01e1fddeff394f97561e3c37f906c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON><PERSON>/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_idle.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c9998f7f1c609d342a9048078c87dfa0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON><PERSON><PERSON>/Model/zhan<PERSON><PERSON>_chiti<PERSON><PERSON>@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fe3d2d5de0fffd548bd208fc86f9a8cd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhanshen_chitianshi_low@room_skill.FBX"}, {"Title": "顶点数", "Info": "13447"}, {"Title": "三角面", "Info": "13644"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7d31c4949e69501478e3931318355a79", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhanshen_chitianshi_low_skin.FBX"}, {"Title": "顶点数", "Info": "14622"}, {"Title": "三角面", "Info": "15360"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0ca2f6e1966cc684d931839d62f5e692", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Chi<PERSON>shi/Model/zhanshen_chitianshi_skin.fbx"}, {"Title": "顶点数", "Info": "14006"}, {"Title": "三角面", "Info": "14604"}, {"Title": "骨骼数", "Info": "98"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0f21013cd0f18564787870d00626e09d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "6947"}, {"Title": "三角面", "Info": "9304"}, {"Title": "骨骼数", "Info": "78"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "828ed2fd9cd27654b9c8a6dcd904a811", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_enter_chibang.FBX"}, {"Title": "顶点数", "Info": "2050"}, {"Title": "三角面", "Info": "2142"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e51a4e90f5488a40aa7f07d1d4d5b98", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "6947"}, {"Title": "三角面", "Info": "9304"}, {"Title": "骨骼数", "Info": "78"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "2404f321c2c426d43ad8b01071c88e9b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_gongji_loop_chibang.FBX"}, {"Title": "顶点数", "Info": "2050"}, {"Title": "三角面", "Info": "2142"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b9c75dfe5943c64cb8de38aabca9208", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "6947"}, {"Title": "三角面", "Info": "9304"}, {"Title": "骨骼数", "Info": "78"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "32d7ef1f938d4fb409310f3db6e5ff5e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_lichang_chibang.FBX"}, {"Title": "顶点数", "Info": "2050"}, {"Title": "三角面", "Info": "2142"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4383015b64ef7a84a88c4f34399afe55", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "6947"}, {"Title": "三角面", "Info": "9304"}, {"Title": "骨骼数", "Info": "78"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "51dcf258c4f2830458e2690e40f38d0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_xuli_gongji_chibang.FBX"}, {"Title": "顶点数", "Info": "2050"}, {"Title": "三角面", "Info": "2142"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "864474a41d114ab43b57e0cc83bad229", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "6947"}, {"Title": "三角面", "Info": "9304"}, {"Title": "骨骼数", "Info": "78"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "54891ee37817ece49b12439d7730b72d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low@skill_sds_ae_xuli_loop_chibang.FBX"}, {"Title": "顶点数", "Info": "2050"}, {"Title": "三角面", "Info": "2142"}, {"Title": "骨骼数", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6dbb3ee3a3b30aa45a33fcad78bc935b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low_chibang_skin.FBX"}, {"Title": "顶点数", "Info": "2356"}, {"Title": "三角面", "Info": "2418"}, {"Title": "骨骼数", "Info": "28"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "10d6da9521c4d07438e4700b41e23566", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Model/zhanshen_chunvzuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "9528"}, {"Title": "三角面", "Info": "13302"}, {"Title": "骨骼数", "Info": "91"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "35b73bb58d75d21459ae95f699b94f24", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaoShou/Model/fish_1548_daoshou_paotai_low_skin.FBX"}, {"Title": "顶点数", "Info": "9553"}, {"Title": "三角面", "Info": "9986"}, {"Title": "骨骼数", "Info": "112"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "449440520e8f27244a2a300eff501507", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@fire1.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e5b5c0cb079a9c241baa796fe234769a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@fire2.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "de0428eb0d801d44fa390b26af652277", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@fire3.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "1a90310c303cdb54d9370f8247109f54", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhanshe<PERSON>_da<PERSON><PERSON>@fire4.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a8b14df2a962a23439c6061f0c8297b8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhanshe<PERSON>_da<PERSON><PERSON>@fire5.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "15ac50a2f126c464387d072bfb63f5dd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@move_fast.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5065cc07342404b47a9ed6ae292ca8bb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@move_fast01.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c47bf69a2155d9a469b6a01e62ae5c88", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@move_fast02.FBX"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fbad25d1c36d1ae418b6ac67e6341bf7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fire1.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "84fca16a4969dab4eb0d4c9741408188", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fire2.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "256f23feb9603ba4d8d7dc64de16f0a6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fire3.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "bd5ce7ce12b37e4449352bf81d13d7ce", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fire4.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "1c510e514de55624eb39807c060b815b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a340378e32d2af74ab0ee3156e9a0c40", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c4e5361fa7f77324dbb44b17133841c5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_idle.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "db9d6ef02531d4d48a400698d72a805e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON><PERSON>@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "10036"}, {"Title": "三角面", "Info": "12024"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "664ba84e9d74c544a97538980df230cf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhanshe<PERSON>_da<PERSON><EMAIL>"}, {"Title": "顶点数", "Info": "10595"}, {"Title": "三角面", "Info": "12984"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b3e1730d8338cbf449cab8ee2c84e3d9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhanshe<PERSON>_da<PERSON><EMAIL>"}, {"Title": "顶点数", "Info": "10595"}, {"Title": "三角面", "Info": "12984"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6ace01c5ac72ff74baa0280031885572", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhan<PERSON><PERSON>_da<PERSON>shi_camera@skill2_camera.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "430168ab297a2d148a44cb6615aa55db", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaTianShi/Model/zhanshen_datianshi_low_skin.fbx"}, {"Title": "顶点数", "Info": "13557"}, {"Title": "三角面", "Info": "16660"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "24655bf33fe7021438d0b0648f35f69a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "34552"}, {"Title": "三角面", "Info": "43317"}, {"Title": "骨骼数", "Info": "245"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "393e609703de00b408ac15f575e10ef9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "34552"}, {"Title": "三角面", "Info": "43317"}, {"Title": "骨骼数", "Info": "245"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "91e500c97ba928d45a5e5ff77d6f0468", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "34552"}, {"Title": "三角面", "Info": "43317"}, {"Title": "骨骼数", "Info": "245"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b7093b1b6aee4584c94d01449fd9b117", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "34552"}, {"Title": "三角面", "Info": "43317"}, {"Title": "骨骼数", "Info": "245"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "72b0a38e275db744287f525aa9710bc5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "34552"}, {"Title": "三角面", "Info": "43317"}, {"Title": "骨骼数", "Info": "245"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a25aa853514400d46afa792e3d7984aa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_low@room_fury.FBX"}, {"Title": "顶点数", "Info": "10536"}, {"Title": "三角面", "Info": "12119"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fdefe6b84d1c55348a7f0b86ec940da3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_low@room_fury_to_room_idle.FBX"}, {"Title": "顶点数", "Info": "10536"}, {"Title": "三角面", "Info": "12119"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "946b162ce4622ce46bc586a3b17505c0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_low@room_idle.FBX"}, {"Title": "顶点数", "Info": "33428"}, {"Title": "三角面", "Info": "41949"}, {"Title": "骨骼数", "Info": "243"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4137725f594703b4594831bac571c0d5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_low@room_idle_to_room_fury.FBX"}, {"Title": "顶点数", "Info": "10536"}, {"Title": "三角面", "Info": "12119"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6870b65131d34f243a0d0bd4ac47cf09", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_low_skin.FBX"}, {"Title": "顶点数", "Info": "10536"}, {"Title": "三角面", "Info": "12119"}, {"Title": "骨骼数", "Info": "122"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d070effe86d3cdd4484d66710f7170bf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fire1.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3a323336421e6084aa59316b54554313", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fire2.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "928a1aff7cc8a244aa100a31c95e3df2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fire3.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4e62e23d36716bf4aac878c839593097", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fire4.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "10f86270569a08449ac0de288add0dd0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4dcbf6ad6cfc84140bb85d75c6a40f8f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "db186853763988c4ebd6b4a42040fc50", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_idle.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "360513d634eebfc43a8094519e9a2025", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "feaa525fbd4e26844816bac8cdcc5c04", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_EMo/Model/fish_1665_emo_paotai_skin.fbx"}, {"Title": "顶点数", "Info": "8711"}, {"Title": "三角面", "Info": "10695"}, {"Title": "骨骼数", "Info": "80"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4f3d2bfc2e725a0479f7ad1f52498a00", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_GuanYu/Model/sanguo_shenqi_guanyu_low_skin.fbx"}, {"Title": "顶点数", "Info": "10389"}, {"Title": "三角面", "Info": "9922"}, {"Title": "骨骼数", "Info": "57"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc64c054d517bb441a28a4524d314d30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadis@room_fire1.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4980356b457c8814bbb23defbe930a3e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadis@room_fire2.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e6cc280b92c4294cb64d8e657dd8f71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadis@room_fire3.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78d4f2162d96a4f47a17a910b2dffd96", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadis@room_fire4.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fe25432fa511a9448b213eb34ba94b8f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadis_low_skin.FBX"}, {"Title": "顶点数", "Info": "31154"}, {"Title": "三角面", "Info": "13296"}, {"Title": "骨骼数", "Info": "86"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c36939f52b80e2f4e98cc6a68646ce5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadisi@room_fury.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f7a0197651184d45bd1cc4d8ce8431f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadisi@room_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02e7570499d509849bc50169463c9507", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadisi@room_idle.FBX"}, {"Title": "顶点数", "Info": "10090"}, {"Title": "三角面", "Info": "10120"}, {"Title": "骨骼数", "Info": "69"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5982af34e7e13bd4ba5ced5ed8662081", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/fish_1713_hadisi@room_idle_to_fury.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "339ffc2a81c099340be5c99e4e5b5543", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>/Model/fish_1581_huangzhong_low_skin.fbx"}, {"Title": "顶点数", "Info": "8617"}, {"Title": "三角面", "Info": "10392"}, {"Title": "骨骼数", "Info": "91"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5efe5959eb9f693459ed2d459250c17a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Jiang<PERSON>/Model/sanguo_shenqi_jiang<PERSON>_low_skin.FBX"}, {"Title": "顶点数", "Info": "14076"}, {"Title": "三角面", "Info": "12266"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "acea2fe528ee45642b17457a070f4164", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/fx_mod_daoguang_huawen01.FBX"}, {"Title": "顶点数", "Info": "297"}, {"Title": "三角面", "Info": "384"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1de996ffc380d7e4281a1fa1650b2730", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/fx_mod_daoguang_sanjiao01.FBX"}, {"Title": "顶点数", "Info": "48"}, {"Title": "三角面", "Info": "16"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0bcf1eb9d5e4fe6489621b7c94b1dcec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_low_@room_skill1.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f9c275d011f37bb46a6355d4b39f4050", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fire1.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "9ef2741f61c1e2343a9519b0d21b8b38", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fire2.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d8f768b42a4d2464abb99c04400cb9bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fire3.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "acfdaf450bd08804d8e7e6d5821c1497", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fire4.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ee4c5668ac34bae40985cc2f44cd6c15", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fda4467e721ca68429abef3189055ebd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cc95c48509cff7d488432f550f2de8a3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_idle.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e93d72a5cc95b4f4e962006ee3e944c2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "11582"}, {"Title": "三角面", "Info": "12384"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8f0eb8c54e2b79e40908a63f0e0bf00b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JingLing/Model/zhanshen_jingling_paotai_skin.fbx"}, {"Title": "顶点数", "Info": "23236"}, {"Title": "三角面", "Info": "12576"}, {"Title": "骨骼数", "Info": "93"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "bc70c982ea1adb84abe30e69c21bf41d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>/Model/zhan<PERSON>n_jinniu<PERSON><PERSON>_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "0"}, {"Title": "三角面", "Info": "0"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8633801c8ba2e114daea77805c6f0905", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>uo/Model/zhanshen_jinniu<PERSON><PERSON>_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "6656"}, {"Title": "三角面", "Info": "9640"}, {"Title": "骨骼数", "Info": "61"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "b374b355e37d63841b4b5fd69a8e56d6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>/Model/zhan<PERSON>n_jinniu<PERSON><PERSON>_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "6656"}, {"Title": "三角面", "Info": "9640"}, {"Title": "骨骼数", "Info": "61"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "36d0988a109942348bb162880f3b078f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>uo/Model/zhan<PERSON>n_jinniu<PERSON><PERSON>_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "6656"}, {"Title": "三角面", "Info": "9640"}, {"Title": "骨骼数", "Info": "61"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "fac67808642fb9243b6c31e8832366a3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>/Model/zhanshen_jinniu<PERSON><PERSON>_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "6656"}, {"Title": "三角面", "Info": "9640"}, {"Title": "骨骼数", "Info": "61"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "f0acd91da7da9004d91ea2e9ae826e9f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>uo/Model/zhanshen_jinniu<PERSON><PERSON>_low_skin.FBX"}, {"Title": "顶点数", "Info": "9440"}, {"Title": "三角面", "Info": "12882"}, {"Title": "骨骼数", "Info": "68"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "1661381868732a64492ad3ce37f21aee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "9545"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "75c8718a815c03d4db7fd21e7a4f54d7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "9545"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b4d17a894e500fd48968ac4308b58622", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "9545"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "301e67d7a0b3b4e44a925c77fe1806f3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "9545"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "17e5c02b358d3b245a74e856c96901d1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "9545"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "08ebffe2337a9c347b7aeacb18c1f0fb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Model/zhanshen_juxiezuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "9541"}, {"Title": "三角面", "Info": "12373"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6645986fed9410949bffd865c6133bc6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire1.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3fdf513c28e201b4380a8dd90e0cfaca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire2.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7a7c46c5055d31b43a482524cec904fc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire3.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "038630fd62daa6d4489a90fff9259ee5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire4.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3dd162e234081c44aa3715bda7d82e37", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire5.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "64f7affae68c7d541b26e1917e3289f7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f5a0b1f0143d28448b3741f6485f0eee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f2fb33fc8bfd1a6469ae3cb780df5770", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_idle.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "9374ad890518d664b8b628d19e148393", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "14628"}, {"Title": "三角面", "Info": "14986"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "20b9ffa30bd0bce4aa376e50a5ab0a36", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fish_1716_ladamandisi_low_skin.fbx"}, {"Title": "顶点数", "Info": "11641"}, {"Title": "三角面", "Info": "13088"}, {"Title": "骨骼数", "Info": "124"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f7ee5868b540ca749ba62a44d0af7eff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Model/fx_mod_lada_ringfire01.FBX"}, {"Title": "顶点数", "Info": "81"}, {"Title": "三角面", "Info": "104"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef6154a98aa732a4baae5e53ddd9fc39", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaoJun/Model/fish_1588_laojun_skin.fbx"}, {"Title": "顶点数", "Info": "7122"}, {"Title": "三角面", "Info": "9379"}, {"Title": "骨骼数", "Info": "141"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "199bcfdeda3ea564cb2ac4402646f299", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiJing/Model/fish_1587_lijing_low_skin.fbx"}, {"Title": "顶点数", "Info": "13577"}, {"Title": "三角面", "Info": "15385"}, {"Title": "骨骼数", "Info": "97"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "30a8f629274015648b3b7fe67f4c26c6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiuBei/Model/zhanshen_liubei_low_skin.FBX"}, {"Title": "顶点数", "Info": "24740"}, {"Title": "三角面", "Info": "30660"}, {"Title": "骨骼数", "Info": "89"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d09b2a7773e63054d8c097b6ae97c40d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LvBu/Model/fish_1580_lvbu_low_skin.fbx"}, {"Title": "顶点数", "Info": "12090"}, {"Title": "三角面", "Info": "10289"}, {"Title": "骨骼数", "Info": "88"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7f39717055a8fcf46907be2c7012094b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Ma<PERSON><PERSON>/Model/sanguo_shenqi_machao_low_skin.fbx"}, {"Title": "顶点数", "Info": "17854"}, {"Title": "三角面", "Info": "13812"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "58d236eaa9fedb24c86e6398ef0d8345", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@idle_to_room_fire_normal.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6580169305716314e924db12befc0355", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f6d356edb41433246967e1b957993aa1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c4f445f48f255634791b5034bdd5027b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal1.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5ef49274e498fce479a00743b36de454", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal2.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7c13ae4b0dee8714ebef29b11ff03f9f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal3.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "21777d6fcfb083b4c851e7203109d052", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal4.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2b8c3413f9a4a5c4abf376dbd555f88c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal5.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ec0e97fc0b1e64449a64585ef803ec55", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fire_normal_to_idle.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "18c0112ae7a06c44db3e6b1b6329448f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e3d46a8ee29c4f1479023ad6b700ab33", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_idle.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e4678014dcaeba042b2bae4146ac02d8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi@room_idle_to_fury.FBX"}, {"Title": "顶点数", "Info": "33923"}, {"Title": "三角面", "Info": "36568"}, {"Title": "骨骼数", "Info": "129"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "329728ef88398844984617b751f162ff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fish_1715_minuosi_skin_low.FBX"}, {"Title": "顶点数", "Info": "16324"}, {"Title": "三角面", "Info": "15004"}, {"Title": "骨骼数", "Info": "115"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e148a1b8e78b404469b1ca5691919644", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Model/fx_mod_minuosi_ringfire01.FBX"}, {"Title": "顶点数", "Info": "81"}, {"Title": "三角面", "Info": "104"}, {"Title": "骨骼数", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "910ed6649711db342b0b486fd2530922", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "7636"}, {"Title": "三角面", "Info": "9886"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "3a5d02cc3a344214ca3fd81a11249041", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_enter1.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "8107af2ebb801654f922c023adde6949", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "7636"}, {"Title": "三角面", "Info": "9886"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "428dae479daf97143a08e66f011da9bb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_gongji_loop1.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "d333c7fb30547af4295325c8d7bc0688", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "7636"}, {"Title": "三角面", "Info": "9886"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "aea5afa83bb3bb2428752e8103d38d28", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_lichang1.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "09e6319b3e2aaa34598b87d665d1a11c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "7636"}, {"Title": "三角面", "Info": "9886"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "25e76d2e733dc6a419d60bf1a4f2139c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_xuli_gongji1.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "79f62096f66cfec4c8ce3f60bdc82028", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "7636"}, {"Title": "三角面", "Info": "9886"}, {"Title": "骨骼数", "Info": "81"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "c516d64cd2a6a2342a2322739d5be373", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low@skill_sds_ae_xuli_loop1.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "533e8d3ea35af714e870009e910cd253", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Model/zhanshen_mojiezuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "7686"}, {"Title": "三角面", "Info": "9966"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "a4f1fe485a4072845840c74e20a5572c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_NvWa/Model/fish_shj_NvWa_skin.FBX"}, {"Title": "顶点数", "Info": "7447"}, {"Title": "三角面", "Info": "9942"}, {"Title": "骨骼数", "Info": "87"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "78eeb7b9c6e47f84093d15326868bf57", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5f0a9f0bce16cb243af0c829ed3c3f6a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "85753a06feef8c44ca098eeebc6d0d5a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "76a97185bd7015b489145b619a45c183", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "17939273100d249499e3a614923927b0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/fish_1666_shengtianshi_low@fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "177e2d164ece5c74ba16e906c625e2bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8177485408071b144ad0152f49b63312", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/fish_1666_shengtianshi_low@idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c03b2592ac657074a9e724ca3911a315", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/fish_1666_shengtianshi_low@room_skill.FBX"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "104f094f55b0bc54eaa5f586d3514dfc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Model/fish_1666_shengtianshi_low_skin.fbx"}, {"Title": "顶点数", "Info": "11565"}, {"Title": "三角面", "Info": "13748"}, {"Title": "骨骼数", "Info": "150"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "12b996e41ea91014389cb30619d92bd5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZ<PERSON>/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "7112"}, {"Title": "三角面", "Info": "9106"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6aeb6085edde61542a6b736e428237b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShou<PERSON>/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "7112"}, {"Title": "三角面", "Info": "9106"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d17ed47bec6529f4eb6ff7258312e235", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShou<PERSON>/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "7112"}, {"Title": "三角面", "Info": "9106"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13bb49c9c7da75148a20299fc8e3f949", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShou<PERSON>/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "7112"}, {"Title": "三角面", "Info": "9106"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "58d2326b1f1dc9642972d3cf62cb873b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZ<PERSON>/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "7112"}, {"Title": "三角面", "Info": "9106"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79a1f4ffd0c237e4aa9b8ba1e808ceef", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_2_skin.FBX"}, {"Title": "顶点数", "Info": "13724"}, {"Title": "三角面", "Info": "17494"}, {"Title": "骨骼数", "Info": "75"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "65a21d89406ec96488db848758de516a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Model/zhan<PERSON><PERSON>_sheshou<PERSON><PERSON>_low_skin.FBX"}, {"Title": "顶点数", "Info": "18771"}, {"Title": "三角面", "Info": "25802"}, {"Title": "骨骼数", "Info": "97"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "615050cfdfaab404196803cc446bccf7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "9178"}, {"Title": "三角面", "Info": "13221"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d0a42436787b2124ab4038922033af10", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "9178"}, {"Title": "三角面", "Info": "13221"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e5481a6b4409887448669cb5465ce046", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "9178"}, {"Title": "三角面", "Info": "13221"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "667d746ce5ae86248a8937603534d41a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "9178"}, {"Title": "三角面", "Info": "13221"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a456a993efae2d04e8d9d94c46b81337", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "9178"}, {"Title": "三角面", "Info": "13221"}, {"Title": "骨骼数", "Info": "83"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "afd81fc97c1760046b778f97d0c997f5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Model/zhanshen_shizizuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "8914"}, {"Title": "三角面", "Info": "13089"}, {"Title": "骨骼数", "Info": "79"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "73e052abb4183d047b77fe7b737d086b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Model/zhanshen_shuangyuzuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "12573"}, {"Title": "三角面", "Info": "17397"}, {"Title": "骨骼数", "Info": "82"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "db89bfaee792b6344b7af666cd25d954", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Model/zhanshen_shuangyuzuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "12573"}, {"Title": "三角面", "Info": "17397"}, {"Title": "骨骼数", "Info": "82"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "db163008579c68c459f1d9d0c9bff82f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Model/zhanshen_shuangyuzuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "12573"}, {"Title": "三角面", "Info": "17397"}, {"Title": "骨骼数", "Info": "82"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "9f976224d78378748ab394498f7c8d8c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Model/zhanshen_shuangyuzuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "12573"}, {"Title": "三角面", "Info": "17397"}, {"Title": "骨骼数", "Info": "82"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d44c3381d5720c54ca8ef0a1de26e10a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Model/zhanshen_shuangyuzuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "12573"}, {"Title": "三角面", "Info": "17397"}, {"Title": "骨骼数", "Info": "82"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "866bcacaea04257449f2bd0f4192a06a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Model/zhanshen_shuangyuzuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "15755"}, {"Title": "三角面", "Info": "21931"}, {"Title": "骨骼数", "Info": "87"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3e95dfc046997be42a9325f3ce6dd877", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "92468fc4339f5af4299a2914b5e3ad5e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3f84627034a75fd429398b261a72f686", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "11b9dc0ef1d2ab244a5ba3d57e74877a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d5fbb2e0d79190d4c9c65e0f9097198c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "77dc2af014f489e4bae879935290470e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6fe025e66e52d64448b72e29ed177ea2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e07af406514bd2b4ea50155c2afdcea5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "1323e1eedd2b674448570697149f31c5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f8517199524a0a447a71a917c2173190", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a1bb3d3cb93a6ed4c87fb2f836fd6690", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "da4f3b8f791c57349b2f0e7b7923c4ae", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@fury_to_idle2.fbx"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a9eb848f11fba2d4bb4b9a84bbe6d19a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e570da172a006434f8890c5983f4b6f3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7dff59ca925dc2848a2fbf331ccde745", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c4da0d5148b30a24dadcc21bdcfda0bf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@idle_to_fury2.fbx"}, {"Title": "顶点数", "Info": "12554"}, {"Title": "三角面", "Info": "15975"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e997cfc5b3e236745b642e86b692c6f1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "12582"}, {"Title": "三角面", "Info": "15887"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "12d064d162a128d47b945c4274ccc280", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "12582"}, {"Title": "三角面", "Info": "15887"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "bbb50934e20007848b7687801c574d9c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "517d2d5abe1dcad409714bb2f96682da", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "12582"}, {"Title": "三角面", "Info": "15887"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ab0c1a9da4ac99342a247d2010492c57", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "12582"}, {"Title": "三角面", "Info": "15887"}, {"Title": "骨骼数", "Info": "95"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "073ecfade52162b4fbc72f7c6f06b5d1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Model/zhanshen_shuangzizuo_low_skin.fbx"}, {"Title": "顶点数", "Info": "12840"}, {"Title": "三角面", "Info": "16275"}, {"Title": "骨骼数", "Info": "100"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "4946f32bcb48e0945b1c2010f375c495", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f0f146f3f325c41488a4cc14c3f587bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_enter1.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e1abec8204b2c5d4e9bf97b22ff4ec92", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fb8ba1770dd1d6348abf8fcc746fb2a9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_gongji_loop1.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5d6c2717340b6434db9885e492b81741", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e9717a5fc6ea93c46a0b443b8e35badc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_lichang1.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "9c0445294bfb67a4ca53309c6e74090d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e399d1844db546e4093425cede72e88e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_xuli_gongji1.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5c62d4b1dd422514c9d452a3159911d9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2955b396e104dac479faa759e1054a39", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low@skill_sds_ae_xuli_loop1.FBX"}, {"Title": "顶点数", "Info": "8010"}, {"Title": "三角面", "Info": "10398"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b609df3adc2bde34aab389bae0beafda", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Model/zhanshen_shuipingzuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "8528"}, {"Title": "三角面", "Info": "11258"}, {"Title": "骨骼数", "Info": "79"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f5ab68fa097b70a47a265af51b947a7a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiMaYi/Model/zhanshen_simayi_low_skin.FBX"}, {"Title": "顶点数", "Info": "14749"}, {"Title": "三角面", "Info": "10983"}, {"Title": "骨骼数", "Info": "76"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f069df061d0fd51449816df790b3f69e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire1.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5c155a4ce1f47f7478252b0e10ac240b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire2.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "87a698b2970e2d64e8e97bfc49f46bfb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire3.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d87fc2492df1eb146952d514d6e1afe3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire4.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d6b61e621ea71884c911061a7aa905dd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire5.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "06ab3aef04a08ad45b5e0f1e5f462b52", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8d0d19afe9c485d468d060ad7a6dfbc3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "a7025b48b409e29449d38dd128d126ca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_idle.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f9b33cab2cb7f364480929f0bac8af5a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d5d720eff8742aa4e92c31ab19bde848", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiShe/Model/zhanshen_sishe_low_skin.fbx"}, {"Title": "顶点数", "Info": "14507"}, {"Title": "三角面", "Info": "12474"}, {"Title": "骨骼数", "Info": "102"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cb7ef65bdbf40194ca6e06c083fc7d58", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_fire1.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d8f05fefa4bc56f4c801e53756823a56", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0d8f8456241cc464d8db471993ae697b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "70edcd8da05a50a4aabe94854a0141fe", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_idle.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "aa11a1a63a4ef9342aeb5ed1bb909258", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d0b7e3238e2f1e14ab50e737ef59c9e3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei@room_xiuxian.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e471766baa7057a4b83016195836829f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Model/fish_1668_taiqiubaobei_low_skin.fbx"}, {"Title": "顶点数", "Info": "8926"}, {"Title": "三角面", "Info": "11843"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7cc3048308233944b990adbdd505f6f2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11752"}, {"Title": "三角面", "Info": "11505"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cb1d4436478ea7d489c4f2ab09908497", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11752"}, {"Title": "三角面", "Info": "11505"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "48f6b08ef4e672d4488117348f0898f0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11752"}, {"Title": "三角面", "Info": "11505"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f380b9f5713bc544881e44c767e08809", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "9420"}, {"Title": "三角面", "Info": "9353"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "59a52fea60e2e1d4aa0a079ba7242de9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11752"}, {"Title": "三角面", "Info": "11505"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fe02be4185f76634dbc6510575dfefe9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/zhanshen_tanatuosi@idle_to_room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "9420"}, {"Title": "三角面", "Info": "9353"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "51f1ea953a7eef242a74bf9c23d316b5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/zhanshen_tanatuosi@room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "9420"}, {"Title": "三角面", "Info": "9353"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "704abeafd3a005f4eb2f707618811072", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/zhanshen_tanatuosi@room_fire_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "9420"}, {"Title": "三角面", "Info": "9353"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "61ba66e7c557eca4a8f591960977ef9f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/zhanshen_tanatuosi@room_idle.FBX"}, {"Title": "顶点数", "Info": "9420"}, {"Title": "三角面", "Info": "9353"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "e8fffdb0efd90cb47b5bd2b79a5eed6d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tanatuosi/Model/zhanshen_tanatuosi_low_skin.FBX"}, {"Title": "顶点数", "Info": "11704"}, {"Title": "三角面", "Info": "11453"}, {"Title": "骨骼数", "Info": "84"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6bc31cc4f9765b04bba9be986ef0a0c5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Model/zhanshen_tiancheng<PERSON>o_low_skin.FBX"}, {"Title": "顶点数", "Info": "11997"}, {"Title": "三角面", "Info": "16587"}, {"Title": "骨骼数", "Info": "79"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "510a1d7fd5d39114bab0e1b0382f839c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Model/zhanshen_tianchen<PERSON>o_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "10842"}, {"Title": "三角面", "Info": "14890"}, {"Title": "骨骼数", "Info": "72"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "415558f2801c1854c9cea6b762cfe9b1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>/Model/zhanshen_tianchenzuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "10842"}, {"Title": "三角面", "Info": "14890"}, {"Title": "骨骼数", "Info": "72"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "874acfe785261264da9bbf1a837df55b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>/Model/zhanshen_tianchenzuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "10842"}, {"Title": "三角面", "Info": "14890"}, {"Title": "骨骼数", "Info": "72"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "1b9037d4dc5a85646a9dfc835d34ab50", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>/Model/zhanshen_tianchenzuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "10842"}, {"Title": "三角面", "Info": "14890"}, {"Title": "骨骼数", "Info": "72"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "316b5ab388d291441b403bea2a74583a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Model/zhanshen_tianchenzuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "10842"}, {"Title": "三角面", "Info": "14890"}, {"Title": "骨骼数", "Info": "72"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d656a68714d05a747b49ee022b27c17b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low@skill_sds_ae_enter.FBX"}, {"Title": "顶点数", "Info": "9871"}, {"Title": "三角面", "Info": "11786"}, {"Title": "骨骼数", "Info": "103"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "73b6f30e0c3d353499eae3713e96b478", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low@skill_sds_ae_gongji_loop.FBX"}, {"Title": "顶点数", "Info": "9871"}, {"Title": "三角面", "Info": "11786"}, {"Title": "骨骼数", "Info": "103"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "85a396aa65a7b4641b1fe460b12e5655", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low@skill_sds_ae_lichang.FBX"}, {"Title": "顶点数", "Info": "9871"}, {"Title": "三角面", "Info": "11786"}, {"Title": "骨骼数", "Info": "103"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5425708e4f214454c85dd869e147e146", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low@skill_sds_ae_xuli_gongji.FBX"}, {"Title": "顶点数", "Info": "9871"}, {"Title": "三角面", "Info": "11786"}, {"Title": "骨骼数", "Info": "103"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "33fe61ed03ef16f46a6f8962e493e452", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low@skill_sds_ae_xuli_loop.FBX"}, {"Title": "顶点数", "Info": "9871"}, {"Title": "三角面", "Info": "11786"}, {"Title": "骨骼数", "Info": "103"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2e96f8235ce2bf64bb9fdccbcc0fc410", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Model/zhanshen_tianxiezuo_low_skin.FBX"}, {"Title": "顶点数", "Info": "10266"}, {"Title": "三角面", "Info": "12414"}, {"Title": "骨骼数", "Info": "108"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c959b96297ca18941b96ba8d1a63f694", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tiesuoelong/Model/1563_huolong_skin.fbx"}, {"Title": "顶点数", "Info": "6679"}, {"Title": "三角面", "Info": "8786"}, {"Title": "骨骼数", "Info": "107"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "da4350292dd0f104bbf56dc788f2245c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jiji<PERSON><PERSON>i@room_fire_normal.FBX"}, {"Title": "顶点数", "Info": "18662"}, {"Title": "三角面", "Info": "22636"}, {"Title": "骨骼数", "Info": "139"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7419ac63b862c9f41b5d979d69161536", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jijiat<PERSON>i@room_fire_normal_to_idle.FBX"}, {"Title": "顶点数", "Info": "20339"}, {"Title": "三角面", "Info": "25516"}, {"Title": "骨骼数", "Info": "130"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "318524b99b5b84940ae2df5e9dbb8592", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jijiat<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>@room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "16752"}, {"Title": "三角面", "Info": "19528"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e09ffe51459ecca4ca875a12a9f65ef3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jijiat<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>@room_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "17630"}, {"Title": "三角面", "Info": "20464"}, {"Title": "骨骼数", "Info": "142"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "06b1ae0306628f74795100e0ec3c614b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jijiat<PERSON><PERSON>_z<PERSON><PERSON><PERSON>@room_idle.FBX"}, {"Title": "顶点数", "Info": "16752"}, {"Title": "三角面", "Info": "19528"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b912665ac044ef04893986fc8a90d31e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Model/fish_1584_jijiat<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>@room_idle_to_fury.FBX"}, {"Title": "顶点数", "Info": "16752"}, {"Title": "三角面", "Info": "19528"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5e729b94d5840ba479ec67dc21f51055", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_fire1.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ceebe486eb881364f995536663ddcc6d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_fire2.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "7e1faec7605d3b942979efec762faf5c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_fire3.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8900a0283491c464bbeb4b13589ead12", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "50b1c9de275cd164786d15f810b40e13", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "62f68107b9ce393498776ef23f63095d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_idle.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f8db8b4599b2d024e86a3136f02d2360", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "19527"}, {"Title": "三角面", "Info": "26529"}, {"Title": "骨骼数", "Info": "74"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b8b4fe784915baa40a33c570a2f404d8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_WuYinXM/Model/zhanshen_wuyinxm_low_skin.fbx"}, {"Title": "顶点数", "Info": "7513"}, {"Title": "三角面", "Info": "9414"}, {"Title": "骨骼数", "Info": "65"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "d46619d111881194784c7fb69cb4ab29", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaHouDun/Model/fish_1582_xiahoudun_low_skin.fbx"}, {"Title": "顶点数", "Info": "7832"}, {"Title": "三角面", "Info": "10725"}, {"Title": "骨骼数", "Info": "60"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 超多三角面数"}]}, {"GUID": "302438726eadb5a458c849cdc958e663", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_xiaoyaojianxian@room_fire1.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "10c309006ada7b14597e0e606f67e2da", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_xiaoyaojianxian@room_fire2.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "988725d9fd224fa44a46fbbaa362fccb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_x<PERSON><PERSON><PERSON><PERSON>anxia<PERSON>@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "210ae1cf856006549a7ae3f5c2313be2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_x<PERSON><PERSON><PERSON><PERSON><PERSON>xia<PERSON>@room_fury_to_idle.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "fa9a496634803904cadec0bd00834592", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_xiaoyaojianxian@room_idle.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f2559d9408562744a99128693871c9a7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_x<PERSON><PERSON><PERSON><PERSON>anxia<PERSON>@room_idle_to_fury.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "13ee637872e34244bb7c0213a33ea7cf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_xiaoyaojianxian@room_xiuxian.fbx"}, {"Title": "顶点数", "Info": "16141"}, {"Title": "三角面", "Info": "23691"}, {"Title": "骨骼数", "Info": "169"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "c1c0e639932c37b4ea207bc99f4d7b1e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiaoyaojianxian/Model/fish_1639_xiaoyaojianxian_low_skin.fbx"}, {"Title": "顶点数", "Info": "6703"}, {"Title": "三角面", "Info": "7548"}, {"Title": "骨骼数", "Info": "115"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "c6ae8614aee54994a9b0a4e8e87d597e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@idle_to_room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "9414"}, {"Title": "三角面", "Info": "11041"}, {"Title": "骨骼数", "Info": "131"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0bafbf6323134e94a9394b5105948371", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON>n@idle_to_room_fire_normal.FBX"}, {"Title": "顶点数", "Info": "19186"}, {"Title": "三角面", "Info": "26092"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "3178d5155f21e5746bb6f5b5ab234325", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "9414"}, {"Title": "三角面", "Info": "11041"}, {"Title": "骨骼数", "Info": "131"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "6e6f3b7ed755a7f488e72c998c97185b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON><PERSON>@room_fire_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "9414"}, {"Title": "三角面", "Info": "11041"}, {"Title": "骨骼数", "Info": "131"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "5f442d54ba6b2794cb4ebb67d930e20f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON>n@room_fire_normal.FBX"}, {"Title": "顶点数", "Info": "19053"}, {"Title": "三角面", "Info": "25912"}, {"Title": "骨骼数", "Info": "139"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0577739e022c01148844ab33bc5df08a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON><PERSON>n@room_fire_normal_to_idle.FBX"}, {"Title": "顶点数", "Info": "19053"}, {"Title": "三角面", "Info": "25912"}, {"Title": "骨骼数", "Info": "139"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "52fd14780f4e199488eac66ed3bb0181", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiar<PERSON><PERSON>shen@room_idle.FBX"}, {"Title": "顶点数", "Info": "11783"}, {"Title": "三角面", "Info": "17335"}, {"Title": "骨骼数", "Info": "114"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f6ac14ae313aa614d9469c90d1871790", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiarizhanshen@room_xiuxian.FBX"}, {"Title": "顶点数", "Info": "19053"}, {"Title": "三角面", "Info": "25912"}, {"Title": "骨骼数", "Info": "139"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "e5245ef61d3546e41bccc152069354b1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14316_xiarizhanshen_low_skin.FBX"}, {"Title": "顶点数", "Info": "11480"}, {"Title": "三角面", "Info": "13075"}, {"Title": "骨骼数", "Info": "143"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "b83a6f622db479e4bbea5ae870ad4579", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14318_x<PERSON><PERSON><PERSON><PERSON><PERSON>@jiatelin_idle_to_room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "10256"}, {"Title": "三角面", "Info": "11575"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "d6bc90f7472fc9b43b539d876219d652", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14318_xiar<PERSON><PERSON><PERSON>n@jiatelin_room__fire_fury.FBX"}, {"Title": "顶点数", "Info": "10256"}, {"Title": "三角面", "Info": "11575"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "93515f96ea27e5c419cca75dd49a29c8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14318_x<PERSON><PERSON><PERSON><PERSON><PERSON>@jiatelin_room_fire_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "10256"}, {"Title": "三角面", "Info": "11575"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "32584dd8a9b92a444a06ea7ab4617c81", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14318_xiar<PERSON><PERSON>shen@jiatelin_room_idle.FBX"}, {"Title": "顶点数", "Info": "10256"}, {"Title": "三角面", "Info": "11575"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "057e1dcb7a2a4fc42967b527579f60f7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Model/fish_14318_xiarzhanshen@jiatelin_xiuxian.FBX"}, {"Title": "顶点数", "Info": "10256"}, {"Title": "三角面", "Info": "11575"}, {"Title": "骨骼数", "Info": "140"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "be8730505fd26aa44b0a9557712a27b6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiong<PERSON>ao/Model/fish_1602_xiongmao_skin.fbx"}, {"Title": "顶点数", "Info": "5305"}, {"Title": "三角面", "Info": "7759"}, {"Title": "骨骼数", "Info": "105"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "ecd65eab91843eb49bc09562a7c65166", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "133"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ea3abdc2dd4b8724d9ec2909e94d81fd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "133"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f777834ac1959f648bdd497388db0552", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "9689"}, {"Title": "三角面", "Info": "9700"}, {"Title": "骨骼数", "Info": "132"}, {"Title": "错误信息", "Info": " | 超多骨骼数"}]}, {"GUID": "d6e193a918171c94ba1ccb91309f933d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/<EMAIL>"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "133"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "0f94526a334bc8f4d897bbb8d3393b10", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/zhanshen_xiupunuosi_low@idle_to_room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "135"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "2a7905513fbd92d4383418340bb23425", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/zhanshen_xiupunuosi_low@room_fire_fury.FBX"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "135"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f8f134d052295c04e9b16e6e293a9bdf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/zhanshen_xiupunuosi_low@room_fire_fury_to_idle.FBX"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "135"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cc3791c7be8e1b64da7e8a1e1ef856bb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/zhanshen_xiupunuosi_low@room_idle.FBX"}, {"Title": "顶点数", "Info": "11462"}, {"Title": "三角面", "Info": "11790"}, {"Title": "骨骼数", "Info": "135"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "ef3343b29cc63b549886c5145e2bd8e2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Xiupunuosi/Model/zhanshen_xiupunuosi_low_skin.FBX"}, {"Title": "顶点数", "Info": "12089"}, {"Title": "三角面", "Info": "12652"}, {"Title": "骨骼数", "Info": "134"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "f17aabb48d123184cae79677187a4394", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XuChu/Model/fish_1583_xuchu_low@room_fire_fury.fbx"}, {"Title": "顶点数", "Info": "44266"}, {"Title": "三角面", "Info": "47057"}, {"Title": "骨骼数", "Info": "85"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "cc7daaec190e5914dbc2961272ebf49f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XuChu/Model/fish_1583_xuchu_low@room_idle.fbx"}, {"Title": "顶点数", "Info": "14224"}, {"Title": "三角面", "Info": "13940"}, {"Title": "骨骼数", "Info": "85"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "8f84463cdee034c46b198f5066b45dee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XuChu/Model/fish_1583_xuchu_low_skin.FBX"}, {"Title": "顶点数", "Info": "14224"}, {"Title": "三角面", "Info": "13940"}, {"Title": "骨骼数", "Info": "85"}, {"Title": "错误信息", "Info": " | 不要开启可读写 | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "07ee8416f1c60cf498b20a0dea3ef25e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_YangJian/Model/fish_1589_yangjian_skin.FBX"}, {"Title": "顶点数", "Info": "8026"}, {"Title": "三角面", "Info": "11082"}, {"Title": "骨骼数", "Info": "104"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}, {"GUID": "9816d47f7e34a4a4dbb4d4741e689f22", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON>/Model/sanguo_shenqi_z<PERSON><PERSON>_low_skin.FBX"}, {"Title": "顶点数", "Info": "11038"}, {"Title": "三角面", "Info": "12130"}, {"Title": "骨骼数", "Info": "59"}, {"Title": "错误信息", "Info": " | 超多三角面数"}]}, {"GUID": "846cf2acbaddf894a9ceaff43b0ec7f3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhugeliang/Model/sanguo_shenqi_zhugeliang_low_skin.FBX"}, {"Title": "顶点数", "Info": "15986"}, {"Title": "三角面", "Info": "15323"}, {"Title": "骨骼数", "Info": "96"}, {"Title": "错误信息", "Info": " | 超多三角面数 | 超多骨骼数"}]}]}