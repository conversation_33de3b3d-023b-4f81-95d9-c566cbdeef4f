{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetPrefabSchema", "ScannerGUID": "ae6df3a5-b19e-4c4c-b39c-b2a8c3f8ee09", "ReportTitle": "扫描所有预制体", "ReportDesc": "规则介绍：检测预制体的子对象数量和粒子组件数量! 检测粒子组件是否包含冗余Mesh!", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "子对象数量", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "粒子组件数量", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "f29da7e37d50d614197b9c4e4b9d11ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13901.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e0ac086e825c70d4f8795cfb1175fbda", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13902.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3051304f66357ac4e81911153de2c731", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13903.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fca813a9a7e639849801df585cd30d4c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13904.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c150b49d68f18d2498b3af2bd22e47b8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13905.prefab"}, {"Title": "子对象数量", "Info": "77"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": " | [glow]包含冗余网格[Quad] | [glow]包含冗余网格[Quad]"}]}, {"GUID": "c41fe15b5204d3e4e9385cc7862af074", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13906.prefab"}, {"Title": "子对象数量", "Info": "90"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a9414cc0131befe40b04b8eec8ffae61", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13907.prefab"}, {"Title": "子对象数量", "Info": "46"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d6a36cca9277cc14982ff14454b188c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13908.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fdc71c8654f87904eb1e9fe24a0f2348", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13909.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3223ae7de7331c64bb9c834535f7a597", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13910.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ff7f4b84fb744684abd7727aca8872b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13911.prefab"}, {"Title": "子对象数量", "Info": "83"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": " | [wings_02_particle]包含粒子标准着色器 | [wings_02_particle]包含粒子标准着色器"}]}, {"GUID": "f271bdf93ac33614989e704e07c9167a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13912.prefab"}, {"Title": "子对象数量", "Info": "110"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "73a462fa413690d40ae2e95c9caafcf9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13913.prefab"}, {"Title": "子对象数量", "Info": "159"}, {"Title": "粒子组件数量", "Info": "38"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eaabafdc73f0c8846acc3497356f1b11", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13914.prefab"}, {"Title": "子对象数量", "Info": "80"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": " | [wings_03_effect]包含粒子标准着色器 | [wings_03_effect]包含粒子标准着色器"}]}, {"GUID": "e89125d1346b51a4db2748a776abc200", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13915.prefab"}, {"Title": "子对象数量", "Info": "96"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "91c38a9ed6c815f40979c14a16e212d7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13916.prefab"}, {"Title": "子对象数量", "Info": "86"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": " | [light1]包含冗余网格[longtou_wenli] | [light2]包含冗余网格[longtou_wenli]"}]}, {"GUID": "25575d7bd32ad1749a6cb03e18fa6f4c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13917.prefab"}, {"Title": "子对象数量", "Info": "91"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": " | [glow1]包含冗余网格[p_luoxuanneixi_00] | [glow_3]包含冗余网格[p_luoxuanneixi_00] | [lizi]包含冗余网格[p_luoxuanneixi_00]"}]}, {"GUID": "c90244f16f1a8a847a64bd35a3371a90", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13918.prefab"}, {"Title": "子对象数量", "Info": "113"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": " | [effect_3918_01]包含粒子标准着色器 | [effect_3918_01]包含粒子标准着色器"}]}, {"GUID": "e5dddb845f6a6e1468abc206020087e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13919.prefab"}, {"Title": "子对象数量", "Info": "75"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a91bb21b85466234682bbd22c1ee3f78", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13920.prefab"}, {"Title": "子对象数量", "Info": "108"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f1389459e7fb4547bdfedf29286436d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13921.prefab"}, {"Title": "子对象数量", "Info": "107"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": " | [wings_3922_show]包含粒子标准着色器"}]}, {"GUID": "f9ecdf3024d466e4f989c366dcd910c4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13922.prefab"}, {"Title": "子对象数量", "Info": "225"}, {"Title": "粒子组件数量", "Info": "64"}, {"Title": "错误信息", "Info": " | [light1_h]包含冗余网格[longtou_wenli]"}]}, {"GUID": "52f9beaeda02a7647a732826f6f8b5b2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13923.prefab"}, {"Title": "子对象数量", "Info": "93"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": " | [light2]包含冗余网格[longtou_wenli] | [light2]包含冗余网格[longtou_wenli]"}]}, {"GUID": "e7f629f1648bf8b41a04e1e61700ed8a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13925.prefab"}, {"Title": "子对象数量", "Info": "83"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "af4c3cd78a0f009459aa05f823518622", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13926.prefab"}, {"Title": "子对象数量", "Info": "87"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": " | [lizi1]包含冗余网格[Quad] | [lizi2]包含冗余网格[Quad] | [dian1]包含冗余网格[Quad] | [lizi1]包含冗余网格[Quad] | [lizi2]包含冗余网格[Quad] | [dian1]包含冗余网格[Quad] | [effect_3926_l_light]包含粒子标准着色器 | [effect_3926_r_light]包含粒子标准着色器"}]}, {"GUID": "40032f9830716484cbdfc4b12800c39b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13927.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca158f711db4c0e4c94a92b046d5d9f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13928.prefab"}, {"Title": "子对象数量", "Info": "79"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8cdb1f5e9d58f334eb0ca4f6fa94b5b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13931.prefab"}, {"Title": "子对象数量", "Info": "158"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d5277806cc645ce4cbb2ea561858d9c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13933.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8be1f60fda851945bf753cbb7efedae", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13934.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7c805f4e751a43b41ae6053e103e02ab", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13936.prefab"}, {"Title": "子对象数量", "Info": "337"}, {"Title": "粒子组件数量", "Info": "62"}, {"Title": "错误信息", "Info": " | [glow_add]包含冗余网格[Quad] | [glowadd02]包含冗余网格[Quad] | [glowadd02]包含冗余网格[Quad] | [ef_yywing_z_chixu]包含粒子标准着色器 | [ef_yywing_y_chixu]包含粒子标准着色器 | [Particle System]包含粒子标准着色器 | [Particle System]包含粒子标准着色器"}]}, {"GUID": "d2b49644fdd0a8446a3116c945869a84", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13937.prefab"}, {"Title": "子对象数量", "Info": "246"}, {"Title": "粒子组件数量", "Info": "36"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ded972b81cf65be46b8ca9758917677e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13939.prefab"}, {"Title": "子对象数量", "Info": "132"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "23d0a28b9b3ae3c428599ca6a0809698", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13940.prefab"}, {"Title": "子对象数量", "Info": "143"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1362444287046249b0df84db2b42e24", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13941.prefab"}, {"Title": "子对象数量", "Info": "62"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ba1d725b98fc8f74195e107ab3db62ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13943.prefab"}, {"Title": "子对象数量", "Info": "140"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d5529fc5cc1b094eb58415e243134e4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13944.prefab"}, {"Title": "子对象数量", "Info": "97"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": " | [effect_3944_1]包含粒子标准着色器 | [effect_3944_2]包含粒子标准着色器"}]}, {"GUID": "09fbdc0bb2e1e6c41812db9ded207b52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13945.prefab"}, {"Title": "子对象数量", "Info": "77"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f0bc031b9ebd5b5479a996f598eb8ee4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13946.prefab"}, {"Title": "子对象数量", "Info": "246"}, {"Title": "粒子组件数量", "Info": "64"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a59a40c391cfdd54a82401cab3f00400", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13947.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd62933aa2743834cb5b0b4bb665c95e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13948.prefab"}, {"Title": "子对象数量", "Info": "97"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3fdf61d55dd1684d8569b3b3149dae5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13949.prefab"}, {"Title": "子对象数量", "Info": "130"}, {"Title": "粒子组件数量", "Info": "19"}, {"Title": "错误信息", "Info": " | [effect_3949_particle]包含粒子标准着色器 | [effect_3949_particle]包含粒子标准着色器"}]}, {"GUID": "47fef9c0450d87646b4e9cf1700f2d3e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13950.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa20e3d67bd8b224a9d0240172d31528", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13951.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00d288c5a782a1847ab8e4be4ba6517d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13952.prefab"}, {"Title": "子对象数量", "Info": "228"}, {"Title": "粒子组件数量", "Info": "49"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7be1466edf53a3f4683beb2ed4ec5d33", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13953.prefab"}, {"Title": "子对象数量", "Info": "46"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "85d80a3879e63ef4c85defac58e986bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13954.prefab"}, {"Title": "子对象数量", "Info": "138"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "84db8cf6e54184443a7f99d92be4462a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13955.prefab"}, {"Title": "子对象数量", "Info": "120"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "efc074dd150f57b44b67afb02bbc8a6a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13956.prefab"}, {"Title": "子对象数量", "Info": "111"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "189fa03f02e2d434ea7be13d86695deb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13957.prefab"}, {"Title": "子对象数量", "Info": "130"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d2eb355ab0e9a84cb54fb637ffa4408", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13958.prefab"}, {"Title": "子对象数量", "Info": "135"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": " | [ef_3958wing_z_chixu]包含粒子标准着色器 | [ef_3958wing_y_chixu]包含粒子标准着色器"}]}, {"GUID": "de8010a93bae258448e1938ca30277f8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13959.prefab"}, {"Title": "子对象数量", "Info": "132"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": " | [ef_3959wing_y_chixu]包含粒子标准着色器 | [ef_3959wing_z_chixu]包含粒子标准着色器"}]}, {"GUID": "1ee8d0a13e634364386a11f15c79ab94", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13960.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea15be836deaba649b2c11592008bb24", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13961.prefab"}, {"Title": "子对象数量", "Info": "134"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ed7a65ab5cd9b34986595dd6b48550e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13962.prefab"}, {"Title": "子对象数量", "Info": "166"}, {"Title": "粒子组件数量", "Info": "24"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2bd7a5397f34f6842bda568336b2a7fc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13963.prefab"}, {"Title": "子对象数量", "Info": "172"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": " | [glow (2)]包含冗余网格[Cube] | [glow (3)]包含冗余网格[Cube] | [zhong]包含粒子标准着色器 | [chiguang1]包含粒子标准着色器 | [chiguang2]包含粒子标准着色器"}]}, {"GUID": "3252315aec45b104daf9dcb49bef622c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13964.prefab"}, {"Title": "子对象数量", "Info": "91"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": " | [zhong]包含粒子标准着色器"}]}, {"GUID": "86b4e041f0307b8488e1cb738f7552bb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13965.prefab"}, {"Title": "子对象数量", "Info": "164"}, {"Title": "粒子组件数量", "Info": "34"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00c649d2afc33b14590b0f22fdd4e5fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13966.prefab"}, {"Title": "子对象数量", "Info": "92"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "14132c358a3cac24d97648be2b998342", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13967.prefab"}, {"Title": "子对象数量", "Info": "107"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e2a19b3d04333df449f476a82ad809b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13969.prefab"}, {"Title": "子对象数量", "Info": "96"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b48a45ecff5fbf94a88c5aeb90f60c6f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13970.prefab"}, {"Title": "子对象数量", "Info": "144"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": " | [ef_yywing_yellow]包含粒子标准着色器 | [ef_yywing_blue]包含粒子标准着色器 | [ef_yywing_z_chixu]包含粒子标准着色器 | [ef_yywing_y_chixu]包含粒子标准着色器"}]}, {"GUID": "c42422cb0c710f646b7660392aeb9dc4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13971.prefab"}, {"Title": "子对象数量", "Info": "95"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7af9e669e06809429c50c0a1bc94edb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13972.prefab"}, {"Title": "子对象数量", "Info": "227"}, {"Title": "粒子组件数量", "Info": "43"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80bbacfa8169c924cb015ea8cdfceee2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13973.prefab"}, {"Title": "子对象数量", "Info": "152"}, {"Title": "粒子组件数量", "Info": "36"}, {"Title": "错误信息", "Info": " | [guang_shan]包含冗余网格[Cube] | [guang_shan]包含冗余网格[Cube] | [Particle System]包含粒子标准着色器 | [Particle System]包含粒子标准着色器"}]}, {"GUID": "0c20c3847e088d64099b6828a714eeff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13974.prefab"}, {"Title": "子对象数量", "Info": "129"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": " | [ef_yywing_y_chixu (2)]包含粒子标准着色器 | [ef_yywing_y_chixu (1)]包含粒子标准着色器"}]}, {"GUID": "201ecd5713760d34ba49df45da3259b6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13975.prefab"}, {"Title": "子对象数量", "Info": "135"}, {"Title": "粒子组件数量", "Info": "25"}, {"Title": "错误信息", "Info": " | [zhong]包含粒子标准着色器 | [chiguang1]包含粒子标准着色器 | [chiguang2]包含粒子标准着色器"}]}, {"GUID": "47807d2dce696884cb26634c5e24603d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13976.prefab"}, {"Title": "子对象数量", "Info": "96"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": " | [lizi]包含冗余网格[Quad]"}]}, {"GUID": "e22ed72c7c910d34ab0dd4304a48139d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13977.prefab"}, {"Title": "子对象数量", "Info": "375"}, {"Title": "粒子组件数量", "Info": "97"}, {"Title": "错误信息", "Info": " | [lizi2 (2)]包含冗余网格[Quad] | [lizi2 (1)]包含冗余网格[Quad] | [juqishanguang]包含粒子标准着色器"}]}, {"GUID": "94ca19f76ec24404dadeebdac83bc3bb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13978.prefab"}, {"Title": "子对象数量", "Info": "111"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": " | [lizi2 (1)]包含冗余网格[Quad] | [lizi2 (1)]包含冗余网格[Quad]"}]}, {"GUID": "47d8eb8eb8a35ff40aa76e9e2df025df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13979.prefab"}, {"Title": "子对象数量", "Info": "101"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8be805e7b8b296340a4236eddb21a6a2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13980.prefab"}, {"Title": "子对象数量", "Info": "104"}, {"Title": "粒子组件数量", "Info": "24"}, {"Title": "错误信息", "Info": " | [lizi2]包含冗余网格[Quad] | [lizi2]包含冗余网格[Quad]"}]}, {"GUID": "b6fac1f99c1c73b43a5e53e76c252207", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13981.prefab"}, {"Title": "子对象数量", "Info": "124"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": " | [lizi2]包含冗余网格[Quad] | [lizi3]包含冗余网格[Quad] | [lizi2]包含冗余网格[Quad] | [lizi3]包含冗余网格[Quad]"}]}, {"GUID": "8be536e5fa910224ba801e077a1a6e6a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13982.prefab"}, {"Title": "子对象数量", "Info": "315"}, {"Title": "粒子组件数量", "Info": "78"}, {"Title": "错误信息", "Info": " | [dian1]包含冗余网格[longtou_wenli] | [lizi1 (2)]包含冗余网格[longtou_wenli] | [lizi1]包含冗余网格[longtou_wenli] | [lizi1 (1)]包含冗余网格[longtou_wenli] | [jiji<PERSON>azhen (2)]包含冗余网格[longtou_wenli] | [trail3 (2)]包含粒子标准着色器 | [trail3 (1)]包含粒子标准着色器"}]}, {"GUID": "cad6b20ded052e5439c556abbb52b02a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13983.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97b8f7602b3f1164aa1046dab2d69446", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13984.prefab"}, {"Title": "子对象数量", "Info": "299"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": " | [light]包含冗余网格[longtou_wenli] | [light]包含冗余网格[longtou_wenli]"}]}, {"GUID": "bdeb609fe00ac7e4fb99f1282cc108a2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13985.prefab"}, {"Title": "子对象数量", "Info": "101"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24709da8e1bf0644b9b5872bda54854c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13986.prefab"}, {"Title": "子对象数量", "Info": "196"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": " | [star]包含冗余网格[Quad] | [star]包含冗余网格[Quad]"}]}, {"GUID": "458ec9317ebc7ec4f8131fb526592e80", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13988.prefab"}, {"Title": "子对象数量", "Info": "129"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": " | [kuosan]包含冗余网格[Quad] | [kuosan]包含冗余网格[Quad]"}]}, {"GUID": "76f43bdef652d8e4fa262c5ec7a45a7e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13989.prefab"}, {"Title": "子对象数量", "Info": "257"}, {"Title": "粒子组件数量", "Info": "25"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "767e58658c8284a48891b74d76ab9003", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/13999.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "633847096dafbe14ca43146cf7777b8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14400.prefab"}, {"Title": "子对象数量", "Info": "126"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "576e19eb8c3a7b2479aa9ebfd16697d3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14401.prefab"}, {"Title": "子对象数量", "Info": "160"}, {"Title": "粒子组件数量", "Info": "30"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c4061c04176e3b4ba94ff86d9066bd3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14402.prefab"}, {"Title": "子对象数量", "Info": "151"}, {"Title": "粒子组件数量", "Info": "30"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22bde7f403225554790bbc91b754d422", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14403.prefab"}, {"Title": "子对象数量", "Info": "355"}, {"Title": "粒子组件数量", "Info": "102"}, {"Title": "错误信息", "Info": " | [jiji<PERSON>az<PERSON> (2)]包含冗余网格[longtou_wenli] | [chachibang_you]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器 | [chachibang_zuo]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器 | [zuobian_kc]包含粒子标准着色器"}]}, {"GUID": "63cb2f021c0cdb8459f785e1342fc6ea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14404.prefab"}, {"Title": "子对象数量", "Info": "190"}, {"Title": "粒子组件数量", "Info": "40"}, {"Title": "错误信息", "Info": " | [lizi2 (1)]包含冗余网格[Quad] | [dian_zhuti01 (1)]包含冗余网格[shandian_003] | [lizi2 (1)]包含冗余网格[Quad] | [dian_zhuti01 (1)]包含冗余网格[shandian_003]"}]}, {"GUID": "6c0b1929a2a6fd04fad5fbe138381133", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14405.prefab"}, {"Title": "子对象数量", "Info": "142"}, {"Title": "粒子组件数量", "Info": "30"}, {"Title": "错误信息", "Info": " | [lizi2 (2)]包含冗余网格[Quad] | [lizi2 (2)]包含冗余网格[Quad] | [lizi2 (2)]包含冗余网格[Quad] | [lizi2 (2)]包含冗余网格[Quad]"}]}, {"GUID": "1fdb1820c274ec14ca7fc5b4a5d9a8b7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14406.prefab"}, {"Title": "子对象数量", "Info": "442"}, {"Title": "粒子组件数量", "Info": "123"}, {"Title": "错误信息", "Info": " | [lizi2 (3)]包含冗余网格[Quad] | [fazheng (3)]包含冗余网格[Quad] | [lizi2 (2)]包含冗余网格[Quad] | [fazheng (2)]包含冗余网格[Quad] | [glow_shan (1)]包含冗余网格[wenzi_yuanpian] | [chachibang_you]包含粒子标准着色器 | [chachibang_zuo]包含粒子标准着色器"}]}, {"GUID": "b24065e59ec806449ad5945062d1f735", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14407.prefab"}, {"Title": "子对象数量", "Info": "228"}, {"Title": "粒子组件数量", "Info": "60"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a5cac68f3752cb64e94b8c1c35836aed", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14408.prefab"}, {"Title": "子对象数量", "Info": "152"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": " | [light]包含冗余网格[longtou_wenli]"}]}, {"GUID": "f554ce3e45b00aa4e9b339c4008a538c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/Prefab/14410.prefab"}, {"Title": "子对象数量", "Info": "379"}, {"Title": "粒子组件数量", "Info": "99"}, {"Title": "错误信息", "Info": " | [ring (1)]包含冗余网格[quanjing_daquan2] | [ring]包含冗余网格[quanjing_daquan2]"}]}]}