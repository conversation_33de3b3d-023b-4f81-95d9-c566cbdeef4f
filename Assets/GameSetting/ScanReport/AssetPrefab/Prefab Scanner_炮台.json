{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetPrefabSchema", "ScannerGUID": "e3758a21-0797-44d7-8723-f9fbca6f6734", "ReportTitle": "扫描所有预制体", "ReportDesc": "规则介绍：检测预制体的子对象数量和粒子组件数量! 检测粒子组件是否包含冗余Mesh!", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "子对象数量", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "粒子组件数量", "Width": 100, "FixedWidth": true, "SearchFiled": false, "SortFiled": true, "IsNumber": true}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "48ab555d08789024c8a44eb72baedb8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/0/bullet_empty.prefab"}, {"Title": "子对象数量", "Info": "2"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb2f7b152db9f9446950eed5f607f24e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/0/bullet_fury.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "34e287aa32664e04897cfb6714d63cad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/0/net_empty.prefab"}, {"Title": "子对象数量", "Info": "2"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3180f1e3bd1c8040949150d8db779eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/0/net_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2018615beccd56d40a084dd9b61b4b8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/1/bullet_1.prefab"}, {"Title": "子对象数量", "Info": "5"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e21cd0b29c0352444847f751ab638889", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/1/cannon_1.prefab"}, {"Title": "子对象数量", "Info": "10"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c8ef599c4671b34f978d8ed859b0418", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/1/net_1.prefab"}, {"Title": "子对象数量", "Info": "5"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "568966f751a442b49a989bee8871e67b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/2/bullet_2.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43a926b8cddf64b4ba54186883198760", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/2/cannon_2.prefab"}, {"Title": "子对象数量", "Info": "10"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6c7dac834a0cf0408f66c64c704df37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/2/net_2.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08d293ceceaf0f84ba7b6c2bb7f79db0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/11/bullet_11.prefab"}, {"Title": "子对象数量", "Info": "11"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "56c566fd882dfda44aad4ea6524dd091", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/11/cannon_11.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b201a54fbc104ed439c771b76626b246", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/11/net_11.prefab"}, {"Title": "子对象数量", "Info": "8"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97b1ec2cd4982e04bbdcf5c1782dcaef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/12/bullet_12.prefab"}, {"Title": "子对象数量", "Info": "11"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd9727b0633af8a4a90be6dd5f174c71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/12/cannon_12.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ae4b811c1461bdc41bbbb0fa5082673c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/12/net_12.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db1f28cbcd901c445aed932dce138941", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/13/bullet_13.prefab"}, {"Title": "子对象数量", "Info": "12"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbd4f2792a0ab3a4f9f0ef3f2d238054", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/13/cannon_13.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1bbfe5d08fe0654b99157620a3aceb4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/13/net_13.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d48580e929c48034089953bf07addad3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/14/bullet_14.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dc6152dc08106cd4aaad8fe1a6dfe134", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/14/cannon_14.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6b5bf07cc4f98944bf89239e3a06e27", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/14/net_14.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "adc43e02039dd5c45a071ebee8804751", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/15/bullet_15.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ebc8b495f5616049933c67c3e44a135", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/15/cannon_15.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8474810f23d32ce439631f75e464e552", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/15/net_15.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c1f979013a58884e8e141c780e1e7a8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/16/bullet_16.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e7b60ec2c20669418874ae59285d081", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/16/cannon_16.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "99cec167eb1f1614d96c5d601c03838b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/16/net_16.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e89b86d56fc47e746bbf27286756636c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/17/bullet_17.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "775f25e920e78504e945aee67e80c9de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/17/cannon_17.prefab"}, {"Title": "子对象数量", "Info": "31"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "106f3e79ee246714abfa92750f80dd26", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/17/net_17.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97e4b1eaa03607f42893e3ef5dacdad6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/18/bullet_18.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c812ee8477651e41a716584bb5cefde", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/18/cannon_18.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9732de22684fdec4f909bd2c7cb2e0d2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/18/net_18.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "daccbfb45e6789b498c21b1254288c29", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/19/bullet_19.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd9b57e8f9daa9e4792a72e803402d78", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/19/cannon_19.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95f3514c5ef60c94f9f3c896bf35a72f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/19/net_19.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "89d913672118d2b4caaeb5498bcf3a8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/100/bullet_100.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3dd4595f82ff17246b1934ee53d67897", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/100/cannon_100.prefab"}, {"Title": "子对象数量", "Info": "52"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d7414a86712b20745b6eae1245df78a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/100/net_100.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "35e2b57b1f2e4ad4295bce0d2fb6b3da", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/106/bullet_106.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b385d7bb5397a3c4e9a035cb5073de6e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/106/bullet_106_fury.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc398c98d374e544eb0df170b1ae0666", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/106/cannon_106.prefab"}, {"Title": "子对象数量", "Info": "61"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9c34b275200369c4fb4f5a782efb9dd3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/106/net_106.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "440dc1c542bc90c48a91bd7b7d4358f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/106/net_106_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bcb423adb12005049b5055e8acc39c6f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/108/bullet_108.prefab"}, {"Title": "子对象数量", "Info": "48"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93607a2d0a807c042a0b2490f5c8ee52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/108/cannon_108.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "36f5593ea0f6ae14e9fb6f067fe6032c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/108/net_108.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ba90d450569e73e4baca0c69a94c3688", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/109/bullet_109.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2f581c0e52bcb24d9a5222d2b248c1a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/109/bullet_109_fury.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "15691629587921242b5b3bb6608db8c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/109/cannon_109.prefab"}, {"Title": "子对象数量", "Info": "7"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "28706cc2dfd2a414f8123ea75dd206e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/109/net_109.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec3be7cae50682441b3e11de7eb4de4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/111/bullet_111.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f89be6851aa58e0499f24d32ba24bcc7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/111/bullet_111_fury.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80066632cdc201e4681172094cd077c5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/111/cannon_111.prefab"}, {"Title": "子对象数量", "Info": "65"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9e4ad32c659ca91428833dff423950d1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/111/net_111.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e3982df15c263d469b49569c2c3423d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/111/net_111_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4fd71c06ad9412a45ae7adec997980f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/112/bullet_112.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09025b735814bf347a4a150a32ac1e63", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/112/cannon_112.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2fd3d7e5921cae34dbba399a42487fd6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/112/net_112.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3418fc3501fbf04aa15047a5b789249", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/113/bullet_113.prefab"}, {"Title": "子对象数量", "Info": "4"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f857442675788040b6669a4db01dc80", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/113/cannon_113.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "072ed62938895bc43aa28a610e65f477", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/113/net_113.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c805b8b47fe98b54dae3c7164c048a7f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/114/bullet_114.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a7678f72d13bdb498860bdc8fcd6f21", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/114/bullet_114_fury.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7cfbc119e0ba79c40bcdb6671300639e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/114/cannon_114.prefab"}, {"Title": "子对象数量", "Info": "84"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a6555772ee822df4c85381e102b835b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/114/net_114.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc6224b4289c13f48a8f7ae7c13b5798", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/114/net_114_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d7ba49c23d559a94db79017e07af60ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/115/bullet_115.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3363fad400b39a74e956fc09c6456def", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/115/cannon_115.prefab"}, {"Title": "子对象数量", "Info": "46"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f973ddb5e735144f81b02a84066d569", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/115/net_115.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "baef44e387e1d8c4b8a74d2b577abf95", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/116/bullet_116.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f6ebaac032360744a9813c2b7825dde6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/116/cannon_116.prefab"}, {"Title": "子对象数量", "Info": "60"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "231236f67e1cd624ab11860ab73294cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/116/net_116.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c76b6aa27da3cd5498e783434c5734c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/117/bullet_117.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0d2b2f5a922043944a93c43e38df36a2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/117/cannon_117.prefab"}, {"Title": "子对象数量", "Info": "129"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1f782c84f48ba640a8e5e831cd6cd88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/117/net_117.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e010778d6c09c7d4aabfe7b386fc9f4b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/118/bullet_118.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "123d78faba7f38441915d1f6bb81b917", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/118/bullet_118_fury.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aabb0ad0d5ec70641b2add4f24c6d290", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/118/cannon_118.prefab"}, {"Title": "子对象数量", "Info": "51"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "51c52b92606a92e408daeb408e98a745", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/118/net_118.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4302091b104fe454e8b5a1f8d6305da6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/118/net_118_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f629a3f3d5a449408921908edcb882c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/119/bullet_119.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "87b1c69faffcd904a912473dbee73937", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/119/bullet_119_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a846f12cf2329846a5c6853ededd010", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/119/cannon_119.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ac05d5e484a20046bfaff17e88b1fa7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/119/net_119.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a47162cd22f844f45b3e8368a7f0ead6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/119/net_119_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a175300bf69fa4448e4817bd30d2990", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/120/bullet_120.prefab"}, {"Title": "子对象数量", "Info": "13"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9a5b62178a58eb24ea463679387b08c4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/120/bullet_120_fury.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f58e1a23990ebf41915f46e35051959", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/120/cannon_120.prefab"}, {"Title": "子对象数量", "Info": "120"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5aa30c41efad5b949aad33fb19e676d6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/120/net_120.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "61712763192ede443a802243cf1673cb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/120/net_120_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ebd3825b5a487b94aa687f3461f16f6a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/121/bullet_121.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d3a5dae0c37b084abee42141fdaddf4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/121/bullet_121_fury.prefab"}, {"Title": "子对象数量", "Info": "104"}, {"Title": "粒子组件数量", "Info": "27"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9619cdfff31eca44fad21b27ebf72061", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/121/cannon_121.prefab"}, {"Title": "子对象数量", "Info": "43"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "72f6db07436a1a048ae2bd58693bd326", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/121/net_121.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b288e9472a0b16449dcff2b583acfa2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/121/net_121_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eef555c8af2a2fc4f9297cd64e4a6993", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/122/bullet_122.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f50eabe42f2e8fb4ba274f66a94ae25c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/122/cannon_122.prefab"}, {"Title": "子对象数量", "Info": "116"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e16a0a0624a63404d95218e5a541e8d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/122/net_122.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1458745f46d29a64a974bdee9b43fd0d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/124/bullet_124.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e247308ee13c3b4fbea3be8c35faf49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/124/bullet_124_fury.prefab"}, {"Title": "子对象数量", "Info": "13"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f0a1406fb9e4864e9f46ffb69400978", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/124/cannon_124.prefab"}, {"Title": "子对象数量", "Info": "72"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9f02ef4f821d0e45861e82cb01cc00e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/124/net_124.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97e565f7a5282594d8676e558eb1e5af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/124/net_124_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c45a3756fc3d0224893def980ca100c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/125/bullet_125.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32f0b19257728cd48a7181487e1d6e9b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/125/cannon_125.prefab"}, {"Title": "子对象数量", "Info": "75"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c39b4b266757b9647927b1fa6e57d1bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/125/net_125.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5529c531e9228904ea31c780111c0812", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/126/bullet_126.prefab"}, {"Title": "子对象数量", "Info": "15"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "caacc1920923ec748913c61a14c6fcb2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/126/cannon_126.prefab"}, {"Title": "子对象数量", "Info": "61"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ce9c20cb6f260a48bd200a1b072c5ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/126/net_126.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4824766f4339c3a41962fbe072148b83", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/127/bullet_127.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c03b3ffbd034d1c419c2a55ceb2afc56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/127/bullet_127_fury.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "33349fa7e806e694fa17091894f947e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/127/cannon_127.prefab"}, {"Title": "子对象数量", "Info": "70"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01f2ef4854659b94fac5be5a2643c85e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/127/net_127.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "69d782ab220a6f94b827641456290795", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/127/net_127_fury.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c7acf6eef3f0fd4d825233f6316886b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/130/bullet_130.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20734239e8c07bd4ea11761eebba6e60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/130/bullet_130_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1c202bff34dcd44fbdfe71b396b3caf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/130/cannon_130.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25a27df92ad736a428dff43a922cdd02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/130/net_130.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a5d6b3a68c6d9a448b510cc0530d5ce6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/130/net_130_fury.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c349968c7f1de624ea787691396b5e96", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/131/bullet_131.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f505e04801c51d042a661ce51fc5b89c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/131/bullet_131_fury.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c967a32c60dc9544087d9d7ab89b97aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/131/cannon_131.prefab"}, {"Title": "子对象数量", "Info": "78"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "181ce01ba0f417c4e9cb8847fcac70b5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/131/net_131.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4236611345894ad43b846cca71b1e444", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/131/net_131_fury.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6e8f4aaad4e4fae4faaaa4447629f8a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/133/bullet_133.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d5612ae6e5a8d7243b5fc3c21527a735", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/133/bullet_133_fury.prefab"}, {"Title": "子对象数量", "Info": "104"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d6c47ffd1047d404b9956a96e497ce0d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/133/cannon_133.prefab"}, {"Title": "子对象数量", "Info": "76"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ccc0791dfc4eae41b87c8c7a323a00f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/133/net_133.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "49e15ec947f695a42a38acf0b0de0c82", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/133/net_133_fury.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a4bde0ede48fccc448139cc05280cc52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/135/bullet_135.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c40d6bb256fcb9a449e6d2a62bee581d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/135/cannon_135.prefab"}, {"Title": "子对象数量", "Info": "76"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b9768a9c1059ca24fb61a30fe6bcad12", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/135/net_135.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "18129a00b2fe4c946aa523d64638300f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/137/bullet_137.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5e9f1823dc1795d4086dd10309e0c6cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/137/bullet_137_fury.prefab"}, {"Title": "子对象数量", "Info": "49"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f2fe8a72bfbd2df419d1384487f790ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/137/cannon_137.prefab"}, {"Title": "子对象数量", "Info": "111"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "968514cbb0e4e1845a17f4aac03f779f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/137/net_137.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c78c950e35afb85478b87ecdb7d6d21b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/137/net_137_fury.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ae5efb4a5efcf7348b7864a65209cc92", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/138/bullet_138.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a283cc96413d13644b169778e87d09fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/138/bullet_138_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ea102c0507a2ca4b9c222a991c4abb5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/138/cannon_138.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f2087a7c32ccb142a77917afc02d0f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/138/net_138.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ec642615aa876845908a8cad17f0788", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/138/net_138_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7acc9bb3850f2549af846ae00ed0b68", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/140/bullet_140.prefab"}, {"Title": "子对象数量", "Info": "62"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b260b769a6c1e5479873fb3d5506dc2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/140/cannon_140.prefab"}, {"Title": "子对象数量", "Info": "59"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d731ae4b251a40d49b40e1997e347bce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/140/net_140.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8a0249d3ae67d4d4c90f9d2c04a2bcb6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/141/bullet_141.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e51c02048fee0144b0ed01828188fee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/141/cannon_141.prefab"}, {"Title": "子对象数量", "Info": "140"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "da3b57e0fe32b4e4b9d03098f3d0fd0f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/141/net_141.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea17f0d21b3b85740bc5d9d41bb4f4b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/bullet_142.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c9501a1bec33b645b74f72c4de54df8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/bullet_142_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3636cd4375827d54f85b4d9b53f58e0a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/cannon_142.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2cb3f584407e854b8ac99719a71eef7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/cannon_142_ui.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43b6b9040d72bcf45be7c7c07f50ccf3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/net_142.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "67d066ac7b30f244380d0f662a2a60a6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/142/net_142_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70bdcd7e68712ca4cb60f939ef4d5592", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/144/bullet_144.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ade812a460b09e43ab6a311cefe75c5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/144/cannon_144.prefab"}, {"Title": "子对象数量", "Info": "145"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "786c8525b6cf1d045a4e52e482b691f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/144/net_144.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3cf69c2417c05d40ba2461872d26366", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/145/bullet_145.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f868612760e203249957da3bae5b5f2f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/145/cannon_145.prefab"}, {"Title": "子对象数量", "Info": "37"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65bf97be0fcb7c64d9f5ccc2ff9f5c39", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/145/net_145.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6612a6a46419a24aa1b67b5403ca6e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/146/bullet_146.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "60ebf3d0cdb068d4ca124217dbebdf92", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/146/bullet_146_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "374c75fd06d779142a9fece5eec7007e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/146/cannon_146.prefab"}, {"Title": "子对象数量", "Info": "81"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "45e4542acfc1a014ca747cf69fb0a7c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/146/net_146.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8749bbcf4025a16458eb81ff5b310472", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/146/net_146_fury.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b09ef966f98861142b7c63ba0033b1a9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/147/bullet_147.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "836bb5571f53c824d8e07e441b5702e1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/147/cannon_147.prefab"}, {"Title": "子对象数量", "Info": "82"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "52c6e5fb428314140bd6b1b6ae44d35a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/147/net_147.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bbece9920693c0e408645167abab09c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/148/bullet_148.prefab"}, {"Title": "子对象数量", "Info": "13"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53cf21716edc6284d863e5b8ac1fa963", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/148/bullet_148_fury.prefab"}, {"Title": "子对象数量", "Info": "13"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e1a4d1449d2aa74490ce81eab4b52f7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/148/cannon_148.prefab"}, {"Title": "子对象数量", "Info": "87"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6e295acb4e30b4246aab6181dec6f194", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/148/net_148.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "af6c011333ecb594d9c9130eb09c9dd6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/148/net_148_fury.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "289f4c8898a76324a8eb207929b53172", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/149/bullet_149.prefab"}, {"Title": "子对象数量", "Info": "13"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4473d35023795c0418ae1f1b614d3ed5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/149/bullet_149_fury.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c88d40763aba75643b35ed14b10a5eb4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/149/cannon_149.prefab"}, {"Title": "子对象数量", "Info": "51"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e146ae1e6027fc8478ffdb87c9cf6640", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/149/net_149.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dcfd9da6ebd45e24f9da749d20c18f41", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/149/net_149_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25cec0c838f82ea4ca94c88acee5490c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/150/bullet_150.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37d480ebd0a737d4898234d8130fc4d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/150/cannon_150.prefab"}, {"Title": "子对象数量", "Info": "90"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa792ac837230be469db7047597685a4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/150/net_150.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b56388862453d2c4884b602b15ad3e0b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/151/bullet_151.prefab"}, {"Title": "子对象数量", "Info": "15"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd969bd7b444abb49971669819fe49a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/151/bullet_151_fury.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef38ede9ea9471f459dd7fd4098d5057", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/151/cannon_151.prefab"}, {"Title": "子对象数量", "Info": "108"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "57884a3a157f7c34ea9dae32e6582d8a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/151/net_151.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "14535e8838a5bf24e9b54d146b7c28d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/151/net_151_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9a126de71cec04f43af2c892505af0f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/152/bullet_152.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f40593f4fe6c46d48a0b1f3b0b3af2bf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/152/bullet_152_fury.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1cd9be90bf0feb24191feaa7351ee600", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/152/cannon_152.prefab"}, {"Title": "子对象数量", "Info": "76"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53437af1265836b4981297d73829abec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/152/net_152.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "611cd36f11eb38443946cd381402cfc0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/152/net_152_fury.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb155891fb8bd3a4da70b6e449b3d851", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/153/bullet_153.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7dddd1787ddb22458455b5fb51ed57f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/153/bullet_153_fury.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02f001885956b804f94b9fa3184ced1a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/153/cannon_153.prefab"}, {"Title": "子对象数量", "Info": "98"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7dd492f62918d784981d2c8f057004f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/153/net_153.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c17b29936ad5d2c48afaf8c3dcada55c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/153/net_153_fury.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c472eecd59d74846940a62f10e8bcc5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/154/bullet_154.prefab"}, {"Title": "子对象数量", "Info": "14"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17d98752c814aec4d9bc7048aa16a084", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/154/bullet_154_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2fbb814ae7b4c9b458e5d43d0cd56afe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/154/cannon_154.prefab"}, {"Title": "子对象数量", "Info": "87"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e421059de791fe049a35d6bf1b1ff9a8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/154/net_154.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24558ae301406a442b24c5709e9bc438", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/154/net_154_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ba2c23f287377a45bbf1f8357aa4d22", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/155/bullet_155.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "708e2e639e8678349a3646657a14b005", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/155/bullet_155_fury.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "21a079b9dbf583a46bb914fb057eb737", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/155/cannon_155.prefab"}, {"Title": "子对象数量", "Info": "77"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8361b27904bfe8408395d5b8724989c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/155/net_155.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ecdd6eafae08e94bbbeff9a5ec7faf9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/155/net_155_fury.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "598cc227ae8d5ee42a32546efa3a31c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/156/bullet_156.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b7147bbeb631ad42865cca7a87998f9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/156/bullet_156_fury.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd0acc44bb92af5498b65750f03c38cb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/156/cannon_156.prefab"}, {"Title": "子对象数量", "Info": "74"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d308625730054aa458fb58eeda70eda7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/156/net_156.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fe4736a695ef1eb438ab7da46a31484c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/156/net_156_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a6837b737fcfb2c4fa24721176d22e52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/157/bullet_157.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38723a4a220ed14419cb27665ad2367c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/157/bullet_157_fury.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d58f60c35f8d2c449dde8f2d337d513", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/157/cannon_157.prefab"}, {"Title": "子对象数量", "Info": "67"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bcdce27cb397e8445b27ba196728f22d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/157/net_157.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "094aa9250fb26aa4b8440a4c5705fcfa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/157/net_157_fury.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7652ae8c5f5ef3347b7228b1fdf110af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/158/bullet_158.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41359be2200d9bb47b34b5c270ff98d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/158/bullet_158_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "182531741240e064c97460685ff40f1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/158/cannon_158.prefab"}, {"Title": "子对象数量", "Info": "59"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "931e275b948280f43bf6494558c1c833", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/158/net_158.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d40c82bcdec5f94cb3d0bc8c424ab48", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/158/net_158_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ff1eafa932502c4f89371587da4a989", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/159/bullet_159.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6f1a9e25b935904a9066700e616b831", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/159/cannon_159.prefab"}, {"Title": "子对象数量", "Info": "133"}, {"Title": "粒子组件数量", "Info": "21"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4dec156086a4b394fac009efdbda5a1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/159/net_159.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea4a5591dfca2eb47a385267ee7efb1e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/160/bullet_160.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40a0c1afceae5a44d81b3dea234dac1c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/160/bullet_160_fury.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7fa15e35e5e47a4eb7de5f89faa829a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/160/cannon_160.prefab"}, {"Title": "子对象数量", "Info": "80"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "029630aded8333f4180328b74cb09f2e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/160/net_160.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8afe8163d9b08354181767679fc603e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/160/net_160_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd2703ac95b153c46a4e9672b5d935f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/161/bullet_161.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f85492c1b88182547880a72476cc8eea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/161/bullet_161_fury.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a304af876ebc754c88446883f3d7d02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/161/cannon_161.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6769be3a855fe3a4e93086b67dfee8dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/161/net_161.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3296c9c192bc05147971d3debd7f1baa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/161/net_161_fury.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f0781a16dd4aab4b9ae3281546c3ad2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/162/bullet_162.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3c5b3a10946241458bb0aea9f0baf51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/162/bullet_162_fury.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ccdd15f9017be6f4882d6451623b10e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/162/cannon_162.prefab"}, {"Title": "子对象数量", "Info": "88"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0d479cac8700e1842ab23bcec8d8431e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/162/net_162.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39f54861ec816d94cb3fc06eaba48efc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/162/net_162_fury.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea5f3ec5fc610ac42a720f3269c7eea2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/163/bullet_163.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f36a1aab095cbf249929d2441e36c983", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/163/bullet_163_fury.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cbdc56e94e03aa24f88a50bc1d131732", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/163/cannon_163.prefab"}, {"Title": "子对象数量", "Info": "74"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95052ae35c740a64a872338f47ea9635", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/163/net_163.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3573672987c80804db9355e54bddbb6d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/163/net_163_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d5bc2fe7b58e354eb9d1d886bfbe968", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/164/bullet_164.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "add2d1861dc2a714c8815aca940e3ec3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/164/bullet_164_fury.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1406bdd371a6674295af1e39fdff184", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/164/cannon_164.prefab"}, {"Title": "子对象数量", "Info": "59"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd678e2453eea2c40ac65018ac0a5cf7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/164/net_164.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f1e450f32fbb74742bc70e75d3950f4e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/164/net_164_fury.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c0c3892bdc4dc94a82b2237bc917339", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/165/bullet_165.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39d21ced42d36d3429722c144fe15bb4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/165/bullet_165_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aece8dde8f571384fb3c6ef9e57f7544", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/165/cannon_165.prefab"}, {"Title": "子对象数量", "Info": "67"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d595a8d5d3e1534d94812b9de4c0a79", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/165/net_165.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f5faee11dcdeb9843b8c2e07d292d26f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/165/net_165_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b323e049d9b60d4a8f5f3f0089350c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/166/bullet_166.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c8bc5570195f874e86830322f8593b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/166/bullet_166_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b377f428b5cade48a9cea96d9ec6fff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/166/cannon_166.prefab"}, {"Title": "子对象数量", "Info": "54"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92904d39b2a7019459cd66626dea80df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/166/net_166.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fcaa26ef3ca9e2147bb7655c888a2d82", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/166/net_166_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7642a8c7717ab724085f6922fd52e997", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/167/bullet_167.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65be66bef28e92b41b10293774ed0892", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/167/bullet_167_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c65356803b7119d41b4691604dad2535", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/167/cannon_167.prefab"}, {"Title": "子对象数量", "Info": "64"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "52444a0fdeb994e4ba3ae70046e06c62", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/167/net_167.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "620fd587d310714449f0fdbc963d2b20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/167/net_167_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4a45d4e3346fda943901d70fdabebb2b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/169/bullet_169.prefab"}, {"Title": "子对象数量", "Info": "15"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3a0f89384def40459202a5e588646c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/169/cannon_169.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cb98ebceee89e7a418f8e96e5025ffee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/169/net_169.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "047c100d4ce66db4c99f7dbe1cde4904", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/bullet_170.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f3386fdbf57a6c40840037904da5e4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/bullet_170_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3bcbe15409fa9a54ca4735823d4ca1bd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/cannon_170.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78013c484a717a64d94bb81e5114af0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/cannon_170_1.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "113f405f5f7c9e242b7d04ff3212177e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/net_170.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "adadc3e03d2c610489272ef53a6fd20c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/170/net_170_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "350a2b83f82d6ec47ae4826cea1d1723", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/171/bullet_171.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4cc88993fd5e16343adf6f68e5f767c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/171/cannon_171.prefab"}, {"Title": "子对象数量", "Info": "66"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "487bde405f6901f43b138f42dd133318", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/171/net_171.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "feb8b8db24d451847a7f2c9fb62a2ab8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/172/bullet_172.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e13379b264a67142b952467b95fe13a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/172/bullet_172_fury.prefab"}, {"Title": "子对象数量", "Info": "46"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ee59ba26cb775d448478b4a447a4cd0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/172/cannon_172.prefab"}, {"Title": "子对象数量", "Info": "46"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3bf45e30520c5284fbcbc37432722def", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/172/net_172.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d890da60f04d9954d8a6909d8a60ee40", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/172/net_172_fury.prefab"}, {"Title": "子对象数量", "Info": "40"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a7e6a8001dce0342bf051abacfbbaa0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/173/bullet_173.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0908047a0d883f4e9d55f661f69fadf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/173/bullet_173_fury.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0c17d8fdff304d945b247cdaf9e7dea8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/173/cannon_173.prefab"}, {"Title": "子对象数量", "Info": "131"}, {"Title": "粒子组件数量", "Info": "31"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53280acc66cacfc4880bb2e842fdbf6f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/173/net_173.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94971d1b972b0f14a8d76b8d160e1660", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/173/net_173_fury.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f8e406fd21cfd694dabeef0bbc4bfb8a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/174/bullet_174.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6e50d7cead6f6f040a25c7478f919151", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/174/cannon_174.prefab"}, {"Title": "子对象数量", "Info": "111"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3b4dc2666d6ad4740aed92df7c4556d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/174/net_174.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8e7977ee0562014c9ad15aeefc2566f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/175/bullet_175.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76b4f9ca94329974fa0e5e3ce7922fcb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/175/bullet_175_fury.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "451622ebe3abfb6418638c686e1137e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/175/cannon_175.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6aaa6b2df47f0104281c36c1301c40b7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/175/net_175.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bb158eae2db8f9e4b914c167f378219f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/175/net_175_fury.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ff12fec7a74290a49984da7922c1e60b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/176/bullet_176.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a28f07af28850f2429ea5110cf51376f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/176/bullet_176_fury.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1a8497a574050cc4fb1282214a0abf16", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/176/cannon_176.prefab"}, {"Title": "子对象数量", "Info": "99"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92a708d743e6be74ca07e1c20cb728f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/176/net_176.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "805333956a22d044fb2cdbba6b95b62c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/176/net_176_fury.prefab"}, {"Title": "子对象数量", "Info": "44"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e176c9c3ebbd50c49a45ff3afb382bad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/177/bullet_177.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eace0d22bf1ea64428d8adf08c96d32b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/177/bullet_177_fury.prefab"}, {"Title": "子对象数量", "Info": "56"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "68bee43f0346df143bf58990452273d1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/177/cannon_177.prefab"}, {"Title": "子对象数量", "Info": "83"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "512db1917aa51fe46a96759a8b86845e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/177/net_177.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db64369481606694c835450a583cc5a4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/177/net_177_fury.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8ba3909d7b84494aa266b5659379943", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/178/bullet_178.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e68cacb93eccdf428cd1c99d96032b7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/178/bullet_178_fury.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7be661efffbc3dc47a49c9af5ab03928", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/178/cannon_178.prefab"}, {"Title": "子对象数量", "Info": "93"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "793599c4a42f4be45bcb9e55bbd86c54", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/178/net_178.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0f38db1496b700c4da5cb185e2bd5b67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/178/net_178_fury.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "da3dc215ed482a6489203ed00b22300e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/179/bullet_179.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e840fb4dc21703b4889c982151db8c50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/179/cannon_179.prefab"}, {"Title": "子对象数量", "Info": "58"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c51216f91f52dcb42a54ae643691821e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/179/net_179.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "87eadf2e5f2b0c64fa8360ac8f9477a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/180/bullet_180.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "57ae11bccb88e934c85c9e0dd26c5e60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/180/cannon_180.prefab"}, {"Title": "子对象数量", "Info": "142"}, {"Title": "粒子组件数量", "Info": "24"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7628060c32242f45818bfc74524ff07", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/180/net_180.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c73f45b6a43dd21409fd8ccf6b60e2c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/181/bullet_181.prefab"}, {"Title": "子对象数量", "Info": "19"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97a95361e3ac9f14183ee15acea0f898", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/181/cannon_181.prefab"}, {"Title": "子对象数量", "Info": "72"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65e58ee15553bad46a25270a0bedbee0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/181/net_181.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "66c8c54c233617842abf8aeae4a65e18", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/182/bullet_182.prefab"}, {"Title": "子对象数量", "Info": "16"}, {"Title": "粒子组件数量", "Info": "2"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "19b651be9ea28bb42a45805d07ec57ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/182/bullet_182_fury.prefab"}, {"Title": "子对象数量", "Info": "37"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2a41374203661464b8fc1c87b60fcd99", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/182/cannon_182.prefab"}, {"Title": "子对象数量", "Info": "147"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f89641b38aabcea4cbafccf94410758a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/182/net_182.prefab"}, {"Title": "子对象数量", "Info": "17"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "404ccfcb1afa0fc4290e09c448465770", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/182/net_182_fury.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a19c30d200ed3ce4eb5c1051c8a29742", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/183/bullet_183.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "396564197bffe1a4aa2aa4eabd323c26", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/183/bullet_183_fury.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32f600e2d65c71b4f87c4a4e66c57d9c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/183/cannon_183.prefab"}, {"Title": "子对象数量", "Info": "112"}, {"Title": "粒子组件数量", "Info": "27"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f136c544209a08428351e1328260731", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/183/net_183.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb4522fc9b7b37f4c8d9d1d0730e42e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/183/net_183_fury.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "467deb45116a3bb46b64f9d659332a8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/184/bullet_184.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e10a7557477830341be7718b341f033c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/184/cannon_184.prefab"}, {"Title": "子对象数量", "Info": "100"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d0918f041a44c814fa15082bb10715fc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/184/net_184.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "45835429f6ff16242adcbc42b6a649ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/185/bullet_185.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "475fbdf2575e39c47b048f4a74ebdbb2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/185/bullet_185_fury.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59c0e4ee5bcc78046a78ce789c2f4e35", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/185/cannon_185.prefab"}, {"Title": "子对象数量", "Info": "128"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1fe3866e6a14e6e4fafd75fe90b512a1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/185/net_185.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "57889640b6a85e049ac1715258c21d56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/185/net_185_fury.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "050c916bddf1bec4baf9957ad3ce0a3f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/186/bullet_186.prefab"}, {"Title": "子对象数量", "Info": "37"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "06b8f55eb1555af4c8164e825e167a6c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/186/cannon_186.prefab"}, {"Title": "子对象数量", "Info": "234"}, {"Title": "粒子组件数量", "Info": "26"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5df29c8796b738041a6a89f20a299f8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/186/net_186.prefab"}, {"Title": "子对象数量", "Info": "44"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad36361dc29070e42ac7df9993d81d4c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/188/bullet_188.prefab"}, {"Title": "子对象数量", "Info": "31"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d141c2637dcff8c40b59e874321ef6a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/188/cannon_188.prefab"}, {"Title": "子对象数量", "Info": "166"}, {"Title": "粒子组件数量", "Info": "24"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b335a74137ae3b41989e9c6127ca56b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/188/net_188.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "516523719bf57ff4cb7496b442edc3b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/189/bullet_189.prefab"}, {"Title": "子对象数量", "Info": "31"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7493a574dcba46448ff5ba17e82a83c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/189/cannon_189.prefab"}, {"Title": "子对象数量", "Info": "68"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "86c8a93b70cc6c746aac68abd136dc5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/189/net_189.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "85f739fa8173ca64ca30d29fd9236ca0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/200/bullet_200.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ebaa1bef70c00c449883b1ed79607547", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/200/cannon_200.prefab"}, {"Title": "子对象数量", "Info": "91"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ec53ff69bbe8b7469964ae863230801", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/200/net_200.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e5fcae440c24e14e99f504a094644da", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/201/bullet_201.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74b271ab54a05824cba34891e7111c2f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/201/cannon_201.prefab"}, {"Title": "子对象数量", "Info": "79"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a85ac82eaba396a45ab38a4cf5373a0b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/201/net_201.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bdf24ef9b826dcc47b4f23243dfec614", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/202/bullet_202.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3179abb126adb8345ab4b9364abb8c94", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/202/cannon_202.prefab"}, {"Title": "子对象数量", "Info": "66"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a20c3fc607714b6418e6c09324233b37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/202/net_202.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1be26ee426431f4419b6c9532f5c6545", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/203/bullet_203.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de1cd60c040954c4098856ca509cfbbd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/203/cannon_203.prefab"}, {"Title": "子对象数量", "Info": "88"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e793c07002725cc4c92d835eda40b3e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/203/net_203.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6df08c556b12f1d4d8b92d7ed0f0dbd7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/204/bullet_204.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32f805cb24245c149a9418529db80289", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/204/cannon_204.prefab"}, {"Title": "子对象数量", "Info": "61"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "57e2789a97cae2443a481dbb3807f9ce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/204/net_204.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5f49f7df3121eb343a2e01503e7bc14f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/205/bullet_205.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "beabd09318491e448b7a8319db1e3082", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/205/cannon_205.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5689938c7ea1d9e44a935e861ff44dce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/205/net_205.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d53655a1f10b8748b3a35ccc4f160e6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/206/bullet_206.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "411512223cf8d6a42a50161d5b6478fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/206/cannon_206.prefab"}, {"Title": "子对象数量", "Info": "102"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76e5526287d1a984885379f1f2f0700f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/206/net_206.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4842720f56e39f340a12ad45ad50272e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/207/bullet_207.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aadd19f024da2034b86f01ce9830f87a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/207/cannon_207.prefab"}, {"Title": "子对象数量", "Info": "86"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d39edf789996284a895a53ade200a87", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/207/net_207.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "12d5af178a81b0249939e9cb0f5c30da", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/208/bullet_208.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ef40e1a7e5cd3141b7bea89403cd151", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/208/cannon_208.prefab"}, {"Title": "子对象数量", "Info": "106"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11bed663e5b5971418add58244e70e88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/208/net_208.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a156970327863fc4c8acb5ded3c05432", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/210/bullet_210.prefab"}, {"Title": "子对象数量", "Info": "20"}, {"Title": "粒子组件数量", "Info": "3"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "273f4a520924b5d4d932a273c80f201a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/210/cannon_210.prefab"}, {"Title": "子对象数量", "Info": "120"}, {"Title": "粒子组件数量", "Info": "25"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3101bc030732d03489622b3329adebe8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/210/net_210.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7828f23ee2309b42967933ac83592f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/211/bullet_211.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32974e1fc3922804593362b6b0fe1f37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/211/cannon_211.prefab"}, {"Title": "子对象数量", "Info": "128"}, {"Title": "粒子组件数量", "Info": "31"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0128eaaa386d5794ebc5542d54d35d25", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/211/net_211.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11c1856a36c0b684e92cf2ad698925b3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/212/bullet_212.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c0a89085850a2e1479b7253a1c0276e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/212/cannon_212.prefab"}, {"Title": "子对象数量", "Info": "147"}, {"Title": "粒子组件数量", "Info": "33"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2a1cc87f79adaac4b94f7644c42ca673", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/212/cannon_212_ui.prefab"}, {"Title": "子对象数量", "Info": "149"}, {"Title": "粒子组件数量", "Info": "33"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f04a5db781bdf314d98d5638bb23546a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/Prefab/212/net_212.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f279e3341ccec641a1c482e0fe455c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/187/control/cannon_187_prefab.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1b3521c0c6225c44ba6252011332c0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/214/control/cannon_214_prefab.prefab"}, {"Title": "子对象数量", "Info": "275"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c38814fa5f13f9d44b7a7a95d34d62a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/216/control/cannon_216_prefab.prefab"}, {"Title": "子对象数量", "Info": "48"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "482ae4de67b0a4c448c393fcfb40f6cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/218/control/cannon_218_prefab.prefab"}, {"Title": "子对象数量", "Info": "60"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3bf8e485f5fd4f645a50e5286d1fc184", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_GuanYu/Prefab/sanguo_guanyu_low_skin_prefab.prefab"}, {"Title": "子对象数量", "Info": "67"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d727df6ab7e6fa44986018a9bb0100ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Prefab/zhanshen_juxiezuo_low_prefab.prefab"}, {"Title": "子对象数量", "Info": "113"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c512668c404fa0b47a0eaa99c7a589c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Ma<PERSON><PERSON>/Prefab/sanguo_machao_skin_prefab.prefab"}, {"Title": "子对象数量", "Info": "133"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "60c886e4caa5d414aa848a1641468f0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShengTianShi/Prefab/fish_1666_shengtianshi_low_skin.prefab"}, {"Title": "子对象数量", "Info": "167"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "efbcaaff4d9e2df409b3308b82e3d005", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/0/bullet_empty.prefab"}, {"Title": "子对象数量", "Info": "2"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "414974647315b7043b687af0836ae084", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/0/bullet_fury.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "823f1126075d828419c16fd92ac51338", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/0/net_empty.prefab"}, {"Title": "子对象数量", "Info": "2"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca2f3600ded3f8142a1ca0b4b9edddd5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/0/net_fury.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96562f05fe3901a4a8af736aab4bdf3f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13157/bullet_13157.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3dadc1ff89e0c01439a676baa09c1bb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13157/cannon_13157.prefab"}, {"Title": "子对象数量", "Info": "226"}, {"Title": "粒子组件数量", "Info": "29"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e79a131983a0a6b4195b2e79b70778f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13157/net_13157.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80a5d91d54726e74a98521ec99c05897", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13158/bullet_13158.prefab"}, {"Title": "子对象数量", "Info": "22"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "622970b59ebd62844bbca8f56ab2e924", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13158/cannon_13158.prefab"}, {"Title": "子对象数量", "Info": "159"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2bc4fd5887f5bc548909dedeae509624", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13158/net_13158.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dadf5646fcda531468c4ef1e2b5615fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13159/bullet_13159.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "21558bc9aa4563a46848390391d2a32b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13159/cannon_13159.prefab"}, {"Title": "子对象数量", "Info": "236"}, {"Title": "粒子组件数量", "Info": "39"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "849542e97ceac734d90ae2077216a2f9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13159/net_13159.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ddfbe6821544b794a938960ab97b71fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13160/bullet_13160.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79a57e1ed945e5b4c9e8059891986acf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13160/cannon_13160.prefab"}, {"Title": "子对象数量", "Info": "90"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "98ba5cec18a5dbe428597a22a76071ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13160/net_13160.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d44d6c9ad31cb647b2febb319a35870", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13161/bullet_13161.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6122b5bf6fe63ea44baa05c7c2557b26", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13161/cannon_13161.prefab"}, {"Title": "子对象数量", "Info": "270"}, {"Title": "粒子组件数量", "Info": "42"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b8345fb43dfb1dc4a999a189e7dd10cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13161/cannon_13161_back.prefab"}, {"Title": "子对象数量", "Info": "226"}, {"Title": "粒子组件数量", "Info": "27"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e725de3768a8bd54088f2db397015227", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13161/net_13161.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e2e4fe4a700eeef4fb40cca26fa439c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13162/bullet_13162.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "537b50a02d6bb164a971a23945c104f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13162/cannon_13162.prefab"}, {"Title": "子对象数量", "Info": "201"}, {"Title": "粒子组件数量", "Info": "26"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "49e1e58bbc6c0834cb6e7b66834cb1a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13162/net_13162.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd3b51bbdad3b97459239d564b69ae7e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13471/bullet_13471.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "006a58e2b2b8ec4428fc330760759ad9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13471/cannon_13471.prefab"}, {"Title": "子对象数量", "Info": "221"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c53ebad08e6b0e49b6b82e7bc961023", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13471/net_13471.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a76401509edb8a448dde571dcc74127", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13472/bullet_13472.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f4da8d95c672c5499d2a3924a37806b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13472/cannon_13472.prefab"}, {"Title": "子对象数量", "Info": "253"}, {"Title": "粒子组件数量", "Info": "19"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e34fa1879684184896b59fa773537ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13472/net_13472.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0028e59b7d20871458f9419282e0c0a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13473/bullet_13473.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4bd2b88efb4952b489f4e71a81d392e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13473/cannon_13473.prefab"}, {"Title": "子对象数量", "Info": "165"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d4f017426784ebd4b9a9ac68d146e966", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13473/net_13473.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02472e30b96d7444589cbcc609589b97", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13487/bullet_13487.prefab"}, {"Title": "子对象数量", "Info": "49"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e361242421411064b9cb6fb31af92f48", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13487/cannon_13487.prefab"}, {"Title": "子对象数量", "Info": "164"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8127fe1fd9f44e94da93bef3b44dc025", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13487/net_13487.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38b3f54fa36ec7945ba79dc6a6bdb5e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13488/bullet_13488.prefab"}, {"Title": "子对象数量", "Info": "25"}, {"Title": "粒子组件数量", "Info": "1"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a406475b15aafc14f8421063b4765027", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13488/cannon_13488.prefab"}, {"Title": "子对象数量", "Info": "70"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ee13b6d42fb566346ad5a892a87f9d38", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13488/net_13488.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1a912528baa91284cb987f56a1083f84", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13489/bullet_13489.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bb894ded822a87d4482bac2cb5917ef8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13489/cannon_13489.prefab"}, {"Title": "子对象数量", "Info": "163"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22bd1eedd6ae22447b8e40ef8f30a70c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13489/net_13489.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eddb38793050d0e49b5fd69f719f645e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13791/bullet_13791.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "234afa13116c10040941816dd7d872c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13791/cannon_13791.prefab"}, {"Title": "子对象数量", "Info": "158"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7db4a9126690dd243bb0c6c2a028005f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13791/net_13791.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0cb224ea00017334095ae598938e1a45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13864/bullet_13864.prefab"}, {"Title": "子对象数量", "Info": "41"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "afe42826b79280c4ba7a869ff312e428", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13864/cannon_13864.prefab"}, {"Title": "子对象数量", "Info": "208"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "765b1dd09fc6e8544ad36f7363976f5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13864/net_13864.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dc4842e3d0e29dd4a9fec528ba9eb93b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13865/bullet_13865.prefab"}, {"Title": "子对象数量", "Info": "52"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d71ece9b5be8e0944a828f1f8e5f6b3a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13865/cannon_13865.prefab"}, {"Title": "子对象数量", "Info": "192"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "90dbb550a0adb0a44a581220c2503e57", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13865/net_13865.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0926760e07ac9ed43b720494580265ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13866/bullet_13866.prefab"}, {"Title": "子对象数量", "Info": "31"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "740cfea41efd4df4888318912d5b83ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13866/cannon_13866.prefab"}, {"Title": "子对象数量", "Info": "225"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6eeee8cd70c8fa440956b05b0e1868c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13866/net_13866.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd611dae509591b44b64ff73c75f9b0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13867/bullet_13867.prefab"}, {"Title": "子对象数量", "Info": "37"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "21c6ea380d8abf54281198c363684321", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13867/cannon_13867.prefab"}, {"Title": "子对象数量", "Info": "171"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "742c7227769661248b4ee1ef837278b6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13867/net_13867.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a028bf8b1c2540744a9d6bdabcdddb9d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13868/bullet_13868.prefab"}, {"Title": "子对象数量", "Info": "65"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13dae72a25908a143938bd5a38b5d213", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13868/cannon_13868.prefab"}, {"Title": "子对象数量", "Info": "254"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "919fe9b39438d54488b554781c7436d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13868/fury_13868.prefab"}, {"Title": "子对象数量", "Info": "188"}, {"Title": "粒子组件数量", "Info": "59"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0146dc4ac972de94987b21b7c53594d2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13868/net_13868.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c26d01fde6ba9ea4099dbf8f9e6c896c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13869/bullet_13869.prefab"}, {"Title": "子对象数量", "Info": "59"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7099180a490ed73448ea5a27938fcc5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13869/cannon_13869.prefab"}, {"Title": "子对象数量", "Info": "353"}, {"Title": "粒子组件数量", "Info": "62"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c668d345dc9e4fd4aa1c6b14866961d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13869/net_13869.prefab"}, {"Title": "子对象数量", "Info": "57"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ac4c07c22eb72924bacd24e39883fe8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13870/bullet_13870.prefab"}, {"Title": "子对象数量", "Info": "66"}, {"Title": "粒子组件数量", "Info": "19"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "15ad6dd5b132199428478267f2879293", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13870/cannon_13870.prefab"}, {"Title": "子对象数量", "Info": "690"}, {"Title": "粒子组件数量", "Info": "152"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce4fd83af32c71644ba867db119ac5ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13870/net_13870.prefab"}, {"Title": "子对象数量", "Info": "68"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "42d372793d558514b97b95f32785146f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13871/bullet_13871.prefab"}, {"Title": "子对象数量", "Info": "53"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db4ac2fba433232409237cc6887237f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13871/cannon_13871.prefab"}, {"Title": "子对象数量", "Info": "373"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95dc4b5dd44f2814dabe471db5b38bfc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13871/net_13871.prefab"}, {"Title": "子对象数量", "Info": "53"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "75895a7fae3eead4d81f1f9106c4ea90", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13872/bullet_13872.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8479c556a4087de4c9854691d7abfdde", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13872/cannon_13872.prefab"}, {"Title": "子对象数量", "Info": "200"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0c30492ef706ad48aa4e9723a9d7b37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13872/net_13872.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5957d29d0af3348449d0f6c6ff97259c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13873/bullet_13873.prefab"}, {"Title": "子对象数量", "Info": "56"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65b55dc7674bd014b883a39f8f33efcf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13873/cannon_13873.prefab"}, {"Title": "子对象数量", "Info": "337"}, {"Title": "粒子组件数量", "Info": "42"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7af6219d2abe5e3478b3478dffc03b0f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13873/net_13873.prefab"}, {"Title": "子对象数量", "Info": "68"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5377c0280d15d424eb385e0b2012cf74", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13874/bullet_13874.prefab"}, {"Title": "子对象数量", "Info": "56"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd8d49b69473a1c45a326b06e70be2fe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13874/cannon_13874.prefab"}, {"Title": "子对象数量", "Info": "383"}, {"Title": "粒子组件数量", "Info": "91"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41d1c3d7abf952544ba3cde435599a89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13874/net_13874.prefab"}, {"Title": "子对象数量", "Info": "65"}, {"Title": "粒子组件数量", "Info": "21"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "200e24518fefed3448a597d3dfb9eb02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13875/bullet_13875.prefab"}, {"Title": "子对象数量", "Info": "70"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eff863484b8a4244d9ce3c3de7869624", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13875/cannon_13875.prefab"}, {"Title": "子对象数量", "Info": "420"}, {"Title": "粒子组件数量", "Info": "90"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a42be594235c114f90d143322bca317", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13875/net_13875.prefab"}, {"Title": "子对象数量", "Info": "74"}, {"Title": "粒子组件数量", "Info": "24"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5de35c70c3236a143972b27a1d58d3d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13888/bullet_13888.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93a5982b9ed1d5c43bf96944cb2ded06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13888/cannon_13888.prefab"}, {"Title": "子对象数量", "Info": "136"}, {"Title": "粒子组件数量", "Info": "4"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cc477260427f595489d671c2455cceaa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13888/net_13888.prefab"}, {"Title": "子对象数量", "Info": "38"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6016f1006027ecc4794209a1905407d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13889/bullet_13889.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2234cc83a2f5d4847bae82ef65b253d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13889/cannon_13889.prefab"}, {"Title": "子对象数量", "Info": "167"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "caed75a10c612ba4dbebd926c89f2781", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13889/net_13889.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6b510332f63da44b9fbf34a5a2f4eaf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13890/bullet_13890.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32ba6cab724c06a478e9b50da55803b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13890/cannon_13890.prefab"}, {"Title": "子对象数量", "Info": "178"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d95e2c9cf27fc184394cd9e67c01f127", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13890/net_13890.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2dc25a905380fb34db1c89ea3498e3e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13891/bullet_13891.prefab"}, {"Title": "子对象数量", "Info": "34"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6da9e9c93e37ba42bf90d4550135b75", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13891/cannon_13891.prefab"}, {"Title": "子对象数量", "Info": "178"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8a2b01829516ed43b967082148ee422", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13891/net_13891.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6234dbb5197168c45b1422007f28ed51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13892/bullet_13892.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "49e4af6691506f94ba4af249a4ebfb57", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13892/cannon_13892.prefab"}, {"Title": "子对象数量", "Info": "188"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94c8dc1e2a7daf04aa7e3fdb074c0e50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13892/fury_13892.prefab"}, {"Title": "子对象数量", "Info": "188"}, {"Title": "粒子组件数量", "Info": "59"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f330444cf2a67074eb41f438d68a36d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13892/net_13892.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b309cf8124504443b45d3ab93d027cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13893/bullet_13893.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea3c52c0ec9f2fb45968ed571d0a3a17", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13893/cannon_13893.prefab"}, {"Title": "子对象数量", "Info": "358"}, {"Title": "粒子组件数量", "Info": "56"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d16aefc633190b47a11c81ae74071e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13893/net_13893.prefab"}, {"Title": "子对象数量", "Info": "45"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13499b0d0c4fa2f4cb401afc048dd132", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13894/bullet_13894.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "081cd5dbc4a29464d964ceba597c1b76", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13894/cannon_13894.prefab"}, {"Title": "子对象数量", "Info": "213"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2de3e67dacc322144b525f480f51d89a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13894/net_13894.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f38a8e0178c0caf4b900b9d0ff0db43b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13895/bullet_13895.prefab"}, {"Title": "子对象数量", "Info": "40"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "23c571aa0af4b66449f47ee86128ccf5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13895/cannon_13895.prefab"}, {"Title": "子对象数量", "Info": "227"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed16cdd67479fda45bd64ed87d315a5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13895/net_13895.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4b919887d6d78046a0ce1ac744b96f3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13896/bullet_13896.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "603407dc43768aa41ab75c2cfb0a3c97", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13896/cannon_13896.prefab"}, {"Title": "子对象数量", "Info": "162"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "442a9893f6d7252419f98042a0936025", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13896/net_13896.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "613254dbc986a8d429afd2f2284190af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13897/bullet_13897.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cdbc8cdacaf101c4ba0246b8275f142d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13897/cannon_13897.prefab"}, {"Title": "子对象数量", "Info": "247"}, {"Title": "粒子组件数量", "Info": "26"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22d404dfdb4b68141a70b93cdae8c521", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13897/net_13897.prefab"}, {"Title": "子对象数量", "Info": "44"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "32b5e285d37462b4c8c281b207b1526b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13898/bullet_13898.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3035e50a25fe7b49a03d70dce765cc6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13898/cannon_13898.prefab"}, {"Title": "子对象数量", "Info": "246"}, {"Title": "粒子组件数量", "Info": "48"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e6030f6d6f83e140bc9cb5531c73454", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13898/net_13898.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "62598ffde53b42c44b675b655921b660", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13899/bullet_13899.prefab"}, {"Title": "子对象数量", "Info": "44"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea24a4c4766d9f143b6db01e479ee06f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13899/bullet_13899_fury.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bbedb5b363970fb4d90a087ab90ecabb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13899/cannon_13899.prefab"}, {"Title": "子对象数量", "Info": "230"}, {"Title": "粒子组件数量", "Info": "32"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "624ea39687e6d074aa2f4ab71d7df761", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/13899/net_13899.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d03cef7456e4814e8aa2796c0f0e07e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14201/bullet_14201.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "152ebe7752039da4584c41ff9fa93ddd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14201/cannon_14201.prefab"}, {"Title": "子对象数量", "Info": "205"}, {"Title": "粒子组件数量", "Info": "22"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f050707b8ac6bbc40b4038e147ce0106", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14201/net_14201.prefab"}, {"Title": "子对象数量", "Info": "18"}, {"Title": "粒子组件数量", "Info": "5"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "85c908db57db46e4081206afeee5519a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14202/bullet_14202.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e933fee6d3a21d848947dc6342189ae9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14202/cannon_14202.prefab"}, {"Title": "子对象数量", "Info": "140"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b092873ace21604f9d49cd58d753ab8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14202/net_14202.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a7616b9fa21ce3344bc862487be8ccc1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14203/bullet_14203.prefab"}, {"Title": "子对象数量", "Info": "51"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "124c72d53e01df74bb5f3131916028fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14203/cannon_14203.prefab"}, {"Title": "子对象数量", "Info": "195"}, {"Title": "粒子组件数量", "Info": "23"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8eb672d6b1fbb2e4c8003bcf13ad41ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14203/net_14203.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08e9c95acd26ac4469f6120c20c26961", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14310/bullet_14310.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "426968e52d203f14fa034ee248b5996d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14310/cannon_14310.prefab"}, {"Title": "子对象数量", "Info": "214"}, {"Title": "粒子组件数量", "Info": "28"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c5577a136044d3448a2a2944e343519", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14310/net_14310.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c62e6f4e9b681204d99d379511756e2c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14312/bullet_14312.prefab"}, {"Title": "子对象数量", "Info": "52"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f86348a53f94034692a513baa03bf9f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14312/cannon_14312.prefab"}, {"Title": "子对象数量", "Info": "190"}, {"Title": "粒子组件数量", "Info": "29"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b710357e0a3276e439e6d107e6c1e9b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14312/net_14312.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c923f5dead18644469a99255fff3d6eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14314/bullet_14314.prefab"}, {"Title": "子对象数量", "Info": "37"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9733b834523d5204491dadf008797caf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14314/cannon_14314.prefab"}, {"Title": "子对象数量", "Info": "199"}, {"Title": "粒子组件数量", "Info": "31"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76fa4460df1a85a49afcbb3b555795f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14314/net_14314.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e47695542f2a27f41a8c69d880890e64", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14316/bullet_14316.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d0f41f3af757831419ae2e00e109bd98", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14316/bullet_14316_fury.prefab"}, {"Title": "子对象数量", "Info": "28"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70c5d0a2322aa0b4ba801cfe8893b6e1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14316/cannon_14316.prefab"}, {"Title": "子对象数量", "Info": "230"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "88774cf98fb0c894fa698ce7d58f00ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14316/net_14316.prefab"}, {"Title": "子对象数量", "Info": "23"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b94df46e34d42b4099d8c8845a90847", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14316/net_14316_fury.prefab"}, {"Title": "子对象数量", "Info": "29"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d9d780f61534434ea8d1c4103faa08a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14318/bullet_14318.prefab"}, {"Title": "子对象数量", "Info": "31"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09fb5999f04018f4aa5be4a5252cb15c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14318/cannon_14318.prefab"}, {"Title": "子对象数量", "Info": "257"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c066d6a74f2a54a44ad1c9c23180c07a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14318/net_14318.prefab"}, {"Title": "子对象数量", "Info": "26"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43108cf1e204dec478b4966586cd410d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14798/bullet_14798.prefab"}, {"Title": "子对象数量", "Info": "32"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bce34dc26556e52429e6e3cb68663a70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14798/bullet_14798_fury.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d88e851b00283f4b81003e27431971d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14798/cannon_14798.prefab"}, {"Title": "子对象数量", "Info": "173"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e863f5ea68bc20e45bb9351adf337b7e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14798/net_14798.prefab"}, {"Title": "子对象数量", "Info": "51"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "310966be6b4b6c3429915537a459b46f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14798/net_14798_fury.prefab"}, {"Title": "子对象数量", "Info": "48"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54ad178955e2a4b43959e43b50701b4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14860/bullet_14860.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1087beeaaf3a83144b423c428dd83d71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14860/bullet_14860_fury.prefab"}, {"Title": "子对象数量", "Info": "35"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "359c8d37686cd6b48a57ce66e1340f45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14860/cannon_14860.prefab"}, {"Title": "子对象数量", "Info": "681"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5555b00a95fcafa488a42e305c1abc96", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14860/net_14860.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dfe2874a6a9d6eb47bab7c3f9e3c120e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/14860/net_14860_fury.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e1661a6d9bcfb4489b04c193682f66f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15002/bullet_15002.prefab"}, {"Title": "子对象数量", "Info": "40"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ea16bb81ac366949a67d539988815f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15002/bullet_15002_fury.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6cb29478764fbb443beea71d634d5dd9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15002/cannon_15002.prefab"}, {"Title": "子对象数量", "Info": "178"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a99827266f9ff3445bcf1113517c3ffd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15002/net_15002.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "add347d7e0c497847b4d71648bfe16c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15002/net_15002_fury.prefab"}, {"Title": "子对象数量", "Info": "53"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a010605b1824494ca7ec07c068f1e2a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007.prefab"}, {"Title": "子对象数量", "Info": "27"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3225f80720bc4841bdfe3ac5574c78d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007_fury.prefab"}, {"Title": "子对象数量", "Info": "111"}, {"Title": "粒子组件数量", "Info": "32"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "82e99671a32b575418a63c806ded3300", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007_fury_1.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44b70979ff51e514ab8b85b9910bd7df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007_fury_2.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "668150c430268834fafaa4bef648794a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007_fury_3.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acef101ca58474e45831b337f3c4d88f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/bullet_15007_fury_4.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1332fd77450d834b9c7aa2d1d8d53aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/cannon_15007.prefab"}, {"Title": "子对象数量", "Info": "175"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3f597ab215f9244190530efe81bde4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/net_15007.prefab"}, {"Title": "子对象数量", "Info": "30"}, {"Title": "粒子组件数量", "Info": "9"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "261b2a5ccce20b942a247f637cc6bbc6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/net_15007_fury.prefab"}, {"Title": "子对象数量", "Info": "36"}, {"Title": "粒子组件数量", "Info": "11"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9302c2f2fe2839347a961c93fb361522", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15007/net_15007_fury_1.prefab"}, {"Title": "子对象数量", "Info": "39"}, {"Title": "粒子组件数量", "Info": "12"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ffb082c3ab99ea9419f6705e788392ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15009/bullet_15009.prefab"}, {"Title": "子对象数量", "Info": "33"}, {"Title": "粒子组件数量", "Info": "10"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "51dc6fbe2dd22334caebba83d483ac52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15009/cannon_15009.prefab"}, {"Title": "子对象数量", "Info": "266"}, {"Title": "粒子组件数量", "Info": "38"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1d7507f473ae844da984f3851949576", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15009/net_15009.prefab"}, {"Title": "子对象数量", "Info": "21"}, {"Title": "粒子组件数量", "Info": "6"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a753e9c0d41f5348bf211eef4f4783c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15011/cannon_15011.prefab"}, {"Title": "子对象数量", "Info": "148"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1236669973c93a943b40e4ac3266b3e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15013/bullet_15013.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7f98126fe69601d46bd595bb601202eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15013/cannon_15013.prefab"}, {"Title": "子对象数量", "Info": "401"}, {"Title": "粒子组件数量", "Info": "67"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "55f6740393241174389c0dcc59ae7d15", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15013/cannon_15013_linshi.prefab"}, {"Title": "子对象数量", "Info": "401"}, {"Title": "粒子组件数量", "Info": "67"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7e780ccbb0c40e9449a65d48bd8a7566", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15013/net_15013.prefab"}, {"Title": "子对象数量", "Info": "61"}, {"Title": "粒子组件数量", "Info": "19"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b7907ace79b845429efa7097546d75a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15015/bullet_15015.prefab"}, {"Title": "子对象数量", "Info": "24"}, {"Title": "粒子组件数量", "Info": "7"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d4debe7373a04b248b4b267d7895ea86", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15015/cannon_15015.prefab"}, {"Title": "子对象数量", "Info": "321"}, {"Title": "粒子组件数量", "Info": "45"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9171cae0ae15d16458963b4b5c74c95d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15015/net_15015.prefab"}, {"Title": "子对象数量", "Info": "42"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a53165dbebb1a1a49a73f3ca688bd679", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15017/bullet_15017.prefab"}, {"Title": "子对象数量", "Info": "50"}, {"Title": "粒子组件数量", "Info": "8"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d3e01c9a14076140a681bf2ee4f8dc4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15017/cannon_15017.prefab"}, {"Title": "子对象数量", "Info": "262"}, {"Title": "粒子组件数量", "Info": "40"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03bc73d55ef603c4bac49a855ab7f63d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15017/net_15017.prefab"}, {"Title": "子对象数量", "Info": "47"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3934208bbbe8f5143b02482de9e77510", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15321/bullet_15321.prefab"}, {"Title": "子对象数量", "Info": "57"}, {"Title": "粒子组件数量", "Info": "15"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94471429242cfbf469eb3aa4dfea62b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15321/cannon_15321.prefab"}, {"Title": "子对象数量", "Info": "224"}, {"Title": "粒子组件数量", "Info": "34"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e0f081f3c0dca3341b9b736e77f05a2b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15321/net_15321.prefab"}, {"Title": "子对象数量", "Info": "77"}, {"Title": "粒子组件数量", "Info": "25"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7db563599a2702d4a82926e73827c17e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15324/bullet_15324.prefab"}, {"Title": "子对象数量", "Info": "55"}, {"Title": "粒子组件数量", "Info": "16"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f9af4f934a4a044aa0f6bffa05ec135", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15324/bullet_15324_fury.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "18"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b2a74c61079d994a843cc590fc01e5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15324/cannon_15324.prefab"}, {"Title": "子对象数量", "Info": "287"}, {"Title": "粒子组件数量", "Info": "32"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4dd71f3c8bbb89c42af4407ac5bfc531", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15324/net_15324.prefab"}, {"Title": "子对象数量", "Info": "45"}, {"Title": "粒子组件数量", "Info": "14"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c618b55015b521a4eb491a28d6f0d317", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15324/net_15324_fury.prefab"}, {"Title": "子对象数量", "Info": "54"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "850c3957d0451204a8cd30baca97d162", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15326/bullet_15326.prefab"}, {"Title": "子对象数量", "Info": "48"}, {"Title": "粒子组件数量", "Info": "13"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f6e303d144d98444d869f80c8edd59a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15326/cannon_15326.prefab"}, {"Title": "子对象数量", "Info": "221"}, {"Title": "粒子组件数量", "Info": "17"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "471355a2b0a83ce49a2a1b2c38b532af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15326/net_15326.prefab"}, {"Title": "子对象数量", "Info": "63"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbe526bc49b97a249b2339c0d01c7b48", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15328/cannon_15328.prefab"}, {"Title": "子对象数量", "Info": "124"}, {"Title": "粒子组件数量", "Info": "0"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d2378f689d04c4844a9862d5a487403d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15330/bullet_15330.prefab"}, {"Title": "子对象数量", "Info": "84"}, {"Title": "粒子组件数量", "Info": "21"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a55705224b7a4f4d9f214853b74b10f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15330/cannon_15330.prefab"}, {"Title": "子对象数量", "Info": "316"}, {"Title": "粒子组件数量", "Info": "49"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa1f7376b7574734ca85a09e80fe87fe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabMars/15330/net_15330.prefab"}, {"Title": "子对象数量", "Info": "62"}, {"Title": "粒子组件数量", "Info": "20"}, {"Title": "错误信息", "Info": ""}]}]}