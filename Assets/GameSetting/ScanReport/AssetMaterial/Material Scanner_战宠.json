{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetMaterialSchema", "ScannerGUID": "1d4dced6-1307-465c-a5d1-88380405203a", "ReportTitle": "扫描所有材质球", "ReportDesc": "规则介绍：检测材质球是否有冗余属性，以及引用了官方的标准着色器、URP/Lit 着色器", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "07a0af34c0992ca47b94c1220ae832d1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "17245536662c3fe43a5463debae1a553", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu01_diff_faxiang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ceab9fbc7593eef46bc92205868be563", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu01_new.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "70f26409c593f854793af736712f6c1c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu01_new_V3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d836f8df7b11be64e8f0805f2f910ded", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "49f273375ae8d674f88dff233f2ec0de", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu02_diff_faxiang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a9b2688ac84cdd64bb2d119ff69c1be8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu02_new.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "217f3954193401843a68afec3eb8c1fd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu02_new_v3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2c41d65da5de675438caee80ae77d25f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "13d290b87f53a29428e4dd98dc3394c8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu03_new.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9792e542ca9c954380e0ecbeb86d5fd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cb64144f997f9b74bbd2c619e09c4c00", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu04_faxiang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f9eff0722205ece489313c98dcb01b19", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu_diff_faxiang_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "23ce0cfd2990d4445a48f130f431360e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fish_1628_baihu_diff_faxiang_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c9f76ee399d5d541b6235fde50e6c3b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_banzi01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ab329634d05beb34381ad03527eb409c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_banzi02add.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ea5285e4431c69a428d17f9259e7fc89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_canliu_kuosan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a770889f554702640847adfc7571a80e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_daoguangwenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b164e098f43397b47be07d1bfa807917", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_heibai01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a3b63a591e793d489561df4fc74ce71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_kuosanbo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df44d82c8e1025d438b01c2522fc990b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_laohu.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c1e65ea4c25bf1409f8217a1a1792fb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_laohu_liuguang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1ccf67d1ac202c049a8ef934b92d9f94", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_mohu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "99d67c8cf88b9c0478ccc934a1aad881", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_pingmuxian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8a278c98c88b03941bac745eddf2b5d0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_shandiansi01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c8eb77c5fcf6535488405df0ffe07408", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_smoke01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f3c98018f65671d46a1a55bd084c517c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_suduxian02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ee9228abd639504e85eda48a607bded", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_suduxian05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "aa612102f1e29b04baf8520c08bbee74", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_suduxian06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a661f317aed7260499cf68606695018e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_suduxian07.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3a98268b51581e7489ce468f02d10e8c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_suduxian08.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e4fd5eaafcb53c43ad148f11fbc1cf1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_tuowei01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "84e79417a4ef87946bc38906e8e979fb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_wenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "823dff61c658e804699701b5239d260b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_wenzi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "55efea83ad3c3c346911218f619f7de1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_yanjingguang01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b671d8187c083ad40b5227f3ab4755bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_yunwen01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bf09ccd690aced54d9ae12a03c6fa722", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_yunwen02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3b1ab0299739a1a4b874ebd41971ba56", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_zhezhao01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "786080f8b36814c45b6cbd179f92efa9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihu_zhuaheng01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d1c7745b4992dfd44b55879ead9a75cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5f2aa9119bae97c43b6a870f439b6d3d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "72e450762d87c7f4e850787e58bf9459", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "817a6358b2e720a4780664ed23607108", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2fabc6b5792f115458f56a5b8a11ef76", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "48ae4e3b09dd8ed4993eb02eeb6cd5c0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7d24dfd79ab49be4bbc3965644acf09d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihutiankong06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e5627ca712451bc4099ba6e39c259b8c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihuzhua00.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d48b88975b98aa24f92267bb5db87a5d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihuzhua01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7e6da44fba6757346b7bad3d3b177679", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baihuzhua02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "43d0ec4e9330ac44381c4cc81828f4a1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian00.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1037bb0e1ee9e5c4bba2f742cd4615cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf9dcd0dcd1ea8c4cb0a2ea3dc56400d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d3bff31c55f61c344b18c1b03e52a3e9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1e773fba3e52f0e44ae0be7e9a2e8bd1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "62c0f0d73594212478fb4478243af1c1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandian05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ef7e17e5cc6f65944a8ee223485e65a0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_baohu_shandianliudong01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b78b5d7b61c6bd649bb67885c95f3472", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_bhsw_yanjing_zhexian01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d5266cd70160c9c468ef67687d4efaef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_canying_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acbda03c32a580d4a90fc2ac1976f847", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_canying_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb81597bd248d4a4dae4a159dc48690f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_shandianxian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "69b9b5ae893ec904997d9c31e5fa8195", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaheng05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3ccb71da814be83478d98fc4991f3650", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaheng06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d4dd2cd25d4fc2143ba2e9ef8ad6412a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaji_wenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8bcfe3cfe5e85974fb923ae0bd7cf94d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaji_wenli01_g.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9a8d1ac23a138ab40b46284ca2862c7d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaji_wenli02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1139fac671513d24aaa9e0f2ed9ae217", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaji_wenli03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3a5b41c5b53da224cb31f146213458ac", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhuaji_wenli04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "90613812251016442957a5327991a1bd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15301/Materials/fx_mat_zhu<PERSON>_shenwei_Outline_huo.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6fc1811ba9269074e88093a3e47cb2e3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_1629_zhuque_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "09b2296730907bc44a67f86c2a9985e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_1629_zhuque_beishi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a5a8cbeb6a978b44d9bcb70f36c3686c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_1629_zhuque_by_outline_huo.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "629bc3291bf71124385cf03523e5554f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_1629_zhuque_by_outline_huo02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3af5899a6f02afb419cf6b9107d1e870", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_1629_zhuque_gold.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8494ecf7166b59d468534273d50441e7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_15303_zhuque01_diff_faxiang_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "027dd8890e0f10343b23f885314b57bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fish_15303_zhuque02_diff_faxiang_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a1be1c2506423bf4eb19188f352b5129", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_canying01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bb15601bb90203b4c8180d7a638d806c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_kuosanwenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "32bb69132544c00418bdb26abdc88411", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_kuosanwenli02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "022ae34a75d2cfe4d9bcd5cd789450fa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_kuosanwenli04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a9f03c24f7d64594db612c118ba2f245", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_kuosanwenli05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb6c2b9cdb160b24fb457202aabce510", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_kuosanwenli06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a28ddf4358e46d74fad933525eba86c1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3c0e1f9fae823dd4a990f6e74ff0a61d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng01_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7026bf813c4411f419399e131a1797ae", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9255e4c8816a014ea68f54de850711f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6497db3bb4c4d974497c81eac789798a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ea145c9b56e10574098ac0330d790cec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9f295218e9c14344b659c8007ddad6f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d493a4dda1eaea540bb930cc2079de12", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng08.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a1eccc3a277614241abf3352af30d483", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_longjuanfeng09.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0db724d514ba08b4da66d657aaee850b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_path01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7d795a422f56aa64a896dc223c87584b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_path02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a7c025958b589e4f9dfb165f91be281", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_shanchi01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0e2d830c7cc4d634c8959d49c3dd5e37", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_shanchi02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5988d465108233547a1a5cc85161502b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9cdc7bfe78148474bbc40cc59357a93a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "25454b54d6356fc4b8d0b0c67df39977", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "28b1075060e5ebc4cbb9479c92054faa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5c61c3c89fa223b4f84d61ee9d8e2e46", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c0e9d2496e244564e8d7879a073fe12d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuque_tuowei06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "129ab02b20f5b9040833535339ed6146", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuuque_firequan01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1dcb2fc03401a9045b83ed7d18f37908", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15303/Materials/fx_mat_zhuuque_firequan02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "694e44e0dcd6b6540901cc904523dc41", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15305/Materials/fish_1628_baihu01_diff_faxiang_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ce4268dcf06434c4ea46023cfc12d05f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15305/Materials/fish_1628_baihu02_diff_faxiang_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9163bc00c71104a4bb22bd4625a21ad0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15305/Materials/fx_mat_zhuaji_wenli02_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c65bb9a2059336d48a2750104e6d5ade", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15306/Materials/fx_mat_zhuque_canying02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d7080c283ec58bb4d91f85af596e32ff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15306/Materials/fx_mat_zhuuque_firequan02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5c8eba320205ea04e865028af8bdade9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fish_1629_zhuque_beishi_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "67d33c6eba7c9a245879c04589f4251c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fish_1629_zhuque_glod_low_mod.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6de33ad23b223141a4c1525446da27e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fish_1629_zhuque_glod_low_mod_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d39d177251ce2644ba88701b77d0a91a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fish_1629_zhuque_low.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b399306d01d149244b1fb55cb24898f9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fish_1629_zhuque_low_ui.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "da8341145334fee4e893a4d92631c4c9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglong_path 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ee29b843411678046b0531f334c67044", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglong_path.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9db3211ec15dbc047bebb7616f1d96a6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_bei.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8e14c5119e1394546a756f0d608e1268", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_shandian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7946e5e51e9a2b3458c339cd6356cc9f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_shandian01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f43266ee429f9b74595dba7ff3aa659b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_shandian02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "457df207552ea8f4f9c0389123351f1b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "737bd48803b2c7b42a835626aa48a0e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e0a582a0cdbe7a46ba8a671c03612bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a9c12f56064df0d409b0c11ec7327ffc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail03_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f17e7ca70a820c444a69b50083957360", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail03_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "61b634d24529df24da669413e024600e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail03_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "27424fc08f02e6d4f9f3447579ce27ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a877fc7855936384c9bc453fd3b7060b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307/Materials/fx_mat_qinglongshenwei_trail05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "82af172d841ede54ca916024400948ca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307_qinglong/Materials/fish_1627_qinglong_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2902b4de75f150249b0b6a96d1f700b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307_qinglong/Materials/fish_1627_qinglong_diff_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3faca43524562043aa073edd423a588", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307_qinglong/Materials/fish_1627_qinglong_diff_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa37f6c03e268c94d95d2a9def998ef0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307_qinglong/Materials/fish_1627_qinglong_diff_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11f16120789896840bb607ebf04ada83", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15307_qinglong/Materials/fish_1627_qinglong_diff_04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dc19da9eadbb0b246a7f53719dbf2da5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/faxiang_01_hong 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b117c56a09a1f5346825a53ca22fd333", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/faxiang_02_hong 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3eae1f4bf4033f498bca87471aa59a6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/faxiang_03_hong 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f997526c5b2b8040a6d98d121fadea8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/faxiang_04_hong 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e7311f9d7ea750c43ba3b7aa119fce5f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/fish_1627_qinglong_diff_bianshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "12fc09ff778f6d24ba1e9e86c32c282c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/fx_mat_qinglong_shenwei_caizhi02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "733c822996dec394394620f66c55c038", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/fx_mat_qlsw_glow01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "90d598c01b0612f47aeb5db0feee2d82", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/fx_mat_qlsw_shandian01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "719a1dd4a4ff0a64e9dabe007274197c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Turret/PrefabArt/15309/Materials/fx_mat_qlsw_shandian02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "daa487161537edf4ca2c884ecaa70be3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/1628_bossshow_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "42f0b76968b921e468dbd52d1fc91459", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/boss_1628_ProjectorShadow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "06147b5f87f35334fb14173b92e94294", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/boss_1628_ProjectorShadow02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4f6ade019551df8428d30dd01d52991f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu01_01_bossshow_v3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cf66fe56ff6d7e54180de480a68052cc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu01_01_bossshow_v3_dark.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3bb2235ea4ef5404abdbac7df6bbc596", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu01_diff_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "07a95705957a4b64ea8a227319ccee43", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_01_bossshow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7d4ceba05e592b549960bc97de59ca61", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_01_bossshow_dark.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "516a4890af89666459cfb5a9b4b600b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_01_preview.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4ffacfd021da39d4d8c60ca5381db55f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_02_bossshow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3551c0048d5ad9f469888133f085bf80", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_03_bossshow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "656dc56e8af7a3848a03a49764db5256", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_03_bossshow_dark.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cb1c96e48d2dd634dada0a5fc00ed8aa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_03_bossshow_v3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0b97002dda83ce744bd07f0b0d5342bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_03_preview.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "306af5a1b8c10804988c0e666115dde7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_diff_level5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "be69f85dd8ba600498c93dca7131b468", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu02_diff_level5_cut.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a09eac1baa5b80b42bc93166b823869b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu03_bossshow_dark.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "11b8c0193e7906d439499d03101ef964", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/baihu/Materials/fish_1628_baihu04_diff_bossshow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ed416947ea403bc448cfff4acd394126", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "06f6705a0b73a18468cde7ac8a5dd943", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_01_hong.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "33d9047fe80a479478dbb5a610d43a10", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_02 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e820eadc3508ef04291441414b1bb50a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_02 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74c9560d767233940b02dbbcc414b7ea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c84579db028519240b007dba4d01d59a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_02_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5555b7614357145459f4339dd0dfc66d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_02_hong.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "adacd3c04491ce643903bb4bd8142308", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_03 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2086e994143718d409dc1c6646d2a1f6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_03 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "23276a8214273d5499432c737c5a1ba5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_03 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e0feab8fcde137438b8b8ca41914db0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b79daec364b1da54da3e1cb9b61cb270", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_03_hong.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "464a3cdf48cd5a548bf81a26e72930cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "38cb15031aacdb14693d26f205621850", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/faxiang_04_hong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5b499f49d99b05e4586c448fd7aa390e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "80e97e45cd02e2246b53349b618f983b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_01_jilifankui_v2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bf800295a2d4f194ca2eb8dbc49e31c6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a66e14e4d2884fa468435773d585057f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_02_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64b12345c8ed8134195682147b52196a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5441c9df2f5b67542b7ec5ca7ab1c3ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_03_jilifankui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e3e481e98c70b644bba050ca17739e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_03_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "404480715f37d51469e9a311814ba07d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_04 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2492a537d46d6d04c9d7cfa9cdde6eb9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1d0eb98f356be8f408422b65fa97f511", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_04_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83924e8b008f58843822d32f443c0af7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_faxiang_01 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1d6a3c9d8bd2fc47955ed1aa5941714", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_faxiang_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "099ba5357554f13478b02751ccb50fcb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_jinshu_custom.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e1de1e2e10f10d4d9e846d31b793f32", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_1627_qinglong_diff_jinshu_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "185ebca3b96573540961253e548c9893", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/fish_long_1627_effect_rim.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ac1376a5cc9683a4bb68268962566998", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/qinglong/Materials/skin_preview_View_qinglong_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8a7955dc24d06c4b9b2db1aa0c03b59", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_01_bossshow.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f449fd59ec1886d488fac3085d309e44", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_01_bossshow_bianshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c41a4969c768ead4d9a0616b1b66e73f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_01_bossshow_bianshen_faxiang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c7034598895f25344ad5de1e5f7d6302", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_01_bossshow_bianshen_fresnel.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a599197a656de8a498958e80e11de82d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_01_bossshow_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1796c08fd5210b74baa125eded2b8582", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_beishi_bossshow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d23b07c2edc4e443b05a82dce545b52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_gold_bossshow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "864c0210b54344240a57a04dc4dfafd3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/fish_1629_zhuque_piaodai_bossshow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "caeb643b8356dae419fb500c2e717979", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/TurretSystem/PrefabArt/zhuque/Materials/skin_preview_View_zhuque_bg.mat"}, {"Title": "错误信息", "Info": ""}]}]}