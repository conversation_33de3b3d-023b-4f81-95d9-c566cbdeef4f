{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetMaterialSchema", "ScannerGUID": "a46ab668-3ade-4f25-bfaf-8fe7081622d1", "ReportTitle": "扫描所有材质球", "ReportDesc": "规则介绍：检测材质球是否有冗余属性，以及引用了官方的标准着色器、URP/Lit 着色器", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "0586691faf4f9c445a135bb42fac5d4e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi01_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "971ee6445db39284b83fcbaa8b02237d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi02_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "16aeb53e5f5557e45b3d45db61c0523f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi03_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "807c02add6cdd86429a8cd81759a653d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi04_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5afb4fadcf261c94a99fb5743432afbb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi_yanqiu01_zhanshi_idle.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "78ec6aab7372e1e449c08b18af4fc99d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Materials/fish_1714_aiyagesi_yanqiu01_zhanshi_idle02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "43caa8cda5f9d9d429e6d5f5eea27190", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyage_dizuo_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8efba23de86217246b0a152a62e976cf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyagesi_zhanshen_beijing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6ebad5068869ead4e96ef038868d9760", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyagesi_zhanshen_beijing_bantou.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c7cb71158d4ad7746a2581490bcabcd7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyagesi_zhanshen_diban01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7bccfa202deb13f4283359232f4f35ca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyagesi_zhanshen_shizhu01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "99a550dcd33845448bb661748726e7c5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/aiyagesi_zhanshen_shizhu02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d7ef9b3d934499b40b6764fc286b1723", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/aiyagesi/Scene/Materials/touying_jianbian.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4c83b2ffa8fe1d540b7a874f07b66efd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8556010e789f695469969b705d89b660", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYang<PERSON>uo/Materials/zhanshen_baiyangzuo01_diff_huangjin_fish.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3d04f1e77eeb2e4a8ac96cf44e40f59", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fbbaa2391e9892d4a8b7a53fff547a1d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c3ebc4414c278e4498e7fde081014a4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bb14d06aa37720b46915d40492f80cb5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo04_diff_fish.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bfd9ebe4370efb54087b96d65c48574f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYang<PERSON>uo/Materials/zhanshen_baiyangzuo04_diff_fish_yangjiao.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f0f1aa6fc6792b44bb1b76724e521bb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/BaiYangZuo/Materials/zhanshen_baiyangzuo05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "84f54aaa86203634382bc0f13e608f95", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/CaiShen/Materials/caishen_body.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb8cad32a275bb040b3646327717f842", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/CaiShen/Materials/fish_yuanbao_001_BaseColor.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "88dd9cba27fff474c86cb4b91b6d3b91", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/caocao_bg2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "65709338f959d9c459ca59f3da39ad45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/caocaozhanshen_dizuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78489039bca7ec54b85351feefd0325a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/changjing/caocao_jianzhu_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4a7997e13785b2a4ebb493e340040a77", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/changjing/caocao_jianzhu_2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "70b245bb8c5123f4dac88b74cd10bee1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/changjing/zifaguang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ca32b2abe2330124c9c26ce98adb15f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/fish_1608_caocao_diff_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "30cbbacede4be254a8d2087414867050", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/fish_1608_caocao_diff_02_zhanshen_effect.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce66941b10531c745b7ca4022ab04c4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/fish_1608_caocao_diff_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c5c46ac3a34a7e4a9a5bfebb8045879", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Caocao/Materials/ProjectorShadow 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "414c5190af3ad2448851d72b6a3e835e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/bai01_noise03_10_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53e0d7ecf903aab4ba432d2842662ef2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/bai01_t_lx_zmz00_19_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b50b3654dd79c34daa89ce2525c9bf2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/beishi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05dd17041edc2554f8c73b9e7b3857c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/biankuang_zise_2_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2f66834f0f755a84ba79eb44984decd3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/biankuang_zise_2_chunvzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "33cfebffa38b5bc4cb7854eba407f3ce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/body_level_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93cd849f3a05d554e9ea0adb22b81c6f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/body_level_01_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6313643c81bc09c4cb0fdb7f2ece448f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/body_level_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "31e43b63a2af99742ac281b03c1cec95", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_canying_zhanshen_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25aac63216c00c945be8f869023354d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_canying_zhanshen_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64f93d0be04b14e458c58701b15e0bc6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_ba.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24bee4c8079a47541be6a1b5f7fd85c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_chi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f567045ac1191ec4f861afa9e8b34ca4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_kong.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25a5e05a5aed11b43bff3f57fd7b4a74", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_liang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "388add0aa0d053a4f8857e16e26d01ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_mei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d84a908ff332e534bb7e785e1990fd98", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_wang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a5b1188be4e1de4fb3695741227c8e3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/chunvzuo_zi_xie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b4e867a00be7db45b5829971c2e098b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/ef_xingkong_lc_01_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7a3f1a75f7ae03498cbf5120e9cb60a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/eye.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2494f79b400cc9e43b30589feb1518a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38ffbf061691c854ca7652465773eeb9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/face_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9c91fb758694a44b851d29a363c2474", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/fozhu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54707db1f1ec19e4a9530ba526d94d49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/hair.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b35f199193ce1a49ae44a8223b2599f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9aeb6132e27f424b8216655cd57fbb0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/sheshouzuo_trail_wenli001_cy_chunvzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81a2bdd4a9a3694458cba32b89d48f00", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/t_bullet151_mask01c_water_zmz03_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "021cced0b1191d24a8bf6d870025a791", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/t_smoke_zmz07alpha02_t_trail166_mask01_cy_1_chunvzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f6ae66d9a24c494b8b6ebfc4bdaeeb8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/t_smoke_zmz07alpha02_t_trail166_mask01_cy_1_chunvzuo_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b787423b1c7094345b64a6e5fa05c4aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/taizi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c26f21188317b97418c8c0b3e2831a76", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/tangka_chunvzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b96918026f2eca14e81515c4f7d92136", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/tooth.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "33369defb35129445b5ffbe8ffa053f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/tou_chunvzuo_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d1b70b9f9ed9c7419a6dd7c4e7b0aef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/wenli_zmz03_1_lizi_fenmo02_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7bca17ec216252e41a04b4ae9939f8de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/wuti_zhuzi_chunvzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "69a53162e7434ef4c9ae5c67c06cdfca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/xingkong_cy_002_1_noise03_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "428c87b6984d25d44afbab559db2f907", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/zhanshen_chunvzuo_08_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8811c782d407e6c44b2c107f1d7ef498", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/zhanshi_wenli1_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0db0754a06185944bcb3ad40e0baccf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ChuNvZuo/Materials/zilongtou_wenli1_chunvzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "87efde44b44aca041ab5d770a057075c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/daoshou_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f39c2ae21a5b6744bc50869f909a081", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/fish_1548_daoshou_mat001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "126fed13a4619ce42a26e0d1a8c2340c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/fish_1548_daoshou_mat001_near.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ea1f57c887e56b48abc425efac7f29e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/fish_1548_daoshou_mat001_tanchuang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fed71e8cae270094e9c5faaa354bb3ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/guangzhu01_b02_bai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "454d5899cc0ed1a4e8a17a9902879948", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_banqiu11_kj.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eec4284d9163e5548aef611f3f11b227", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_beijing_dian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e827de9c7b54bae4c8b1beca0ce67a97", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_beijingci01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43deb17952d89b341b290e12574109ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_dadaodan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0701ebade0f778d4e838451127e03229", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_guangci07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0fceec1380c90344c9e30a127b28bf32", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_guangxian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63dd5d305cd6c72479217c0b3a67b97c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_jinggao01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f52d5487bc0bf9b42b67a741099ae82f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_keji_quan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "426b9b1aa8a7dbb409586dbe955cfd08", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_kejihuan03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43b11e39744fe3f49b75e5e35069ecfc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_kj_shuzi01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d6c9473bbe300c45a2a0b02cc48d594", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_quanxi01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "851e87e42e92f754bbbd8564ae8cdaf7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_ring_keji01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0c7859f6cd89d34f9b3777620429c44", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_smoke06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b887f2d40ac5cb040afd4af958fe467e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/DaoShou/Materials/m_tuowei07_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acda80640d3825e49a7bc9103e065be0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/artifact_title_wenli12_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c0a0083972f7f5b4fb7abde090b05135", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/lx_zmz_shine01_light005_2c_cy1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f7ecdcdea85fc44a9a54aae11276f4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/lx_zmz_shine01_light005_2c_cy2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bae71a846c852ee4081e69084e1ae520", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/lx_zmz_shine01_t_bullet151_mask01__1_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b2ec2afa75655441ba7056a14d265d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/normal_glass01_lx_cy_shine01cube_1_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "423750813a5e82347ad807c98f1b1ee8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/obj_lock_zmz01_light005_2__1_dis_cy1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "30b767ee1c99c9d458f81197dcb26163", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/sanguo_shenqi_guanyu_dao_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b9c78b4591c8d1b41bc1b6ca134cb508", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/sanguo_shenqi_guanyu_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1f14654076ecd34ea94393f6ca7c48f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/shuyeshadow_guanyu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ccd353649e5bda84b8d6b0777865dbc0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/xingkong_shenqi_guanyu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d8b17d8f882c214f9e033459d0c58ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/xingkong_xingdian_color.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dc1f9bc348645a449a4f497747d6f8b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/xingkong_xingdian_cy1_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "676c8f81ba9422d48920375e6a5892b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/Materials/zilongtou_wenli12_2_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f142ae44a5b5ab049a47440d501c2d6d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/dizuo/Materials/S_floor_1_low_BaseColor 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2c00c39c9068064897acedc91bed198", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/dizuo/Materials/S_floor_1_low_BaseColor.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8eff0e6da5dd00f4fb7cf4066cdb643d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shu/Materials/PineNeedles_01_D 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c9037c860d095d4385536af4ba60824", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shu/Materials/PineNeedles_01_D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c41ab0dbcffa60e48bd320710cf5eca2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shu/Materials/RedLeaves_Tree_D 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f8cef9a275ed2c14ebf6659eac9362b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shu/Materials/RedLeaves_Tree_D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ecce366bc5ae684e87f5c8767322d3a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/shui/Materials/water_01_N.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e08c1d4160c0af84984264ed0c0425cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/taijie/Materials/Ladder_Diffuse_Tex.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ff5be434266a1e144826ba23b5176ce7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/Toro_Lantern_Mesh/Materials/Brick_Toro_Lantern_Diffuse_Tex 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "497126f89dc27d24fbcc6c8358611b49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/Toro_Lantern_Mesh/Materials/Brick_Toro_Lantern_Diffuse_Tex 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6cd55975bfc05dc4fa8a97c2b6d66ecf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/GuanYu/scene/Toro_Lantern_Mesh/Materials/Brick_Toro_Lantern_Diffuse_Tex.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29d3a61e1bd48b04196de32f4b9d1f04", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON>/Materials/blur_lq_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f85969b9c174a5f41889017fd21b11af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/effect_level_shine07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c7f0ed992f9e5448bac5f48e5732331", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/fish_1581_huangzhong_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0ffc87b0cf39be4e82379bc27fa5bac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/Materials/fish_1581_huangzhong_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05d34545176fd4542affa0d7b0ee7284", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/glow_jingtou02_a 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e997ddd0927eca4fb95a2ce7a0656fe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>/Materials/hz_jian_lq003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e0d3f0c916c837846a72bf8ac9f34143", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/m_baozhaxulie_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b346fe1264cb6449b0c7bdc117c762c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/m_guangzhu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "def69898516544e48a616a1de40ba2e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON><PERSON>/Materials/m_qiliang01_lq01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f30cb6b8467690d4c8903f9760fb120e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON><PERSON>/Materials/m_qiliang01_lq02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2866ef820b2b5c43913da356dd4d1a9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>/Materials/wenli_jingge_01_lq 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7429aa9398f5009438ec23bd70899c37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>/Materials/wenli_jingge_03_lq_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f8ab6d8476c32d14b8f0aa8d114c8e63", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON><PERSON>/Materials/wenli_kuosan_lq_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd5a06b45a2a217498d0904939339956", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/wuti_02_2x2_lq 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54a4f9182ffc65f489e2793bfdd66556", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON>hong/Materials/xingkong_lq_03_blend01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "04c1fe95b4bb952478570ab9016117d0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Huang<PERSON><PERSON>/Materials/yanhuo_001_add 1_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d0524ad0dc4275e419581059410aefb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/CJ_huangzhong_dizuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "876596f9aaef10e4fbcf5b1dc3095296", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/Ground_Concrete.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ea6447d5bc57753479be2466131ce5eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/Huangzhong_Ground_brick.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfb6e445dd0c18c44a11b6f95082fbb2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/jianbian_huangzhong 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7df749a8db1715488526d0c2c264160", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/jianbian_huangzhong 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1fa85da56191e44ca4a630a9ea5d195", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/lambert2SG_Base_Color _yoo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e82670488f5fc2147bed15c72d16dea0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/lambert2SG_Base_Color_tu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "128db3e436f7d4f4e9922c77a16aa949", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/zifaguang_dizuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d65dc9143877c04a8ebd20f42dfbea9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/HuangZhong/scene/Materials/zifaguang_gu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c7371437df6a0c44386de0939d56ccb5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/light001_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2dd1dc2aed5d1024c9aeb15dc9f8b741", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_bai01_t_lx_zmz00_5_lq3_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "03cf4e25ed973074db60420a63588a13", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_gp2_wuti_shitou_001_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0173eae8da14c3246a84a33a5e5dbf5a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_gp2_wuti_shitou_001_wenli_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "677f808d65d446c48ae0ee947986029f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_gp2_wuti_shitou_002_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7bb1234f74db4e44496703961e38cd73", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_gp2_wuti_shitou_002_wenli_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fa3594469e46f88488674738305a14b5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_jiang<PERSON>_jianshi_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9eb888fac85ffb7499c5c5f97769eead", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_jiang<PERSON>_jinjing_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "000ab4baa8b6c7449877d7fcb1992575", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_jiang<PERSON>_tiankong_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a1173230036052c4ca747c9219790c8f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_jiangwei_yuanjing_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a3910baede4f7d649971d6e9a2f93026", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_shangyun01_lq_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfc1a51c32e2ead4c9b1cf301881807b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_wenli_26_lq2_jiangwei_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "79c02d4380fb2704aabe2fa0b70a9690", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_wuti_sun01_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "045c59fe06421354d879147834fa3b5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/mat_xingkong_beijing_001_lq3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d40eabbd9931b4a4e84fe803aa9802a9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5e4dee4c906ee854a8b2c93cf1014a59", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "647eba0af0706524398417ed31757ddc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44f3b523720e6a345a296bcb89581645", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fed7068d53ebbe142b4164eae10d74e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d33b9727767e3040b667bd57ec28b7d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_01_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ccce024a1fa925444adf0412a587b0b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_02_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4a04392b091c60e4a963f8c5218153ac", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_02_3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3c06073ca96ce7a45afa893706463cea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiangwei_02_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d28a1206401791c4eb379fa636bd9dd5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiang<PERSON>_beijing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e24499acbc26c4f46b992b3a6ab466a8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/sanguo_shenqi_jiang<PERSON>_beijing02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8f314f144af79974e8fb72920664b51a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiangWei/Materials/wenli_jingge_01_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4527ce95ad809e4babb4a568800de5f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/changjing/c09-220419_Base_Color.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a4fa711a00aaeca42b402a035b2f1dc0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/changjing/jixu_shitou_8.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8ef8588c1a34b574eac92e20619b3e03", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/changjing/shense.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ba0e59ecbe8291f4691132feff78805b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/changjing/t_chaotic_skies_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7ce0d227c9b69534a8e6a961a38d1989", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu01_diff_paotai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2182d5c6a18237b41be658ebe677ae3f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu01_diff_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4ed20d6ccf4060a41a4b0b266345b80c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu01_diff_zhanshen_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8dc83ee538021143a1fb832cbb2f552", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu02_diff_paotai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3bef301cf90c02847a616d1e4b5285dd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu02_diff_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "022d8067ab3d2c24fa0afd6b20b8f9de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/fish_1610_jiaxu02_diff_zhanshen_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a81a8a4b0596c8e40916c07c2e246a05", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/Materials/jiaxu_bg.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf2e71a5611667f4f9bdd62806f63adb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/Changjing_JX_suolian/Materials/Changjing_JX_suolian_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "85f3c2938f2c54149bb59c02da06f6ec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/Changjing_JX_zhuzi/Materials/Changjing_JX_zhuzi_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a4ee16ad31684b74697c4bcefbd40ef0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/jiaxuzhanshen_dizuo/Materials/jiaxuzhanshen_dizuo_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fdeb9d19f1ec757479cf7b50c0df4a71", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/water/Lava_3_flow 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "87aafd22786cc3b4d949192e17a5cd42", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JiaXu/scene/water/Lava_3_flow 3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "78de049d64da67940b9271250281f80f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/beijingtu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "749bf728de514e4459c4a99f28b783cc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/ProjectorShadow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e9eef67fc3aa5274b9ce885739b937c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiuZuo/Materials/ProjectorShadow02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e116aa6422f78c4cb50e7bf5e3f78b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo01_diff_baijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4aaf0987e9f7b204ebae94d5b89c3834", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo01_diff_baijin_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cc99667f03c4af548bad2ad88f4afb79", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "060ed0dbb8ed45948b90cdf973331798", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo01_diff_huangjin_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f1b5a191977d1b94a9231b67851f4f50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo01_diff_huangjin_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0d08df353156d844cb9572465fdfd443", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo01_diff_kuzi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "974508b98df6c5542849a7a64e47ea8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eaf6095a049c60d4fa99c4af46d6429e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d83ab85a38cfc2469db049346cad1a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo03_diff_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d288cb5e977f20f42a0c91483e64e714", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo03_diff_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "46225fc99c3571346ad45c32c388abb2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "afe2413520db2864193ef11c3fa7c9db", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo04_diff_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "88f719c98a896d144baf75bce9c0fc67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>/Materials/zhanshen_jinniuzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7936b71cbc966e64683eef2d43031fba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo05_diff_jilifankui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "55d9e9e38aa9cdf448b73ed19f7dc956", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jinzhu/Materials/fish_jinzhu_001_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c4d7c64bdb2886748acabd0e09c413b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/jinzhu/Materials/jinzhuscene.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6eb5dae1bb6489843bc92ba058bd6238", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/body_zhanshen_V1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8773d61192dbc84980ec3dc777dbf0d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/body_zhanshen_V1_zhanshi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4d830b3a2b76cf94cbe164f78f879250", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/body_zhanshen_V2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ee193395f9e0a546a31dd24da621c8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/boom_glow_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3195e14b4f2a824498cc2348b0985ec9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/box_effect_box.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59aab970fd432af4282cfeac73736a20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/eye.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d753b59fa52fe14694c0834e43bbecf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c7689ffca1cfda941b4fb33699aeff1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/Hair.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "652359d2257d7944eb0b9f59db0c7c92", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/huan014_g_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01c92b125e7414d4ebb3678ad747ee5c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/jb_zmz02_3_t_lx_zmz02_juxie6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "226665746388dc4498b48627cb686826", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/jb_zmz02_3_t_lx_zmz02_juxie6_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f59fc371e18cf94da7f719c73645306", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/jb_zmz02_3_t_lx_zmz02_juxie6_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5e936b93feca7754eaac2e72b77643b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/m_heidong01_juxie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "402408165b0494746b40b516e4c618ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/m_heidong02_liziliang_juxie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "857c9c4fc2f4d384f8636debc62fb763", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "689e440b153e3244aae647b4f0f33761", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/suduxian_cy_001_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "681a0dde7c1634a439456a00b9ea368e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07_glow_017_juxie1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3235e6cd21834494cb6621e29ce1220b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07_glow_017_juxie2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e150d201911f07488d0d8a33463e698", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07_glow_017_juxie3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97b3bc3271299274bb6b7793acd2c3f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07_glow_017_juxie4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "755081d16d9d17146949edacf0440e7f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07_glow_017_juxie4_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65a9d45a98d3c3a4d94b9cc8b774ca8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/t_smoke_zmz07alpha02_t_trail166_mask01_cy_1_juxie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbfb38263804247478bc169577afda7b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/xingkong_lc_01_blend_juxie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ec3e8d6e7f2c4c4c8bfed0f6228e8dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/yachi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abeaba12bd47bdd4fb07f541223488a9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/JuXieZuo/Materials/zhanshen_juxiezuo_beishi_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03087bf87bdaeb543a33ec52b205e5a9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fish_1716_ladamandisi01_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ef3e99abd23330f42994116704c7c919", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fish_1716_ladamandisi02_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bd95cbd8b821b4f4c88098b62ef0f314", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fish_1716_ladamandisi03_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6b296153f5b267644a2abe893fb01fc9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_da.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5bf7997c97a13894fa54aa9644c81d74", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_da_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "be141602b13a73446a6860a030ea0410", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_jie.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c7798428247e8834d9c40c25390908af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_jie_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1f3aa46650db7d458ae255cd43ba045", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_jing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3f93f5d27d7b7404ba8d2e466c3e3163", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_jing_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05173048c3b8cae428ced2411f8b0dc8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_zui.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "efd467fba04e7e74db145f166cae6fc4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_1716_ldmds_zi_zui_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5cbeaa9d821108749926de674d6cddd0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_gp30_ldmds_xingtu_001.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1c5795acfeaaf5a429a9400d0582f868", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_gp30_ldmds_xingtu_001_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b3eeae87d6d310840a9f5118b91be7f4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_gp30_ldmds_xingtu_002.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3a6487086ea1cca49b5af70994fe5bc6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LaDaManDiSi/Materials/fx_mat_ldmds_xingtu_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76ab8d822d49f4844bb4deebb2400f81", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Anim/m_cloud04_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "16e50c13c1ef8604c985b37163334de1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/baiyangzuo_wenli_29_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "550b9497ab1ada54dabc1ccfa432afc5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/baiyangzuo_wenli_29_lijing_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d5a72c8d7fe62da4a8e70e18e9f32387", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/egg_guangzhu_waisu_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b79e55e319cee154d95e461c759a68f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/egg_guangzhu_waisu_lijing_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d2dd51f9d6ccee245aac8a8b87595b89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a71dc91781658e42b28b961888ffbc4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing01_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab99a34e6234ccd4ea801a116555ea14", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5007af8a0107dc349bb2defa40bf0706", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "725b8be56c893054b85e372399f7a981", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing03_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a957bd47a8e14b746abf5a0f5db87f1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing_diff_faxiang 02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc2bbbe63e4cd0468aa915582d16acf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/fish_1587_lijing_diff_faxiang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ac44e666c2a000647ace24cf7327bdfb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/jinniuzuo_wenli_5_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7265e192e4c5b64f9747f59543cf59d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/jinniuzuo_wenli_5_lijing_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "42bd89febc4af96408f0832d74b4b17c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/jinniuzu<PERSON>_wenli_5_lijing_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ffb87f52e68fbdb4c93662862c994326", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/jinniuzu<PERSON>_wenli_5_lijing_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "48cbedafc1033b2438714b66b612a0c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lijing_Scene.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d59ea8d7c77e234e8b9956489194203", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lijing_ta_diff_effect.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "88d8d82dfa73ef94188fe614131217fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lijing_ta_diff_effect_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a2c72576e7865445b2bcadce25d451d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lx_cloud_zmz00_1_light005_2_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cb3c5176f3f3ae745ba75bf75bf1f25c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lx_cloud_zmz00__4_lijing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "864d7d89c3952cb4789090422bc5b4dc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/lx_cloud_zmz00__5_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59309a47020a47e46b5175b674ab8bf1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/t_smoke_zmz07_glow_017_lijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "181f9a8255889344abd8964e8637900a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiJing/Materials/t_smoke_zmz07_glow_017_lijing_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "304fefa28bb485a4fb396b8ce0202f71", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/fish_liubei02_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3aba130967e25c14680600b1fd43f484", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/fish_liubei02_diff_pifeng_zhanshi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e300e9aec706d9f498936a7aa50b7031", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/fish_liubei02_diff_weapon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80c6cc2ca7984c147ba1e125e17537a8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_159_4cheng6_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "980507766bd67df4a9b96a6b07fecb5f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_baiyang<PERSON><PERSON>_wenli_25_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3cfd142f7dc66044ca6cc0573be28926", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_blur_cy_001_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5523f837225e6a94ca1eb5185be5c60e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_chongcipath_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6546db27b41c9941a7016a58613a1dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_chuchang_bai_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e3316473beb5a04185c40778af0b2b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_fangsheglow_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "649a42e763cb26e41a44a1b82af97237", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_glow_shine001_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "685da70b078413347b8d823f23839866", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_heibaishan_common_002_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b646823e26f40e42a5285cb28056742", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_heiwushi_42_path_013_r_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "82b77e3e3e322db409fa6437776d8c94", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_huan0_liubei_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1bef2ba0b0c60145aa0934149f2f2d6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_jiguang_zmz06_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "094af03093b798d438aba58a8e4a83f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_jiguang_zmz08_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96ef73238a604614c90c3c34daf1e417", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_jiguang_zmz010_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5d304a6581a12264d935cd14d9458f54", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_jiguang_zmz011_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "155c12a37a0546b49999d534282d523b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_banguang_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7396248771b84644884f60d1817c8395", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_daoguang2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7031d404bfa578f45a178d0084b6c3b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_huoyan_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d88c72453c6b644cb8fbf3a82c65b24", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_jianying2_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "862f89d592c38da43964f7607f253767", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_jianying4_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ca249f85ca7b2714e961d1461a887e25", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_jianying_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4b84c07217f4f5942a9f49cf0634222c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_wenli2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22b53adbe82a57e4b9d9be1d7ce4d872", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_wenli3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09ff94862beba9146a7a7d861001d727", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_xingkong2_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "11e67b656b3a4224a87b187dd3e4e621", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_xingkong3_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "69d69b608c1d82542a9604a1b554624f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubei_xingkong_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "73c728261d0e25d41b51a2cff389365b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeilizi_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "82e85b35296cc2343ae4e775be49d72f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeiwenli_8_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "074a4005b1d2de4469dee942a8bde84b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeiwenli_9_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0825149f615cd54458bee684fa15672a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeiwenli_10_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c23b69dc65683e48aff63200768c4df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b12921498e411c540b5c91f07e88fc50", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5bed4f24a8fb36b43857c2d457c7b422", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo4_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70dbbaf5f5de7e04299fc72dd267a5e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo5_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e3df08781633b946abcaf5938d22d13", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo6_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9eacb63fd22844f4eb761b184d18b670", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo7_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab36d18dee1f63247a69ac8e21a70270", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_liubeixulie_yanhuo8_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "48a8a3538d33fbe429131153b3a1f183", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_m_xingkong06_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22101e689b183fd44a878c128f7b30da", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_path017_mo2_wenli1_cy_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3f64a0be291b594ebd466f6a2db480c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_path_liubei_016_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "095154b77790f354fab44a3dc27e9bac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_quan_002_nq01_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81cd1bf38ce2c234697587d8612dccd4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_quan_002_nq02_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25dde2ea8c2841e43b735c909c937e23", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_quan_002_nq03_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b0b39ca53de4cc419eaa69aa286b70c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_quan_002_nq04_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab24aef0b4c7e974aa282caa021b8882", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_shanguang_lc_02_add3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "06581498712a8444f8d4ae019c60d9a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_tuowei04_cy_2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "605a4946494d26d488c7be96945380d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_tuowei04_cy_22_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5e44a4f8428ea104087364a06330aa93", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_xingkong_005_ld_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "538c4c7115e47384c90cdc25337c5d2f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/mat_xingkong_liubei_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "69e68bebabdfe5c46b89d9e3f146041c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/sanguo_shenqi_liubei_23.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db458139f32976a40a5822ca6fb81028", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/sanguo_shenqi_liubei_feiji.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1e31bd9c89cd57346a72bc17e033f914", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/sanguo_shenqi_liubei_feiji2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ce2ab43317fb02840abb1c4a9adb9fe0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LiuBei/Materials/sanguo_shenqi_liubei_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "50f2acf31bdb29a4cb8faa7b68ab45ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Material/<PERSON>_<PERSON><PERSON><PERSON>_Huang_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03256984aaf691e4da0d4e042d82fc6e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Material/M_Yun_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ef3663b049b3504486318c6c3bb13ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Material/M_Yun_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a7e7b5369026a854885a3445a76582e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Material/MI_Props1_Starbase 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000002514395435677365702", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Material/MI_Props1_Starbase.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c5c38456c9ec6fb4d9d619e9faa8cb37", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/changjing/Model/Materials/MI_Props1_Starbase.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5c0a343a419390441be7fd11272fd637", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/1580_cannonskin_wenli4_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e6a29900b00bba41b75fdde4aba3ca2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/1580_cannonskin_wenli6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "822dfd8a7a3b3914eb46c72ccb2a9b04", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/bai01_noise03_26_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f3663b7e9706204ab51a7d15e0803b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/bullet_173_trail1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0fa44928a5a0590499fcdc8ba3f06288", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/fish_1580_lvbu_diff_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "450b94b191a25fe449e42327070e45be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/fish_1580_lvbu_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e8b3971c467c5d4685e1e4bf4a80faa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/fish_long_1539_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59b3ec04db602c546b258893d4965eb4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/flow_02_light005_4__3_cy2_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b9c3a13a7b6501b4aac674eb339af124", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/glow_011.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0dabbb03ff35cf94798dff1188099f67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/huolong_bullet_wenli_005_cy1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "097cfb4500d279b48b1a6b5ff4cac0bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/jb_zmz02_3_t_lx_zmz02_cy2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "26dc5c4a6d2b1564db152d3a720f3641", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/jb_zmz02_3_t_lx_zmz02_cy3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "110967e825fec8b418b8bc9c001cd688", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/light005_6_lightning_zmz03_cy1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6c11be8ea052814ba6d4eb2888cd300", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/path_012_cy_wenli003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4478c0eab4352744db26ac14154435ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/shandian_xiao_256_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ace4ea0944ea834bba76c76862ac88c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/LvBu/Materials/wenli_jingge_03_cy7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec65db4cb1030d748a9a1d1ca49b63c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/bai01_t_lx_zmz00_18.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "68c325cd94d083740a2a80348fdcb781", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/bullet_147_tuowei04_cy_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "15508b099015ddd49970f7810ee773c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/dilie_lc_01_noise05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ceb94b7994267c4e9f04a201b94ad36", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_004.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17885893d6a41944bb9815f8969caf69", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_005.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "272dd350712a9b84d8e6a5395b4549c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_0044.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "14e986f9cc48df4439af5303a6406e88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_0045.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7bb9c29c2147274a9c67067b2f0905f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_0046.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "309fcbf9f78cb63408127d4c45cb91e3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_0062_mat_0048.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ae1b1a3179aba641b8beb3e1134bf45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_fh_0003_46.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "709e34d73bded70429068d17f84ade87", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_shuijin_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f8872454f750874095e759c0b2173c4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_smoke_0206.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00127b637d46bf344ba9903ded5ecd56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_smoke_0206_a.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c2b7f8d266e9664ca28dea8303e2601", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_smoke_0207.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5fd270a637f70044380765e166ec3934", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_wenli_0212_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b925d1399b80ba45950c9d999605f72", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/ef_mat_wenli_0216.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4cd7fca0850980540b67a28b9910b627", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/fantianyin_mask5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "936a8fa645549a44e8bdd6468b3ac2b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/guangzhu03_g2_t_lx_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0f4821365472e0a4b85769a6aee2e8c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/guangzhu03_g2_t_lx_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2aeecf3ca545c6a4eb68c1a164218875", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/juxie_canying02_wenli025.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "48d51e77ed1f79a4bacc0a5bc311017a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/juxie_canying02_wenli026.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "60ca59bbf94372c4d8e253fbdea59f2f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/juxie_canying02_wenli027.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11e28103bda5ebb409b978bccf2e8ab0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/lightning_zmz03_lx_zmz_shine03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e5b179ddca22be64486781f039173715", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/lightning_zmz03_lx_zmz_shine06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c9a7e6a0d061ae9429630b3e722e593f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/lightning_zmz03_lx_zmz_shine07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "62e7a2a8be6cb9543ac1cbe8c183f172", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/lightning_zmz03_lx_zmz_shine08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03a9745d975dce942aba48d197eb2bb8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/lx_zmz_shine01_fazheng_lc_11_2_ui 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "af553f79faf78f04695b8b3ac511e853", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/mask_fenghuang_fire02_noise07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9afdf775a0457794fbbf7086b8bf344c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/net_167_fury_glow2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "723a6e58a8656a04c8c9c2108d2c967d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/obj_fire_zmz02_wenli024_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4636e8b8d44024c4eabcb63f90cef6c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/obj_lock_zmz01_light005_2__1_dis 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "193a60fd1b147bb44bccc604847de1ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/path017_mo2_wenli1_cy 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b2440fdd4f25cc4e98993f437ea2a9c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/path_005_g 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db5c77d849589f840a25258cbe618779", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/PineNeedles_01_D 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab888a53bfc8ed64bb922cf518a58931", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/sanguo_shenqi_machao_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd807d32417a3bf4b91f2954727c9137", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/sanguo_shenqi_machao_diff_wuqi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a7b08b228feee0c4fa6798006e83fef8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/shadow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7b489596a5d49a43be604fd79b238b6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/shuyeshadow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64beceecca39f0645be52fe7c31c5c51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/t_smoke_zmz07_eff_tex_glow_1021.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d5c388648dbbc84c8de4a2ce23c9a6c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Materials/xl_bullet150_zmz_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03117bd07b6d598449a27848af969c49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Floor01.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02c14846110b4c54cbedb2e017f7f896", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Roof01.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83ae33fb77f9c26419bf0a7bc6452dad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Roof02.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f0f51a58bcfa49d44ae2485f3a8c9bdc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Stair01.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "903ba55991fd3d340a52b2a350dcd04d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Wall02.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec90aa766bef86c45b1425d1d792e3aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Wall03.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "775dd9a42974cdf45b6ed046a365764c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/bulid/M_Asia_Wall04.002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7f6f83731372b9e4e91a13dcacd1a9b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/ditai/Materials/T_Asia_MainDoor_D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37e134797aa423843998ea0f8743732c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Ma<PERSON>hao/Scene/ditai/Materials/Tex_Cliff__ALB 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ba631d5d44f16a4eb2169a1f49b1c15", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Ma<PERSON>hao/Scene/ditai/Materials/Tex_Cliff__ALB.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8f249b08cbe0654bbec4d6413054576", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/shitou/Materials/T_IMG_2681_D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c4eb5542a34a9d049b5fd52c34b76194", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/Sky/Materials/ding.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1d067e7bcbf1cf147ad81e283570c06e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MaChao/Scene/Sky/Materials/huan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ebbceb8fdb4321c4fb26ede9ffb23697", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi01 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8b1ec687353cae84181279e30fa8b775", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi02 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0331ad10df338f846959750dada0b1bc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi03 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "190a3dad9990f274db610df0df63215c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi04 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5738738c6b3d3874e9f86405446dc2a2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi04_effect_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "090e6e1ca0cecfe46a8cd88f2105b7cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MiNuoSi/Materials/fish_1715_minuosi04_effect_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d5b03044ef2103149a011fc9d5fd8cd7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/baiyangzuo_wenli_25_lq8_3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "456cb95bfa9a4c448b6de4e0fc80349a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>uo/Materials/ef_mat_glow_049_03 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "67f98189ab817b1478851522f4bb31e4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>uo/Materials/ef_tex_yuelaing_003_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7fbc9f55d49d7c44ebfde1ac8d58fe06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>uo/Materials/m_banqiu07_mojiezuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fe19286680dedd14f86a1c5e142df3f2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/wuti_yueliang_hong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0ab710e9c9cfe7e4c805db8f9248187b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo/Materials/xingkong_lq_05_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0e21b739cf68a244acaa8b5f5ac693b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo/Materials/xingkong_lq_05_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a30769e83df6a434d9344166a34fecb5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "97eaece6e666d324c982c518f60fe6cf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7147003ac171f184c87743901475ebb3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "87899da212e54214085141d993653089", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "df9652c201f89e84c9a6e1e3c0c30cee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7d58798b111f1fa44b70a8f30ac95a24", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c781031eac3a6a540aedfa054aba5f95", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJie<PERSON>/Materials/zhanshen_mojiezuo_05_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "33c33e6da18cd134f85b7b38ca82090d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/ProjectorShadow 3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "75b5c3b7867aab8438b6c58dff2b5aa5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/sds_bg.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "df16af96ff138b34588d1b616f32b89e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_01_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "073c0df522b66bc43b00c738d814e74f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_02_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf42c0a8f1d85fd47be276b829ae26fc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_03_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8cab2ed74edf3384c9d3dc633776f714", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_04_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "79ccd6f19a1413148801033925b044f1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_05_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5f181dec5b7ed0c449cdb22a7bcca27c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/MoJieZuo2/Materials/zhanshen_mojiezuo_06_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6efac95d6f2d2484e9eb76b0e0d3b7c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/fish_shj_shenv_003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38efa77217500e74d91d2687e17481ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/fish_shj_shenv_004 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2ef4c5085d7a2540b1283a57dcba16a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/fish_shj_shenv_004.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d70ccf8a824b624fb5e73fa1d03930f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/fish_shj_shenv_004_wuqi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02ba587afa96f2547b04ce2d2cfba82e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_baozhaqiu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13d6d609b2c78fc43b1840c03c9a6b69", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_dm_wenli01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "153e89bd6e24aa84cb372fdf634bb9af", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_dm_wenli02_shang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "409d44ced7960d742882c1801377c6ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_dm_wenli03_once.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "953f24ce3a73f1a41920d325d73f6093", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_glow34.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d086b652b838e584087dce8531361f17", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_glow_xian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "399e7f5838ab244478c1d45843b51806", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_guangci03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cf54885638430e14bb2c9374637f4372", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_heibeijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ad82f684a9471c48be368a98a1cd741", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_kuosan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "061be0386d072e04f83e9ea1e6408a1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_kuosan_a.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a71d7175d9da5de479959cedf4867007", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_kuosan_a_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb84573eefcccc445a1abc8eeb65e570", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_kuosan_bao.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78eb3d2d37d842e4a8cd4012ac6086e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_kuosanhuan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a4564e2336b47b549b2bebc676f48fc4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_neijuhuan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c431f7d2cd71e96428424cbfd2df0bbc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_neijuhuan03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8a325cc6b6e53904cafa3c93538100d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_piaodai01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d1658c4d198a5b44193d461e460a62b5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_ring01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8abad2720cbb04e41854c87bbbbe79b7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_ring_zhuan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fc707011230e63d4b9f84536922934b4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xingpan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "260c33815f7eeab4ba19a702278fe088", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xingpan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8be1af63aaf6a974d9b7a35850d55386", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xishou01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "31a124f9366ee7c4d97f7d22470dc758", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xishou_fenwei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "258001cf18019ae42821f3a735498e20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xk_tiaodai02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00ed469ae60a798408b6f26f194e64bd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xuanfeng_da.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8b936306bb2aec48959e2ee4a772578", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xuanfeng_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00f241c08d328fc4a8f4ad37e185a8a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xuanfeng_xiao.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "951a2eec3abb6754d8d8867d2bcc9270", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/NvWa/Materials/m_xuanfeng_xiao2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed4e949e598308d4a93db130c0f5f5cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/1580_cannonskin_wenli5_2_13.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "81b142c22ced3b743bf7a50e5ce79463", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/baiyangzuo_wenli_14_tianchengzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "31ec77694f759c14da7b4dfa15c1ed4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/baiyangzuo_wenli_166.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b5673b3f439a284eb18e51f01c3f70b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/cj_tanxizhiqiang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6705e42a8351ce14d9060039a7c823cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/guang005_Noise10_jiulianbaodeng2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7f560e01e41fd3249a165d9fb6f5c558", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/heibaishan_common_001_heijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3fc0e4062214f2c4880679cab3abb28c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d1c38e5ae4aaaba48b8e138c707ac7c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_01_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bb79a307917cdc24d889d01ea6e76cd0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0b70ed1335f043449b100df444d11402", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b1ed814fc6136bb48b3883e4744f7729", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e18cdef227118ac449ed8f738c7d2a32", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2355118bf12320340ad0dcdff0708099", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e5ddb521f1551b45ba3beab0d81f8b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sheshouzuo_jlfk_awake01_07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40fc2b97289564a4299a50a20e62234d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/sky_yuncheng04_6_lq4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6eb156759442aac438436209feaaf3f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/suduxian_lc_03_glow_lc_07_lq1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab1780566709f6a45a60b77cf85764fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/t_smoke_zmz07_glow_017_shizi_2_lq1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bf64a55104474c149a0a2583f3e7701d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/wuti_zhuzi_lq01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cc5e5949921663b4ea945ebbff32d6b7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/wuti_zhuzi_lq02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c79398d7a1877ae4995b16f9ce4f6506", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/xingzuo_sheshou01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c5d1cfbb0db6c5048ae6507fffc0517e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/xulie_fire016_6x6_a.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8831bc539ce56954e805f6d48fe17020", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_01baoxiang_diff_new.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d7854e5f5ea940149966cf41a79d76c4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_02renma_diff_new.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8abf4338adcfc1c4e9b32d977af1c7df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_chibang_diff01_yuwei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8cc8e3beaeac53a44adb7e1faea3eb08", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON><PERSON>_gongjian_diffbaijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ddba2429dd6e5b4299cd144e5c66675", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON><PERSON>_jili_gongjian_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c3a246a3252544468688571ef7eab9e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON><PERSON>_jili_gongjian_diff_v2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6fa275bdd86aca74487714fb5338da30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON><PERSON>_jili_jian_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c25e904a032a54e4cb52fc5ec99cbc6b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON>uo_jili_xiangjia_diff01_v2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8144cd07bfc869f4390ce8a6aa07f8d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShou<PERSON>uo_jili_xiangjia_diff01_v2_tui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6e3a9fd41853f4946b2096dd0aa33017", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_pifu_diff01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3fe8cdaf42128554d9d110101c4f241e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_pifu_diff03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8e13f0b91946534dbccbae4611528dc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_pifu_diff_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cbf998893809ad548bf4f8246582b0eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_toufa_diff01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab83f1949d51965488c5de6a321b9b47", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_yanjing_diff01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a4a9b37742f1c24d82dcbdeaa4d3afa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_zhanshen_sheshouzuo2_01_diffBaijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eba9af07b4a40bb43a5bfbeaf11849be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SheShouZuo/Materials/zhanshen_SheShouZuo_zhanshen_sheshouzuo2_02_diffBaijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64c48c32fee8fe248a413393106a9f88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/bai01_wenli021_2_shizi1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f2c5a4f41b235d4e908fc7c9364f443", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Shi<PERSON>i<PERSON>uo/Materials/beishi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1da76507bd2d0ad40a459de98e381d16", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/body.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "da0209a25235e6046b7cc98b1f1bfd5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/bodyV2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e2c081b28725b846ac8b93268d9d998", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/eye.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f5946818fc30f724f9c475cd82fd18bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4754aab21ef41dd468409e57602019ce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Shi<PERSON>iZuo/Materials/hair.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ae201d0438520a044ad4f61db99f0287", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/lightning_lion01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "181769fac44c96c4eb5e13e659dffa8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/m_path01_kj_shizi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07c0b6da87401844c845cd881e4507b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/niuqu_cy_shizi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "314473ed6af43284099b10bc87a84952", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "504339103003df242a2ac5183f4d662e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Shi<PERSON>iZuo/Materials/shizizuo_canying_003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "019950dfc7881be418950d2b1433f5d6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Shi<PERSON>iZuo/Materials/shizizuo_canying_003_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa84186e2373d604ab64ba95ff00e6d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/zhanshen_shizizuo_01_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "28b1a1a996b4c0247bba11a9cec1e650", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/zhanshen_shizizuo_02_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e71a16acefaf9a44eb0165624ddbdbed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/zhanshen_shizizuo_03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa378b53642f9bb4d9423e2f066980b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShiZiZuo/Materials/zhanshen_shizizuo_04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1d38f72cca37081448947ed4e9f3f351", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_meiguihua_wenli01_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4592233f6dab1294a9e0992516c1cba0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_RadialBlur01_shuangyu_lq_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed25b5528893fc3469b8d14f956965ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_shuangyuzuo_yu02_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f075ddf3f5d9814cbb8d0b4edcd9dac", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYu<PERSON>uo/Materials/mat_shuangyuzuo_yz01_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4b331c50f998ce54fa3bb613c49e3f69", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_shuangyuzuo_yz02_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "18b6a5911780f34448c3877599c30f9a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_shuangyuzuo_yz03_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4f960e525482e244fa2cd828c51aea57", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_xingqiu_001_lq1_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c52aebf0c4439c7439ffe321baa20670", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/mat_xingqiu_posui_lq_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25f596b927fe1174cac9126a9ffdb461", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "22226d8482121254ea348505cc0660cc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cfc12d76d12833b48b5be0b416343c0e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo01_diff_huangjin_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c450fa930b463464ebfcc63a03185f56", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo01_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "17f44555431123a46af9dcf0b7a4fefc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo01_diff_pifeng_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "681b931904fd5db43bfdba3921041527", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "016a1182b3319554190d7823f6e983a8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "78fe94c220ee1694888d0e654fdf433f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d30aa13afdb1dd44bbc7de61334c699c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ca453560ce689c0459fa96559f3c3773", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangYuZuo/Materials/zhanshen_shuangyuzuo06_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5b07eeda5adf56e4ba62593c361d3fb2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "20bc3bf644a7dd94f8a3a2ae72ea015f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "abe14dad734e6024e9f548e94a2e27cc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_2_baijin_new.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2b47b49cd90d1424483095b6ec2b9450", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_2_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cf0c2253f28081a48ab22548fd269bc9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_2_huangjin01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a03ea1f3c90d7324497cb11d62264518", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_2_huangjin_new.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "25ac1f5197216234bbe3f0f88dc48edb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "09ba0fe654e4be84987b1ef99575740e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_baijin_2_gaoji.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "709aad14c6159f34bba4ace686dbdd85", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_Tips01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c022f94939fc9944784ea1561f7a7507", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo01_Tips02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5fbd3cf0c447ca24688dcde620c5ae18", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo02_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f860e27f6b031b47abedd12c491ed35", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo02_1_chibang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "60a3f23b36dc4214ea4efde497e4915e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo02_Tips03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5a567ec66176bfd428ae0254a124839b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "45681abf40ffb654fb264ebe3c7cb60a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo03_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2274cebb6d9c7734dbd6130bd083e5cd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo03_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8b4799ffeba52a04c8ae27ca9d17238d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "68f6a15f40ae0bb48b8a3b2eb06b9bff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo04_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "be48ddaff2aaf4d4289861051656a078", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo04_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c90bb9ad6b4ba2942927e7e8114329ee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b7907a21e19173e4ea3c95eec0002dd1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo05_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c216a5eab0c41034bad8c48b0ed1d8fa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuangZi<PERSON>uo/Materials/zhanshen_shuangzizuo05_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ac11be8d9f54eef4e993c312acf344e7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_bing_wenli01_lq2_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "95afdf49983068a46b72d73c59fa3886", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_shuiping_nvshen02_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e818ba96ae1bcfb4b8034c0983a9155d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_shuiping_nvshen_mask_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e2b464596b16f044a849a21455085629", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_shuipingzuo_wenli_01_lq01_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "909f8930c4a278a48a6c9da03d7d019c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_wenli_ice_01_lq_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dfcd3f67c99cdff4facc14e327a4737d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_zi_nv_001_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d953f50839583ed498e548e51ac92ed8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_zi_shen_001_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b24bc9cfd53128f45ab7220e2206b6f0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/mat_zi_shu_001_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6de6472762f2a1b41b98b1f2fc918118", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "89d7aca043f33334c93388375a818b4c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ee8fd00bfa3ab144ca256481b6855fdf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_huangjin_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e7228cc654e55b44ab0ccd474b030eea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1861198adee0a9447b1193a52b1a61d8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f3a5ed6243170bc4eb8df2210db7182a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "90674574bf8600542b8439547a633f0c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4a3f03b75e6db4049845a42841adbf11", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshen_shuipingzuo05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1c6329c33d511cc4c929acd98e42bd66", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshi_juxiezuo_mingdoushi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1f4d7930d1c845f4aa5398e2b1624e4c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ShuiPingZuo/Materials/zhanshi_juxiezuo_mingdoushi_Hair.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b9130f8b23200a94686599c1eb7e125f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/changjing/tex/Materials/simayizhanshen_dizuo_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ed5666f2af6369441ade4abca08d4af9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/changjing/tex/Materials/simayizhanshen_dizuo_diff_waiquan.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "068bc893fd151de45aba90d8c342e5af", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_01_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c98f5fe4ba5529447884ab8af5a60729", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_01_Preview.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1f8c94f396883a2449f23d84c93f567b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8d6390a13dd0a064d983d358d2c5c43e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_02_diff_shanzi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "61ff3fa3832da52428bfaa8f9632c713", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_02_diff_shanzi_suipian.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "34297b0b031dc6d4da8474fd98f7f8ad", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/fish_1609_simayi_02_Preview.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5a42d27dcc1ab3a4dba9a7e414950519", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/ProjectorShadow 3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "08b091573169dd2439bee23941e45fd1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/ProjectorShadow_Receive.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b8f158f5345ff7144b64172bb0db3032", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/Materials/simayi_bg.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "feb6f2e0560f4fa4c8a7f77353639eff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/<PERSON>ce/Materials/rock-01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0d22412f4a3fe9d48a188863719dc5f7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/SiMaYi/<PERSON>ce/Materials/simayi_tiankong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "518638c2b2b4d3948b546d8dd062f6d9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_Taini_Putong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "32ef37e34889e334fa91b812d8c0e297", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu00.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a614072500e939d42a7fa02a11d2828b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu00_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1ebd339e6a5833b46a6ac37753012fcf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "923860f3c24bf1c40bab06131fcecb81", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu01_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5455a07076cd0d148b81827f4b533f76", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "586e3aae2827b314b9d363820510e552", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu02_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9c0d6fe14261dd94b92d1a64e57f82d0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e52f4ea36e7c38346837fd9fd36656b2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu03_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9890e4ba8f0d5748aa779eb1b25b187", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dfd7536b70537e04290f5d3b8cac8c4b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu04_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e62e15e106329642811b7c282a7a826", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "07dab4970c3de9743b29308e9659319e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu05_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0dd1207a774dade42806bd63af783409", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3a4af6213aa032d42ac215589181c75a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu06_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7e23ca5228236a144abcd64022bc0d4c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu07.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cb2c55e5b48481144bf28d3fea96486b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu07_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "11e535d6aadffc84ca9b8db6652c8e24", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu08.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e952ec1dafd589246a90b450678cfa99", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu08_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9f4a76c24b519774c8c26a10e0754f15", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu09.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5d266433df76229499495b754fa54c16", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu09_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "692fb0dd4b68e0c49aadd1b28fbc14a0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu10.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6fe3cd3eeaa9998429279489695dea9f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu10_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3fcc9c535a4acb74f96bba037eb32c92", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu11.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4d7570f49be8c194e8deb4b799fb2ea3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu11_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b03df59e2723de64bbddc3705501663b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu12.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6d95e635816221b4d9b9bdc2fac31813", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu12_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "daf61994776745044af94c699ecf7224", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu13.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ba529ddb4e1cb934c8a898602fd7f70f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu13_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4273d686c3b0bb24886272c9d9640834", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu14.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e54a9ca9f1cc4904ea157d48082a3194", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu14_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "280f64ef06d32a641af7e10b3de81812", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu15.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fd4def26277cbce4682c78406160d396", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiu_Enter_taiqiu15_zhanshen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b36d7edc5bd90da4dbf390debc07be46", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiuzhuo_Putong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "03cc8899cf7646047ad2452b0d902042", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiuzhu<PERSON>_Putong_daiji.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "543c42d4d70c68045b34a16dcece47cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_1688_taiqiuzhuo_zhanshi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "058655d6630f13645a1672eaa47e0ca9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_body.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d79a523748214654483647c1621b30c4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_body_Preview.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "aa7491482b573e64da3f50e1a1d676c3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_chenyi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b08fe1c308eb3cb458959405cf78973f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_jiemao.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c810d6c252aabd3469effbf5eeca4f4e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_piqun.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "16c1dcc13c12df6459d907823b0ff407", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_shouhuan.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8d7e63c4eb2b4464088af3b9f89d9a14", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_toufa01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "69087eaef440adc498dc1cac8b30b943", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_toufa02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8881de6f3e5707348a9c61ee6b4c6a6f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_tui.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dfdaa558e0ace6d48a612b1d1d3a9362", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_xie.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2c2b960579ba6dd4a8e8da2a527aaba2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_fish_1668_taiqiubaobei_yanqiu.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d60040d907d1ff744a7a70203d4b8383", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_qiugan_model.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2dd16b44e574370448d321ccc1b58d16", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiQiuBaoBei/Materials/zhanshen_qiukuang_model.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a263983634a047f47a07242c871b68b5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/1111.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "916ba6c9e57a65d4bba3fb6926c0968d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/body.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "353bb041a99b33147ac108bcf1c704d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/cloud01_ab.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "68c9fc710b785554f9bfaed5fce7ad11", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/ef_0062_mat_011.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "def46dab8bfa10043b768e3429633deb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_baoqi02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e13db06a49d1aa74ab8e8dec98f6f23a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_chongjibo06_an.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64cff421878f67f44a771809b3a8511d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_chongjibo07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a195f2f4a166704a97256b355e99dba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_chongqi04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3830759302427fc419a7a3118f813905", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_chongqi05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25e205d2e4204b041b9272e83ef64c45", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_cloud11_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "58f823d48d022ad49ba10c2ad4ccfdde", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_cloud12_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e55e167a10b843742ab1de34b2960bfc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_cloud12_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81348019da13dc448b816ba45a5d6834", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_cloud14.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "91d29f949a5aa1f40976d5f86014e332", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_guofengpiaodai02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ccad4d0915f4ca4cb6fe9bc34040ac0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_heiban01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9dacf587ba9b93843ab1864a78316dc3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_huoyan01_uv.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cba90465198988742896b66ac9615a02", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_jiguang_huoyan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "34f03a3d9ab472a479e919cf74951ee4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_jiguang_rongjie02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29f0a04dc44d2804c96e8106fc6d9fe0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_juqi01_piaodai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ab01c4753e6a3340b0fcf87c7d7741d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_laojun_scence02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9cea2f392bab55c46b183ad1ac240c4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_laojun_scence03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a6c831a76fcbe004f86c499cea0eb2cb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_laojunpiaodai01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9eb4471fea9db9141b52428fc5093889", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_miaozhunxian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94f453214dccf2246b0a864d8a5be07d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_niuqu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc843dff8203bee45a13910d4fbff2a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_path03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a9a6c7a5f3a61854f95063fa19a2ed7a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_path04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2fbdb97c5e73cc744bf6dab8640cd829", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_piaodai02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bee33f9c0d3524840a4875c9a35a0426", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_piaodai03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44ec62f171969ee47890d96c9042e128", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_tiaodaijuqi03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e54016289472a6c489badb63888707ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/m_trail01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b1ca5b59b45335043b832cc86bb168ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Materials/wuqi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dab2fc03af6cd6f4f80d4b41e20e2eb7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TaiShangLaoJun/Scene/Materials/TaiShangLaoJun_Scene.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e037764d5a1ab3f469faa31f866f257a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Cartoon 6.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "67d79abf07cc2134fbd3c661316d5ab5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Cartoon_skin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e2b9da7cb3665544a2dd2b791f11963", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Face.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fbfc59255a455ed4ca19a8df79c9fa8e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Hair.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "87acd92dbf25ef24ebe5606c4afbf7fb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Wing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8ae123c74583a3049bfa5d264d25eace", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/t_bullet151_mask01c_water_zmz03_tianchengzuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a34f61463b3271b448cb3824cacb37cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/t_bullet151_mask01c_water_zmz03_tianchengzuo_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40e8068bb828cbf4fa618c2dbee7f898", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/TrailShader6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8fcd710feffbd6743a49cee922f4eb30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/wenli_long_cy_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ff25f3ae0661b348a04bf9aa049078c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "749e0a8c7dfe8894582c626008c36aca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acc8c89e2c641454ab3e953836e4b869", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "58352fa7648dca9469f6143975374aed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo02_diff_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40bbfe87fdd1d3a468fb4892a41bc4d3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "52478c45ed986eb4497a28119c4a5f20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d06121571d35d6541ae2e004d4012778", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianChengZuo/Materials/zhanshen_tianchengzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "188425502eed7b6488cbc0a29788d1ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>/Materials/zhanshen_tiancheng<PERSON>o_break_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0bd230e36b6b914d8be3a366e5a6f0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo_effect_mask_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94d3e0283822cf6498d0c5b810835991", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo_effect_tonghu_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4c0853d42d463f845bd3a1c894060c0a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo_effect_tonghu_001_canying.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80de8f99152b59745a6d066e62ecf370", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo_effect_tonghu_001_canying_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9fdbdac1ca3114f43b7d25039208c44c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo_effect_tonghu_001_canying_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e81ef2f40f358f48b1f84b3ddaa0eb9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>/Materials/zhanshen_tianchengzuo_xingtu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "879b5a2e9e18e934d99830680292386c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianCheng<PERSON>/Materials/zhanshen_tianchengzuo_xingtu_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e857052b7c028d6409a1e9c45f0e49cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/bai01_noise03_32_lq8.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4565763f5aee4574aa17dc6fd78dc453", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/baiyangzuo_wenli_26.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81fcedf189b8ef741a89238e5c541450", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/m_heidong02_liziliang_lq_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d6c2c6e9f4d766449ef67bd80591486", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/xingkong_beijing_001_lq2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec20650b3d5dc14419d0e9d17c93723c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/xingzuo_tianxiezuo01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "35a6d2744b9a890439d572b94072174e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/xingzuo_tianxiezuo02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "17407718bf31f764b93498ee9e4bf65b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b75bc496a5f12ef46a2e8be220fd6954", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo01_diff_beishi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "70317f50f8954974ba4f643d97e24032", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7a720de20366541459fd646736dc2889", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo01_diff_huangjin_tanchuang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6382f141ea0aed14780824d4800ca2d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2db7c1b9fc251c346b457755048faacc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo02_diff_jiemao.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9064ebc48373fe34c95c46f33828be13", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo02_diff_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e68671969186b8f4aa8aaeb05b82e9d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cf93ba2a3f91c0346a39d18b0b54eed6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TianXieZuo/Materials/zhanshen_tianxiezuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ac6893d7c1c72554e9db2c9f36da4a53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/bullet_158_trail5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d46b21f5facf854da9394e010273af7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/fish_1536_tiesuoelong_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11ff775c1e021fd4681f76bedf3dcc4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/fish_1536_tiesuoelong_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e13f405dd0057345b08977cb2fda088", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/lizi008_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6075dce522a055a48adf386f20cdac06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_beijingglow01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c7fd3333a78cfb043a8cc980117dfd6d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_beijingheiwu02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b64955a245cfa2943b38a3e8404c3753", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_heipian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df223bb958b0e79459cb366ad546f9d0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df39e963b5196864c80d726d07d1f639", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97c13ded6947a824a92e2e8a8d41e1f3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "148a36cb5a234264188e68f8d3000aad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "da515012843170243a7888dd9ddd33cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_09.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9580ad7a398cbd540939f8d7f54c1d78", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_11.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "983750ba20733f043897e2fdbd831880", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0cd77b1f0c8921a4c829a94a0dba7f97", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_huoyan_14.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3b80b14cedcb864c98d9996d297a9f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_jianglow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0adb911d6b7ea934590f9d561fa6cfd5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_longzui_huoyan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59a0e064dafb9874eb66ea66b5ec2064", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_longzui_huoyan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1081a6bd26bfb204892c0f979d2884e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70ef843e9c850ed49ad61f7fcc08b1dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "84a396deabb6f894da7ce20321d8d18d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "84dbd50eae1bd094b876eaf2cb43a690", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce090b8520bdba44da792019c4843ea4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29cae4d25d282964495ec51f16a35602", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_shitou06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f7292d80aa4f19346a3892fff175b8a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_smoke05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "342a8727297669e4dacbe7a725a9f139", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_smoke_10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d568866fe9ec92948b48d924e3b71926", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_smoke_13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ec379a6073566d45991f372d67d3814", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_tielian01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cdcb92aa559d8be48ac2f8f921b6a8ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_fenwei_tielian02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e810503fe3e240349837eb9c0c6890ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huolongBuff01_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6078b01c405db6142a84f87822e91630", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huolongBuff02_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbf34ba852c026e45ac6719b8620a7bd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huozhu_heiwu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1ff5ae3b3634df4db0b66702be068f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huozhu_huo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c770bb830ac97d445bff4d7ea07415bf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huozhu_huo02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "058cc1b0b0bb624408423236d00e1913", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huozhu_huo03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d28a960fedf443b4ba4c7a26c523358a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_huozhu_huoxing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00e4ff8a6ee19e44aaaf94e103c0eaa4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_jingtou_huo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81851207fc94192489370da749b5ee79", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_jingtou_huo02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "667bdb640db06ea4d824d1527be01671", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_jingtou_huo05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a5eb2f8fffcf1bb48aebc1fa33f2f69d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_jingtou_huo07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "61e83a3596618dd4ebee92420efd0c6f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_jingtou_qiliu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b2c96c998d206d478b4664ab1bf5ace", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_luodi_shitou.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aaf471908c195da4f87f260c16cc398b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_luodi_smoke02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3233cddb726be249802527487b4dc26", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_shangsheng_qiliu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cbdf7cf13b925984990d0017743fe080", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_tuowei_huo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f28688f71c177904886c2c871d18fe91", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_tuowei_smoke.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f76137533267a4349b156d1e7760122d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_zuochibang_huo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de6333239f847b04caa6d34f455ce258", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_zuochibang_huoxing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd88eed1b1ae39646abee0cf6385e071", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/vfx_zuochibang_huoxing_b.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02fe0c3058fbbbb4a80a010ff335d184", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Materials/xulie_yanhuo_011_4x4_blendhei 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b5ad7229cb807f642bf264a510ad6580", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/ding 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01a27dbe476b6a7438e80f24c81a2454", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/ding 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9bfa9768ae20ebe41a6d1fb326312a57", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/huan 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000010678058598274979666", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_column_2_low1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f6a7a7fe81139341a9b8deebd12f99c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Fire_Cliffs_TilingStone_01_inst 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000005644016277772475697", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Fire_Cliffs_TilingStone_01_inst.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000004262538941826261198", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Fire_Gass_Patch.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000015547160478710561656", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Fire_OddsnEnds_FloorPillar.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000003044086844444394523", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Forge_Face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000009279218044882033525", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_Forge_Swords.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7929d28e6737ef740bb1404d1e8e9f5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/M_support_1_low1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d9bc42161400f541805e19145bd13d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/MI_Pine_Bark_Piece_wjswad0_2K 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f9001f8ba1625b45a130b4cd34f04b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/MI_Pine_Bark_Piece_wjswad0_2K 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000001141916445071122193", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/MI_Pine_Bark_Piece_wjswad0_2K.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000009550572893496570567", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/TieSuoELong/Scene/Materials/MI_Tu<PERSON>_Small_Stone_vizvchy_2K.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6632b5e984545b04d9e18e24736cf782", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "641917a563dccf34d93124e648c3905a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_fresnel.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9a9038f141e6fc74fa66047f4c039e65", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1023f2dd450f94b46af049c51d334797", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_01_caige.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d18553c34be8b0f49ba0fe63a8444f8a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_01_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ca8cc665e74d9274da13b3be4b5fbe5a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9294a05c0122e9846a94058fd7faedf6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_02_beifen02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b0c340d68eb87734c8ec893e0bd26953", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f74aa55432399da41806d7e8b7e7606a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_beifen01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "36ba728fd5870904693b22fd7989763a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_Paodan.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7a1e2dc3805f427468b08b87545002e9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1dc3f097d4dcef4418ced6a3a758802e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_yanjing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fb4a01af910d77044b45b4e88721f393", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fish_1584_jijiatuzi_diff_yanjing_tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a34b95929c7296546ac14804d3e59cbe", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fx_fish_1584_jijiatuzi_01_scene_effect.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a933a02fa92100143b441251e01e0ade", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fx_fish_1584_jijiatuzi_02_scene_effect.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "01d0278768a1d4447b9a37406fcc1c1c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/fx_fish_1584_jijiatuzi_03_scene_effect.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "059d6ea5928be884393cf97311b9fd60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/ProjectorShadow 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b00190f1aca69a440b6477c8654b3f93", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/tusiji_beijing01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "921b08e0aa3ee8f4aa9c233073e1399c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/tusiji_beijing02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4f5e492990bbc1649a8032b7a91b2707", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/tusiji_beijing03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1b60e322ca9962a409d461a484c04b39", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tusiji/Materials/tusiji_beijing04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "78c10c02a0efea049bef25095309074a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Tu<PERSON>ji/<PERSON><PERSON>/Materials/tusiji_zhanshen_dizuo.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c5c3f6d6e18987f49b9efb86d75c5105", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Materials/Mengban_Jianbian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3a949adc84b5d443ad28619e1866013", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Materials/MM_MillHorizontal03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20330de1a2a37864d8e7362a0e39d4ea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Materials/MM_MillHorizontal04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000001892958700356839396", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/changjing/Materials/Yarrow_MInst_LOD1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d694ae267c5720849a45db31f53bc699", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/bai_a 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a070e689ffb0a349b0ad2d7a219e1de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/bai_a 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "48cb0f5be7fb1ea4782b6c8fff1fa1e1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/bai_a 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4a9939ac89893ab4db18100339602f22", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/bai_a 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a1b93177038053c4591e35821a4a9519", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/baiyang_tuteng_canying_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d3dacb42c022c44e9731fb3415d3371", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/choose_cannon_wenli_2_cy_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39cc2430b401d8d4db1bc1cb8d6da78b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/ef_mat_line_02_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96b4033fd83a9a344ae4a02e9ef0eb06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/fish_1582_xiahoudun_01_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c2be2e01f37e7cc4399171e2f1c315cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/fish_1582_xiahoudun_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "566f1ceb9ba170640865a660cf78c16b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/fish_1582_xiahoudun_02_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "23639ff72e4dfd34595e9c183177228a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/fish_1582_xiahoudun_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "671cd52841d028c4caa6e4f51ee5920d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/huolong_bullet_wenli_006_3_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d270a0f3721ee544980e9fc2180e751a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/liuguang_cy_005.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cc4553762d659214fb5e77495709a98e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/obj_fire_zmz05_noise03__2_cy_1_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a9cf13e1314ccc4294fb1c60227ee36", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/obj_lock_zmz01_light005_2__1_dis 4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ca132575a195f946878bad33bc3a279", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/shenv_shou014.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "91463135c7a9ad5419a77ad62d249073", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/shuangzizuo_jilifankui_canying3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c04510e28aa6fed49b3c4bd327893c89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/t_bullet151_mask01c_water_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b80c078b4d28151449e9eb5be759437a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/t_lx_zmz00_1_path_013_c_1_ui_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f276ce6d12dfc4841be2f931449690f7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/t_lx_zmz00_1_path_013_c_1_ui_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f6180e27cab88b34aaafc21488179e32", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/wenli_sanjiao_add 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "577761e2657d8674f937db57f59d0548", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/xingkong_beijing_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "49b6b327b2379a74b857a3edf36498d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/xulie_yanhuo_010_water_zmz01_ui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1be3ec8147de3ed418949618a57b0c49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiaHouDun/Materials/xulie_yanhuo_010_water_zmz01_ui 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "30cd1bce7331975418c43f2e9c163695", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/fish_1602_majiang_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7090c5717bc3a9c45a267caaf9630fac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/fish_1602_majiang_diff_small.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b7f727c19ba8b0409a23f3dd3d4585b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/fish_1602_xiongmao_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e5b3955f9de86345bd3bc707405f73a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/fish_1602_xiongmao_diff_tips.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3a52014c6202c3d45ae2cd8d203f96fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/fish_1602_xiongmao_diff_wuqing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3289ca3c0ba8b554ab19ef19d8e70aff", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_jizuobiao_001_ld_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e211962fd96c991458cdb9118480a4a1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_shengxiaolong_hitMid_baodian10_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3087cf41c5d7800438aa0b81a60907e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_suduxian_liubei_pingmu3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "071d850011dbf1d41adf940fe8469165", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_trail_002_xiongmao_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0d898e50a2dd1649ab6e4224547cb64", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_wenli_011_ld_1_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9b608ea8be63fb34ba7c2eb08749837d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmao_wenli024_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0bacc99db45a74f488c3f8896581c3fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_majiang3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17634ff19fd8ff241a2642bcd1e7c6f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_majiang_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "90cb1ff7098f51b45ab52329110ed380", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d1819cdc4576fee429d9b8346be7687c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6149afb866ea334f8e0e53b4c6cd481", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli8_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20b54c190d9603a4a90b16be2dc5053f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli9_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7c0a6dee7b004464db9a4debb6fcb494", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli10_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c9f07a10cf25f948a2db96026af1b49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli11_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3d3510d831074f418b1c3920a9f34c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli12_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2e723a15634d1b748ae43e3a14777cd4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli20_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c347ed9de3eea8f4d90fb0016e5f9ef0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli21_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a20ac339be663f54989de46c3b073267", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaodaxia_wenli_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a310d43d5a609d245a93c97e38fcf597", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_xiongmaophuan2_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "432e3e91fb060d348839ff1187862703", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/mat_yinyang_xiongmao_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "80b43093e0a73ea4a92b6b2116422643", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/xiongmaodaxia_bg 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e111b58933cd5d149b6b1f2fe8ec4965", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Materials/xiongmaodaxia_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7d48749184d46d4d9c6d1041e88d94d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/changjing/Materials/SM_Ground_005_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dcae3c2e07ee99e4391c39f0fd4ac0d3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/changjing/Materials/SM_Ground_005_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "72ae81ab80f5f0c4ebfe68f8ad35e444", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XiongMaoDaXia/Model/changjing/Materials/SM_MetalPanel_06_BaseColor.001.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "36a4bad925e3ad448965f4a483a9c133", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/fish_1583_xuchu_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "092a3d3f1116d644d92670e38abe0b75", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/fish_1583_xuchu_01_diff_tanchuang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9c450325a36b85d49a1e056b913905bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/fish_1583_xuchu_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5896c4f12825ff74f8e22d195f923911", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_baoqi01_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4828bf5474f8cb8409014cf2f5c1d6f5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_baoqi02_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c68e461eee8d4074a99d5085e41e989c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_baozha01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a97c1e57e4855044eae8115f849a116a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_chongjibo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d31549c5e0beee348ad39eed97a515d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_dimian_chongjibo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1bfa6da7b73fd0e4cbd04ae0ade1dfe5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_dimian_yuan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9e4952fdd1e0c64293791b0b3fbc7d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_dimian_yuan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "80236e289cba86640b471fb3d5058da7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_glow002_diss.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02652fdfe1eddb047be4d6bcde14cde5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_lizi05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6fcdd4e1dca5f2459c19c475bd4df14", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_lizi_liuxing01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "afbe5d532c03ad042aea25b6504a4e65", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_luan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f007aae06cb0c5e40ab8513837f428e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_tiankong01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4e5615c3986a0147a7b1bae4bcdb19a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_tuowei06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e0046046b18497044b77d395b693a8c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_wind02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ca84a7aa9a842144896a40f379114d1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_xiankai01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "220580d17ac6c824b9226752de37c5fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_xishou_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acba9d6761b446c46b4ff7f02c95e3c5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_xishou_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44e9d51976bb9844c930d46045fb96ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_di.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a404ffd903329bc4596262d5e81a9db0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_dimian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6d4501dcdc3a044c8ffd34f82c04038", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_ding.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c606b58628d49bb4e83a84526eedfd10", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_liang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5157cbb0ac9d25f4f87920a4379815d3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_zhong 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "accf163208ca9f749993be4d4845ff5f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_yuanhuan_zhong.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3980a6ca6babff34eb22624ce9533df7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/m_zishenwenli01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7da0ceb23cb273f4fa3832cae0400407", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/Materials/ProjectorShadow 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000007646435879753856335", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/scene/Materials/MI_floore_6_2_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "abc00000000004593538432935863806", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/scene/Materials/MI_floore_6_2_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9e83e78f688ba6e47a1e57080251aec6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/XuChu/scene/Materials/xuchu_beijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d7ebba412871e954c86d925becd3a41e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/changjing/Yang<PERSON>an_Scene 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cccbd484f281fd246baad46125cddd37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/changjing/Yang<PERSON>an_Scene 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9a36f5482bc44844194e9ac147588b4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON><PERSON><PERSON>/changjing/<PERSON><PERSON><PERSON>_Scene.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22d6de7e73a2c37458290dfb84aa870c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/effect_yangjian_yuanshen.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6df9dc56fb98c2e48937914a0ea20df3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/effect_yangjian_yuanshen02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07ca8bf6de549b4438983b0f69b70342", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/fish_1589_yangjian_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "da6d5a1195ca3f64ab89680a79538570", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/fish_1589_yangjian_01_diff_zhanshi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e21d7022581c0dd4da368b7b3f0e4e27", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/fish_1589_yangjian_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "28a64de23625fca4d883289e0b6a6da1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/fish_1589_yangji<PERSON>_wuqi_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "931fd755905a40743a40c65975b8c32e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON>/Materials/hand 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17a8653a05911c44083dd5166af12e70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON>jian/Materials/langtou.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c3c4d329124b1544cac735332405c349", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON>jian/Materials/m_cloud12_lq_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fcfc4e7caa5e1a149ab5061a8e50ba26", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/<PERSON>jian/Materials/path_004_3_lq_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ebec1cb1a48015e4e996f522345efc55", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yang<PERSON>/Materials/shanhehuijuan_lq_001_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "497fc229f4342444bab3bd0a18449d3d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Yangjian/Materials/xingkong_beijing_001_lq1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3e7205f43078b64e95f1a023271c736", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Materials/fantianyin_mask2_cy_6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c611a96c221f2249845f7c6d3489d0a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Zhao<PERSON>un/Materials/sanguo_shenqi_zhao<PERSON>_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce005e7d9ff21254d8476d7a7e769c36", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/Zhao<PERSON>un/Materials/sanguo_shenqi_zhao<PERSON>_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "61a2dfa0de9bd8d48a6e82086f8ad139", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Materials/wenli_jingge_03_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0599ce6a66188645add5e39a4577848", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuan2/Materials/1001_Base_Color 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd7d067ecbdb3d144b2b8c91e14ecdeb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuan2/Materials/1001_Base_Color.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "456964d2830ed3a4ab1b1cf60fbd8ea4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuanjin/Materials/paotai_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a581a3a82f995de4b9a86911788653e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhaoYun/Scene/feichuanjin/Materials/Raven_Alb.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f34520202e307ec44948b16d816436d9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/1580_cannonskin_wenli5_2_12.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "38d17a2ae4dddeb46b11464dfd8fedf2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/bullet_147_tuowei10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1fada6c53aa73ef4a98b41ab00d5ea0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/fazheng_lc_04_liuguang_22.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4033cb51cc8135a42beb73b7c535dfed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/fazheng_lc_08_add_1_caise.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad763f91adf40954baaf2f02faa82827", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/m_glow05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e56780aa876271a41ae403efa800072e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_baozha01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c549d96ee6f13264baa6dbc67d392206", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_dianlubianwenli01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8acaf2c6574d45943a9d10f13e2f8c39", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0d8dcc35bd43adb43bce13d2147c49eb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e00cc3140f610e14bb7d61226b90c0ea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian03_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d000cf614077fe64f9f821a7ab74fadd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian04_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1e38025dccef9924a81442651b00e713", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian05_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "488f469108b10c44aa66514e65998bd7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian06_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4842c7efbd622ad4480d22a04f414f49", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian07_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "34b552d3b24c41f4d842069a193f8bec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian08_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "55b1f1d6f2c62414ba4108974e91b4fb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian09_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "798241871ca33dd48baf60f0beac452a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian10_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "39f91a3354c7feb4abb79ca419487b16", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_guangxian11_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "db054f7d29d039e4a86dbec3ef418243", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_jingtouguangyun01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b6b153d6094ae5d429b0101fe5554e6e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_kejichangge01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b841d6e6775c9bd4a8d78dad3b86fa7b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_line01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f86a706e9d8f2e042a05f510f275084d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_line02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fd83d93c269bcd043a8832ac0ab5f528", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_linediss_01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d44cdb0548111bf40ad7a7bb59448742", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_linediss_02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cd3ead10ec74feb4eb46cecdf9c5f0e1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_lizhi_003_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9a1b702891dd4c34888f415094d6c069", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_lizhixulie01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "946cc3dca876f754daf138c6c9fb9989", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_lizhixulie02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1d2783dd4621edf488901ff6ce95502c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_redbluepy01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e76c698e7eab97458ac946c1eae9cdc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_ring_04_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6f1ba7ea3f20e25428f52d9cd296db94", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shandian01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "928b29b16c71ae848a15306375090cd4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shandian_02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8412d377446076e43a1ed4a1742a3376", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shanguang_02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "00212cdca0e2eca42ae661f27100ceb0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shangyun02_lq_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4e101043d9b63844aaa15be4dbf7ae0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shine01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8a0ca455855294f429af45f46c6a4bd5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shine02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "415590e1d4fd7f44a90c8784c7f28a55", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_shine03_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1748cd12a02b6d44e8b4bf81d351989e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_suduxian01_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a111cedfe8861884f8bdc690b319fd6b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_suduxian02_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83c05fb76e4b8884997d94bca6b84651", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_taiji01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f9d5585c581342945b363ca8a3d0c745", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiankong_01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d75eb6cd6de2e9841b6449ec88504bf1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiankong_02_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b040cfbc601a39249955f1297b13d792", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiankong_03_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "60b5881bed7c4da45867d3747e7311d1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiankong_04_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "656ac7e41beaf9144ac793aeef460949", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiaodai01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "708c08ba230afcb4da35efd28661a957", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_tiaodai02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "025903f1f3ec28341bacc5b5344f5950", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_059_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6afb49e43e275cb42a6c8906dca0248f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_060_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5cf82a4a114461c41964c27a813d862d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_061_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "32541369a798b3b4b944b01d6fdb2c64", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_062_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6410d4b6b130f804f86ca2827406abda", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_063_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c74566a923cc4a747812d5d75b6daa33", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_064_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4d13c6867975f524d8e84d8b86745ba5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_065_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9c23ecd36092e9e4bb9dc16ab15ce454", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_066_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4ca9f3f2a1c98e04698c77aa282401cb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_067_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ea1e7ccbe4486b04d88c80991868b130", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_068_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d688ec3d6cc6ba54da52b3679e8b8a1c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_069_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6f03ac83887136c448aa4b53e0167e7d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_070_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3dbd8cf49cf614448b29b87d4237b142", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_072_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb568b01f61c24f478a45ce95344e793", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wenli_073_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df54819fcbab8394a9dacbd3c9e62788", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_wind01_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0b1fddb8346c8d748a034eb599a47284", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_xishouxian01_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "724b9d48ad8608148bb0180ef04ef8d2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_xishouxian02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "08118bb8380effd4a8af6bafbe5e3c4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_yuncheng01_lc_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bfaf1eb15bbc0f24e8c2b296c84d3a22", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_yuncheng02_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bff26f48101623e42b9f5ac95be0fbda", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/mat_yuncheng03_lc_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7fcfb90d6ee36084ba7ec44cde0ea656", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/ProjectorShadow 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ba891c98459e7cb4681d8aaa3c93703f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/ProjectorShadow 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2418f07db181661489f18fd3e5a36de7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang01_fengge4.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9bb35bd79e0fa941aab66cf3e055dd6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang01_fengge4_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "60bad5df227f0064ab369bbcc775413e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang01_fengge4_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1558e7c62704c13458cd6416f7f4fca6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang02_fengge4 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a62af70fde891704e9ec034a5ca65f66", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang02_fengge4.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "02373846b4bdd3548bcba9350083c14a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang02_fengge4_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6d1fbbc6991e0614580f20ce8d015b47", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang02_fengge4_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9b1bfb0a68fe68d459286542f09d71b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang03_fengge4.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "18b1a4c340750c7459e8eff71e166e3b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang03_fengge4_Tips.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1b65a31818098e344908d09d4f824fd0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/sanguo_shenqi_zhugeliang_shanzi_rim.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "72c8418dbd2e9324cb497072369d2e55", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/smoke_lc_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ed3cce44ec21fa4296ccb3ca68184d5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/t_xiaoshandian01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "adade8fd6c5e74d459bcbf383a5e8bbb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuGeLiang/Materials/zhugeliang_beijing01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "aec3ddb39e8a74f49b4ccad3492ee8d4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/daoying.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0d5443ea65e27ff4c8e1b49fa891fc0c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen01_01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "759922a6a6f1bac4c9b0467a8cf4e5d8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen01_02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cd5cfe52b29006447b1e77f5cf72bc3b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen01_03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6620849a39796e94dba37dd3ff688705", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen01_04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6c26ddad5fbdf4f41ba806341d1e6194", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b06d0b89cd9faef498ebec933ba2b8f4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen03_diff_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "035833d10e09c7d49a55a9d2ac95324c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen03_diff_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fde9759786b4d034abaf973d67f22276", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen04_diff_idle.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bcbffd80b862176498065661fc8ee935", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen04_diff_show.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a913a00716955184280e21101d011138", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "96d3f7380deabb94b849f5f68208b774", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen008009.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "57b325ce30e5f2a48b39f9bc56a55b2a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/fish_14316_xiarzhanshen010011.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9050d3af391f79648b6abd146903091b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/M.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "442d4c6015b49784784fe803f2d3bf64", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/shuimian 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a1ee3f3fe483a4246b2633dd3c6d2d04", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/shuimian.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d04ff0d250c30c041942237e2f1936a8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/shuimian02 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6dd4cdd9af2c89949aaa6145b2e467e0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/shuimian03 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fa3b54c0f3913a742a1028341370ccd8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/shuimian03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a551dbd076479f54f9a32ecbf87f177a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xiari_beijing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a465ad6cb18f9ac45b8b4fd2b1e92f2e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xuanwu_beijing1_1_daoying 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7d0f8c30afd5c9a44b34fd83efff135e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xuanwu_beijing1_2_daoying 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1b471e849f69c1845b9a735bf2067687", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xuanwu_beijing1_3_daoying 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "eadf1dc67f475424ca3566c072cada92", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xuanwu_beijing1_4_daoying 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b964532b590224141b5b645560a33a95", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Materials/xuanwu_beijing1_5_daoying 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5dafae2428c10c44381ca201cf6980fc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/beijing-3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b2f2d9d48e7d8a8458c75745c071ca23", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_cao.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1395735cdffaea748afbe55f33585d0d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_caomao_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "56cd76a901e523144b26acbbf18e19f9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_caomao_2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9b400afb16d07f242a77b011ed42d227", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_haiou.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a848749af4850b34da0c031a511b5cca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_haixing.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "76c7fe03bc52c6b41a1d43a0cd1a8c1e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_paiqiu.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "59619be97d6b0f248bf8f3d9abb99f61", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shadi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0a29e77b6e57b2d48a2980fdd5be37d4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shikuai01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "95a20f9830304a247b20f8e98af1a721", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shikuai02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6fd14d3d8a1ed8f4f9bddc5d231f7f35", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shugan.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "524942c5e5f7ba94492d35e6c7184eb9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shuye 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "21564c6dc55927c4d895f3d14e56e1a8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_shuye.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d79c112ade2950442803debd426b4e5c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_taiyangsan.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2611ecc41fa0fe04090638d0e6e24a14", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_tangyi01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "32a48cd29e74a174badba1f6cecab83b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_tangyi02_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "745e086fcb10ed649bf48f76faed8523", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/Materials/xiari_tangyi02_2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d0d62fab8e968314cb819a4c381f034a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Scene/xiari_sky/xiari_tiankonghe.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b2f6156e3a3a6d84aac318a321a9c0c6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/CannonSkin/PrefabArt/ZhuLangTianXin/Textures/qiang_bg.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}]}