{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetMaterialSchema", "ScannerGUID": "544d1c56-4073-49bc-848f-ec3ae3810399", "ReportTitle": "扫描所有材质球", "ReportDesc": "规则介绍：检测材质球是否有冗余属性，以及引用了官方的标准着色器、URP/Lit 着色器", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "38e1576b072ccb7489a0b6340fad2198", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3901/spine/wing_feather_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "330d7556c9a9a8344aa35750e15ba5f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3902/spine/wing_technology_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c3e6fc7fa2a16824ea188b52264869dc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3903/spine/wing_butterfly_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d48d96c597d11d46af463aa01418150", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3904/spine/wing_phoenix_Material-Screen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c7cb5dc753ba5984d9063814ef5cbb06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3904/spine/wing_phoenix_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3dde400db7ce5e84da7c68e536e6accc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3905/Materials/wings_3905.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9a9b3c8cb4a2aa24fb66c6aac802a02b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3906/Materials/chibang_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0ac6f0004b82dc44a2a4e40b0e59b8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3907/Materials/wings_3907.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4dd1f43cf5a12c44091e0bb26b74b390", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3908/Materials/wings_06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5554eb4a7f525954f96f2071617f959a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3909/spine/wing_9_Material-Screen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b2d82c2a7cdc7944789f28bb0145e5ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3909/spine/wing_9_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6decfbced6838c44696941de452e09b7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3910/spine/wing_10_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c8575511ee36934cb827d7fdff3ed87", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3911/Materials/wings_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f383599a12777a47a482c02cd1fb64d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3912/Materials/wings_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e5baec6857447942acc4976c3866024", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3913/Materials/chibang_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6f9b71c37d05c44a9c36804af540b53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3914/Materials/chibang_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e37c943c6dce69a438cda66db40e3458", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3915/Materials/wings_longgui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "127c12a680fc88e4fa70be274a9e2f70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3916/Materials/wings_3916.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "337b8f8dd7ce2394f9f776ee698b23c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3917/Materials/wings_3917.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bb039d052c840044987f2b2e2796a104", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3917/Materials/wings_3917_effect.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22c0fab300da6884faeada439e2f7b0f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3918/Materials/wings_10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01e0c9cbbb3491d4f93505b4fa784480", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3919/Materials/wings_16.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6ce8f05a6b87b34a89f54b232247aa6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3920/Materials/wing_12.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76c86fdd54b54d04588c578764cb08e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3921/Materials/wings_13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad94241604e58994da9651ded560a9d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3922/Materials/wings_3922.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70099d08960517247b041d65020035c8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3923/Materials/wings_3923.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dac0790e416a2624e85431b2e7b9e854", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3925/Materials/wings_3925.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f390de85411abb44b5d1775dbedecaa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3926/Materials/wings_3926.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f6e84790c6772b4a9b827b4c5908141", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3927/Materials/wings_3927.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e76aac5d85b0e01458f7d2526e5e59fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3928/Materials/wings_3928.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3be377fc4c3786746b2bb4a5b4f047a3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3931/Materials/wings_3931_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4eaaaed61083ac04295ea9a14bd93670", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3933/Materials/wings_3933.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "003e48d8e09b0d246aa1e53975f4327a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3933/Materials/wings_3933_effect1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d86cd6654b477a04094987a74dd22715", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3934/Materials/wings_3934.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29f64e241776f1c42b866d10bdb9d51b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3936/Materials/wings_3765_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1458d24583e400f48822b65f63cc349b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3937/Materials/fx_mat_chibang_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b31f5a814fb0964a8ae9728f0e692da", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3937/Materials/wenli021_8_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a5501f4a7906e24ca671aa061941d65", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3937/Materials/wings_3937_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf45f5d84eee5e74c9af49bfb42037e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3937/Materials/xingkong_lc_01_t_lx_zmz00_ui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5fe402c041ff64745bd0745ee84bdb18", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3939/Materials/wings_3949_effect_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6824eec00fa9eaa4289f4027ac868b68", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3940/Materials/wings_3940.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "494c1507076ce8d43bae575577ff6557", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3941/Materials/wings_3941.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5367fc5bbe73cf649a050da809378559", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3943/Materials/wings_3943_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f927ab40f50b884e90b597de6e98466", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3944/Materials/wings_3944.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "45cd30121eb0b1348887bc2d2ac3d213", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3945/Materials/wing_3945.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "66bae9deb1c6bda4aa605a6873fbaa8b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3946/Materials/wings_3946.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ecc4fa7327db9543beef6cdba3b8aea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3948/Materials/wings_3948.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "820899db0f30d454998b38448c0073b0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3949/Materials/wings_3949.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "654a14cd4e533654084a08dc083b3048", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3950/Materials/wings_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad71b213461c17640ba1aaab3562c961", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3951/Materials/wings_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7fa6c05d04a1a7847b5ddff325680fd1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3952/Materials/wings_3952.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df39ab84fa3ad1f42bdd91ddf0aaee93", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3953/Materials/wings_3953.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "acfc9c9099b025049a806059a24e7bf1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3954/Materials/wings_3954_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6928b259a767c44ab63dfc8c747617e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3955/Materials/wing_3955_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3615a33bb22bce945824e54f5bfdd731", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3956/Materials/wings_3956_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08b466e84dee7d8448ce3bba4f3d9c93", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3957/Materials/wings_3957_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96b3090650fb9974e93558fc894c97be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3958/Materials/wings_3958_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "72febaa864becb54dab580b68dfafd5e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3959/Materials/wings_3959_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "88cc0a387314dbf40a32696e13cd8811", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3960/Materials/wings_3960_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07175a323d2790347acbb6c765f9bfc2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3961/Materials/wings_3961_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "476a5a379f5c6484cafd46af7cff0eed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3962/Materials/wings_3962_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79554ab940d491242b757dbd6ee469c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3963/Materials/wings_3963_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "165ca7deaade1b14289aafee0c7760cc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3964/Materials/wings_3964_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "099eca69536faf548a03448445ddddf5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3965/Materials/wings_3965_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "174bf4cf004b19048a24bd77b6bfd338", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3966/Materials/wings_3966_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b80c04dc3aac755429720732416d4075", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3967/Materials/wings_3967_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0d6a142b36e974448aa87ad6827746d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3969/Materials/wings_3969_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2483e815d4dc6124b8e642e38a5a246e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3970/Materials/wings_3970_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f6166dc4b7bba14597ef36978596531", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3971/Materials/wings_3971_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e14b8154bdf56b84dbe9946049a74604", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3972/Materials/wings_3972_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b0051a8fdd67d44d9712cb49e3c83ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3973/Materials/wings_3973_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d302cb7974e7fea4b8565925f94685bf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3974/Materials/wings_3974_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ddd5c2f4f3e1194f97fb98c5b322c80", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3975/Materials/wings_3975_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "15b8feb44d4c1f844bfd24b34300a97e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3976/Materials/wings_3976_diff_effect.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e68d767d24b7ad4eaa2773cb6614619", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3977/Materials/wings_3977_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "333bb66e2b749114dadf027025c78a93", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3978/Materials/wings_3978_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "afda8c1230caec349a176536cda4f651", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3979/Materials/wings_3979_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "58aa974a06c4891408de4bb9f13c1dc8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3980/Materials/wings_3980_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f9bc85127d2a6241b6c918e0942006d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3981/Materials/wings_3981_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "420d3ac3b66dace43b15a7a985e27991", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3982/Materials/wings_3982_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2cd983fa3486e66438c007f3103b268b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3983/Materials/wings_3983_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f1032bb62273134bbb04926976d1f71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3984/Materials/wings_3984_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aeb1d8e573a9719448f7e8c813462a5a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3984/Materials/wings_3984_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb5640ec534d1b34686496115ee1ee09", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/fx_mat_13985_yy_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0914ad295ac0414eaa73d90c7dbbd1e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/fx_mat_huaban_002_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "43efda6466fce074cb05a5d0796251bb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/fx_mat_path_016_c06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4067d381f5fb4b04aa70850f370780a6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/fx_mat_trail_003_yy_tengman.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8134932d02c328943aad7070642a3624", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/fx_mat_wenli_001_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "079f303e8878a314da3b0eeb2e689234", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3985/Materials/wings_3985_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "917778fd661a2c74cb895ccc614f2fe1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/fx_mat_hongbao_001.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2aa81e757bed11e4f9078e264a3355b0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/fx_mat_majiang_mask_001.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "529327f1dbc352b43af9c1536727c57f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/fx_mat_majiang_mask_002.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6f2b662c20a83124ab0c70caea5bea38", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/glow_junyun_zmz03_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "894661eebc57bf04886974c747500d92", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/wings_3986_diff 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c711d8240fbb8f7428052be7f636cad5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/wings_3986_diff 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "864c2a958a043fe43ad0fb88e0c236ec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3986/Materials/wings_3986_diff 3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d78f1a0858723ab4e9d3264f9df0217f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_burst_a_bl_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6cc7955852403a40b14d7f0ab3392eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_flow_bl_a_b.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bdd99bfc33400ee43aeff53537e7abf6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_jijia_jjkc_glow_b_bl_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b2944aa2c761fdd4ab6cfc81b4031b97", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_jijia_jjkc_glow_b_bl_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c4959ce8e03776341bdb8967f8c72c7d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_jijia_jjkc_glow_b_bl_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "93536e473a6b5584396f43e216456d9f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_jijia_jjkc_glow_b_bl_04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "571a198f7f20884459283c9107a50833", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_keji_a_d_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d1146ae2fde18f4d847bee6bd8f875f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_keji_b_bl_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "68f99d732407a91429c1d5d8364b7355", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_keji_gr_a_d_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ed164fe821618e48a4330b04cef57fe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_keji_liubianxing_bl_a_d_m.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "328703028ae6d2a4aae70329d285b3ac", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ad33cf29369e4344a3b2bdf5e2420bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b74fe62b89c093478ac8e1eaa58a1fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "001036ab9c735144284bca094f99ecfb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2290c67a22df64b4eb068ec81408cdbf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "27c8c800ac383b94bb7a1336c3ae3840", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_bl_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "714a2504a24b76241a57a405ceb40007", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_lightning_a_bl_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d78060ba68c4cfe4786552e28637de74", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/fx_mat_ring_glow_a.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5d1961acd24405f48a366a0ba632f994", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/No Name.mat"}, {"Title": "错误信息", "Info": " | 包含官方标准着色器"}]}, {"GUID": "f4e5c7e243c5846428af81ef97db4f93", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/quan_003_raodong 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c7429efe58037064489251eb9fbb4d86", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3988/Materials/wings_3988_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d811f0418c838544fbf54301c873938d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_fuzi_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "df8ce6acb9314474f8bb0c67377b2ded", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_guofengpiaodai04_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "901ea9ad3b2672b4e90e67648281b7e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_guofengpiaodai05_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f06c825f779c1be47862c42d2fdfa92e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_guofengpiaodai06_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "50536c55ac80eea46a877f483ff25975", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_wenli_099_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f86b167d6aba81468470c77625e5aab", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/fx_mat_yinxing01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "36e41c9b599f19d42908faa85d10ad7f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3989/Materials/wings_3989_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1186015566da82c48afd6b7bd256afb4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3999/spine/wing_11_Material-Screen.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4a33082c3f08c0e4c95d4c3369606afa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_3999/spine/wing_11_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b58825f49b4522c4988bb502c374b49e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4400/Materials/wings_4400_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b5eb59184626464e8813a84e48ebd23", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4401/Materials/fx_mat_glow_b_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a4279c777549e7042b8025779b6c8c19", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4401/Materials/wings_4401_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b8eb8fca15eb86940bccceaf46bdc306", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4401/Materials/wings_4401_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e7b0ead3aa625e6479a1f8b250c962b3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4402/Materials/fx_mat_14402_wenli1_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94a5698e6908fe948809d5eeb4a676c6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4402/Materials/fx_mat_kejifazheng_03_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "52eb59ff72c50154d9021ec207f415a5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4402/Materials/fx_mat_kejifazheng_04_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e2e5917202306e4d80f2d5564a9b073", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4402/Materials/fx_mat_shine04_glow_a.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "958f8ff603ec8c64fa3366712adc32bf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4402/Materials/wings_4402_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f5ab842b3e28f14a893870da1b22f2c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4403/Materials/fx_mat_machao_chibang_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92360f3e79f63f644aeeb31f7160126c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4403/Materials/fx_mat_machaochi_cha.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f5ca86e0174c13a4387629ffb3394f88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4403/Materials/fx_mat_machaochibang_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ea7e424af2e4a2e4aa47f4e748c27dcf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4403/Materials/wings_4403_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d835dcab4e778f24caa868231b92b955", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_keji_001_10_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef41a66cbfdc1804a874687568154ce1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_keji_001_11_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "18f7f8446727cff4a918c6dd9660a919", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_keji_002_lq.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cd5f0f35741668e459ada9ae4a10c1a4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_kejifangkuai_02_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab4558c956e93ee49a6148669ba8a18b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_kejifazheng_02_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8bdfd2daf5cc0594b9c50e2bd8e1b9b9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_kejifazheng_07_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "66467f61e19729c418a749862d73f6ad", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/fx_mat_kejixulie_01_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7ce643b8d69279148a59380596ef655f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/m_kj_tuoweihuan01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5764fc3aa29d3ea45852b7d8173286eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/m_kuosan02_cy_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44ad0f9acc2e40646b833d97d595588d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/slzy_liubian_mask04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "73803fd525c97cb4eb803d6121052e46", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/wings_4404_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f98fd38068114f45b804c33ff5c2c71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4404/Materials/wings_4404_diff_fish.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3eae195748f009946995380590eb587d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4405/Materials/wings_4405_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5caad882f13c13e4499a3d574f8d9e10", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4406/Materials/fx_mat_jiqiren01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "204fd4bca1f7ea04db727d8ce1fb98f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4406/Materials/fx_mat_zjcb_keji01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f34060a1e9961f3459e63d9e33c22a33", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4406/Materials/fx_mat_zjcb_saoguang02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9cfcf7cfc9a3d241bd50709484e3bb5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4406/Materials/wings_4406_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c515d098fda64ab42a451d89fe72ec9f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4407/Materials/fish_yindao_shuoding07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "017f70f500885e04ba52e62d1220121f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4407/Materials/fx_mat_flow_a_d_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3f6b78cc5a70f4d4dbff536ccdfd83ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4407/Materials/wings_4407_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "feae8ce3bfe2fa54394c055cd42fad7f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4408/Materials/wings_4408_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dacb3f11d1412d4409f395ae159648bd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_jiang<PERSON>_path01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6a384b0f5899ede41ba2f4adcfed3fd5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_keji_jiang<PERSON>_ring01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "82b2aa00c03f7a942bca2ee4c07b2cdc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_keji_jiang<PERSON>_ring02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "481f26136a023624894575fc841eb026", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_keji_jiang<PERSON>_shan01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "444be4346dec5664bb9eba2bdc955dc4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_miaobian_jiangwei_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "112586170f188de469d7dc4673f33c27", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_riang_keji_lc01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "58c5cf3f00db3a241b0227a892167904", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/fx_mat_sanjiao_keji01_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2f71fe38362801545b1a0c6005ad1a68", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Wings/PrefabArt/wings_4410/Materials/wings_4410_diff_fish.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}]}