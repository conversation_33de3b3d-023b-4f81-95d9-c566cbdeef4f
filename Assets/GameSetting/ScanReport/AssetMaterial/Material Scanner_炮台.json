{"FileSign": "596f6f4172745265706f7274", "FileVersion": "1.0", "SchemaType": "AssetMaterialSchema", "ScannerGUID": "02dd222d-2225-495e-9a93-9eb7869288d0", "ReportTitle": "扫描所有材质球", "ReportDesc": "规则介绍：检测材质球是否有冗余属性，以及引用了官方的标准着色器、URP/Lit 着色器", "ToolbarTitles": [{"Title": "资源路径", "Width": 300, "FixedWidth": false, "SearchFiled": true, "SortFiled": true, "IsNumber": false}, {"Title": "错误信息", "Width": 200, "FixedWidth": false, "SearchFiled": true, "SortFiled": false, "IsNumber": false}], "ScanElements": [{"GUID": "e20a87510c770464ea683f145d3bbb70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/11/material/ojb_yuwang01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7923f907ddf6014e8720079ba0e4b59", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/12/material/obj_xulie_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "18bc6cb99b976344e99eb6adf98e9015", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/13/material/water_zmz01_jianbian__fly02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "33e7f05339debf84ab80bd64556e3011", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/15/material/trail011.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f70ba85428673c0469b50594a3340988", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/15/material/wenli021__5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9b6cfeac2d3ab0e41bf68ebdf61cc5f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/16/material/water_zmz01__fly.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "47efb90920a55ff44838c7d7eb876912", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/16/material/water_zmz01__fly01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "818a6c2f20032134b95f0fc8f6e46127", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/16/material/xulie_water_002_4x4_ab.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "77498da0e887fad48bb0b74082aeebc2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/17/material/trail010.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8eb640ec0de236140bb4c9823478fe8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/18/material/rongjie_lc_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3401600e1210ed345ac5a9c85eba5bca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/19/material/wenli021.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de54ef74701e0f249a7afaffc8b2c404", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/19/material/wenli021__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d98b4f9b30907f409f6a521bbbd20c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/lightning_zmz02_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a23978dd10b9c9c4d854e0adbe9ffc20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/lizi_30_add_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8ca88eb5faa9d045a3b902b7e15a123", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/obj_fire_zmz05_noise03__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a3534bda6c2aad6439e0d261f78259d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/obj_fire_zmz05_noise03__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b923489d264d374d94de953a9b68e25", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/obj_fire_zmz05_noise03__4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3d3d43400a793f4d84f777c454e108c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/obj_fire_zmz05_noise10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bdb57f55b2734e147b86785936c6a45e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/t_106_jianying01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "56d265f8c679f4d4d8ed3ad331840ba6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/t_106_jianying01_noise10_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "060ce3b2b556caa41bc068d3bd2d3184", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/t_106_tooth_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c9ffef8376140b6459b0b9e256def3b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/trail010_wenli024_1__7_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "778b84d70edbdba41ac90b308a405920", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/106/material/wenli016_wenli024_1__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2d386483d112aff46b39cd030bb14b98", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/108/material/obj_fire_zmz02_trail_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a4b4c6daa561114f8ec57275c279ddc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/108/material/trail_zmz02_2__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8b151b83c114e5429d2245072af6821", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/108/material/trail_zmz02_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78583f25511f1e64392ef29277f3fc4a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/108/material/trail_zmz03_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0f0aa3c250a52334d9b24d114a4ef87d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/108/material/wenli021_1_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a56c6a348ec038f41a3174fae4afa1e6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/109/material/obj_kehuan_zmz00_mask_lc_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a5fca0d6a324a34687dfa1ab6b4fdd8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/109/spine/cannon_109_Material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4c2cf27abfa93bc43906d63f0a735fd5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/111/material/guang007.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6c88f1049ab10984fb877572ac61f846", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/111/material/lightning_zmz02__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41c9949c607455544b71edb2b66dab16", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/guangshu_06_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9e4aefad7e3d47428bc2de6cb2dfa3a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/guangshu_06_4_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e36897883383f9843830e32bb2a4ee29", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/guangzhu01_1__6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a62f5b5114322744ba8fe4e920a5e58f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/shandian_lc_06_add_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b85910852a664574fbbb7fd8ddf00d65", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/trail_lc_03_add_dis.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "68f190fa2e3ed4f488261f70bc698a7e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/112/material/xulie_baozha_lc_05_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0be6794a32a99d043b8f0fb4504b4d53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/obj_longgui_zmz02_1_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c9897299808f194da03e15c0d4f8a59", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/shitou_lc_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c73047b1161a9854688060241464ec15", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/t_bullet_114_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "642c1a14c0d6a364b85c5218cfa2e97e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/t_bullet_114_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "addeb21a5e31e224da2b514497964a8f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/t_cannon_114_zz01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "edb4ceec68dd02d489d5d6913a8d56f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/t_cannon_114_zz02_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de74e2c12c256a848a7d3e5207d02f94", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/t_cannon_114_zz_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "91203fe9a2c1cc34e88fb180db98c11a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/wenli016_water_zmz01__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a54994d79cde224eb0ffa5aca0167c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/wenli024_1_obj_yuanbaidi_00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c3ace65b8843a8d46a536dca3b8ee4d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/114/material/xulie_yanhuo_010_water_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "399b3193acd842d4d9e56b6fabb1bce2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/bullet_115_wenli1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "06cdf8013ea78d242ab35ad1f8c4b08b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/bullet_115_yumao1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f793a73eaaef32844bfac069202a205f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/bullet_115_yumao2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0117d954157957e43883498539ad911f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/cannon_115_wenli2_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a6f135aa5067e64cb73f859a58c4698", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/cannon_115_wenli2_violent_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43296002ab833694cb6c2a4b7ac87512", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/115/material/obj_fire_zmz05_noise03_cy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6bcab4f327f23554caa8de75bfe2efbe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/guangzhu01_b08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2eafec6249ffac148ab915689791d945", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/guangzhu03__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0c56df25acc25b54e87fd0c88e9ba5ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/noise10_mask_116_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e1f78896f4dbd754bb3f945a144a9683", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/noise10_mask_116_1g.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "856992acac2050d4c9db4696df78288b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/noise10_mask_116_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c98960979daad5c4da790db3d6165929", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/trail_zmz_09.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d0dc29fcfe99ea499b72f5f4ffa9ac5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/trail_zmz_09__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d88422247b8ba4d4bbccf2de38a625ea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/water_zmz01__fly 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cdd0423045b5e4c4f880eaef7ed310cf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/water_zmz01__fly03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44261bd567a07934ba7e1fdbd194ca8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/116/material/wenli024_1_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1d433586aed584e4a8296ed063b8f3db", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/glow_lc_05_jiguang_zmz04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "352f408bfdf92344e840afb261759f34", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/glow_lc_05_jiguang_zmz04_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ebde96eddec85c74e8a5574c80cdc4e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/guang006__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "764cd10bc9a5d354686ae9c4da1f7329", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/lv_mask12_an_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8fcc7a89425e0b641b05b97b1f710ea5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_bullet01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "218165322ee8e404b980326412af57f3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_bullet03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3b9552bc768f2b141b47b65f67413461", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07fe132d5612b0c48ba4d31c3d26b811", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light02_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "863083e1e23fdf4459cb4365fd6d423d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light03_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "769621b90fca0264488d7f44dc6327d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light04_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6600dac9047d3384aa7f6057d4e9c153", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light04_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f4c1dcfa60fd76418eab4b0397b1290", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/t_117_light05_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6687678021247544a85a8a6f9aebc4d9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/water_zmz05_wenli019.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd292c0955370cd46bd21521f7e75796", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/wenli026_guangzhu03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "efc4ca309ebe01c49b1d78d54d9497ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/wenli026_trail_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b8947eccec6f58c4fa7eb3fab75a9c09", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/117/material/wenli026_wenli024__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed8d39b0f266a544292303a35b1887de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/t_118_jianying01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79ed94ec34ca4a8469da17e9d4335314", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/t_118_jianying01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f055904582c0b1647824f4339d110527", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/t_118_jianying02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b09c65008135b8d4887c55d9a618d1de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/t_118_mask_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f27f3e6a46f6f8d4d9d7c2de14323ba2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/t_119_niu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e68bc02ce98ba4746b0728bcde8c74aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/118/material/trail010_wenli024_1__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca574f0dada17d04fa053566d3e57579", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/119/material/bai01_wenli_zmz01a.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de3f7db89ab3db24ebf2483114fbb0c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/119/material/bai01_wenli_zmz01a__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "926ab22d5f5bbe44c8cac80668f25a88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/119/material/huan014_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ddcb1f382065c4e4f8ef3e8bc53dd347", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/119/material/t_cannon_119_mask01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c1c8579619f33a94c870165a3b3a1eaf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/119/material/wenli024_1X2__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70e327ebe7633fc45bed399f27e18da7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/120/material/cannon_120_ice02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "258dce4c5ca51b648babeba9fd08de1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/120/material/cannon_120_ice_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b77b58bea10a9b6429224aba2de07c36", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/120/material/trail010_wenli024_1__4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b7651eb828826194a83c6e4161120fc8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/120/material/trail010_wenli024_1__5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b112a96cda1a6c4ebda1b531b1347fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/120/material/wenli024_1_mask_120_ice00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "572a760d838a9164e8e5fdb514565a1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/121/material/mask_hunting_button01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fc4b3b68e7e5709409720c55ebf8e2e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/121/material/ojb_zidan03_2_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "024f78fb191d84a469b811c66c0d2b07", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/121/material/ojb_zidan03_2_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d5048d9350e254b46be9a80b601b3dad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/121/material/trail011_dis04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd2e40292c08d1b44947c8b640e3fe08", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/bullet_122_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1744c41663fa8b74eb84e65d0e36a2df", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/bullet_122_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6118930addb338948a6afc3f599c520b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/cannon_122_1e_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "447bb4fef5c35014cbab8db950556876", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/cannon_122_2e_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eddbfb48d96701a4ea0fde8bec3b8034", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/cannon_122_3e_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9dfab290fd93e864ca46632b9a3ca863", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/cannon_122_4e_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "17aab98dba610c343bf7bbedca33b68f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/cannon_122_6_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4005855712c85444d9b05d9b2e715b70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/jiguang_tou_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "308f7d506f91d09449a1d6337a4b64fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/shine_zmz01c_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "efeaddb130ff4fe45b4dccf69605cc7c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/122/material/trail_zmz_10_cannon_122_4e_mask_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "626670dbbf86a6a4d8588b5175bd1918", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/bullet_124_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ca5e1e082e02674b985462f4d265087", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/bullet_124_1_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2a3df6c4f891c3f4fbea4a83bd75e800", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/bullet_124_vio01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fcc3dc2a6db794946b5ffad87aed9d5a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/bullet_124_vio01_noise10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1a06d8255600d6a4e82c32e8a1eb6626", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/bullet_124_vio02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed674d8f3bf6ea841995a4af08e896a1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/cannon_124_mask01_wenli024_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e66cc6b76a208643859d6c5fd995bb1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/cannon_124_mask02_wenli024_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "10d0263f8f9300847b71e5cd51f034bc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/124/material/cannon_124_mask03_wenli024_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4d81581275ba0d74f99297c0d5354f98", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/125/material/cannon_125_effect_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db6e1f650a3340f48bb5d8043e029383", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/125/material/cannon_125_effect_mask0.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "591976897a5bf1f44a674fa564684186", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/125/material/cannon_125_effect_mask1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6563d9a9852e7a540901541da9d1c500", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/125/material/cannon_125_effect_mask2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2c103e46c425f1458ae0284c0da0279", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/125/material/water_wenli_zmz03_t_117_light03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e34a4f4fb4356ed40b69806fa7132a20", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/bai01_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1c034e4ca4090f244ae63ffa7dd2f977", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/fresnel_bullet_126.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53f3565b35c7c094aa35b69148735dfe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/jb_water_public00_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b13e47462ebde3b4d8e0ab2fd5468c30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/jb_water_public00_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c195ede5496e4446b1a7138da911b4b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/Mask_001r_mask_glass_yazi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bcb34660ad11280428b59a279feccac6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/Mask_001r_mask_glass_yazi__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "69d77988c35a6064ab11a1b7995b7e54", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/mask_zmz_01_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec332ab733081a1429f74c3c22feaff3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/water_zmz04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a56bd22f6a6cf74ead463aed3884c08", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/water_zmz05_water_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "19a508d90372ebf418db1559a57a4629", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/water_zmz06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "70079f64d68bc3543a30a5fa6d6b664a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/126/material/water_zmz06__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96c91aeb7964acc46bcdeaa554e527f9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/127/material/cannon_127_material.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ca86c8ce3fa2c34a9c5a09914d197e1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/127/material/obj_fire_zmz05_noise03__9.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1af0fea81b7090f4faf1653cb860d1ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/127/material/obj_fire_zmz05_noise03__10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a281e35365a6dd40a9614dcca92ff8a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/127/material/t_lx_zmz00_t_lx_zmz00_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa82d92153863804eb3d6a6a8a208579", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/127/material/trail_zmz02_2_noise15.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37979e4e0d4a0884fa1509c63689d42a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon130__smoke.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ecf482e5509bf854dbdc09097bc12b16", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon130_bullet.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "84831f554f660044d9bd0c275334b5e1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon130_bullet2_fury.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4cf1cc33aaa27bc499b3a89a0308c62a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon130_bullet_fury.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2271f8d91e8c80e488e9b9d5e732e78d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon130_zidanwenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8f2646800917527458388657f9da1c63", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_1_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b217717318353f4ca14f09f06da5615", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_2_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6a619570919e1a446ae9ca9ae81fd38a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_3_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29e93732485b09449820fd3b670d1523", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_4_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d22b232a9d30810409f14c066a577cdc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_5_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfdfae4a464b6dc4eb3f007360e63ab9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_6_guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a6f5ecb82d4b34c4cba35f55c0af9fc0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_cannon_130_mask_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02157caa60d46bd41afd277289ec13c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_net_130_quan_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4a072fb85a9b224e8409799e18faad8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/130/material/fx_mat_net_130_quan_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1cbd39217dfb4ff4899f8fc833c9edf7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/131/material/cannon_131_114.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02f45ce41ac54044b85adbd1fb8ccfea", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/131/material/cannon_131_e01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ecad9ad2b52400845bd60f3211c6501e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/131/material/jiguang_zmz04_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "124e7ef2eae913646894b50022f1ec36", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/131/material/t_lx_zmz00_1_water_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "65d69263e6a1c724eaa0975db403625c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/cannon_133_mask02_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "251091be3c7d4fc469030587e3d9fa37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/trail_zmz04_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20979d15d1642d3489a071c4a66280fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/water_03_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "275ad56fa0f161448b2b837f8db19e4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/water_05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b702849b81ef6c9468c99da3dd4b0ad9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/water_09.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3525cda65ce44224696eb0fe68a2ba0f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/wenli024_1_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "394a380d95e81464798362f974900629", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/wuti_chuanjiang_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "989b8b8699139fb448ccad493d5f2665", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/133/material/wuti_daodan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb7e79946ab4ad84fb925b85ad311bd0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/fazhen_cy_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "102a5049f05346041b33c7c2024cd273", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/fazheng_lc_07_liuguang_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "436bed8610a74134aba792b232148077", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/vfx_fazhen_taiji_bb_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dcc3766a1db46094b8915f95cea6e58c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/xiankun_fire_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "074ea007c16b8f9429d9429013e479b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/xiankun_fire_003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1850540475c5bbf4a8acb196439a0008", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/xiankun_fire_bb_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "73cb85543880de24f97522bf53bae6d6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/135/material/xkpt_battery_h.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4a9441ae3ae887c4f913c286e6c2931b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/color_jb_bh_light001_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8cbe4d606ae5a35419227c25dab6c30f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/glow_008_add_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad0b996cfce62e34f8c6387a268594fc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/jl_liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "197ca723394205f47ac77a59e16f03a4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/jl_liuguang_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "521ffb159dc02304c94ec83a45d310f7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/t_net155_lx_zmz_shine02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d135d5c02ac87f84c96ad34ece395ab7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/t_smoke_zmz05_1_t_lx_zmz00__16.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e19e414f8e2156c4d9f13ee5ae2e12f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/t_smoke_zmz05_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "849323486456cca4c85d45a742528dcd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/t_smoke_zmz05_light005_7c02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d952a0d25e436a943a6ec2ec87832a72", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/137/material/wuti_lbs_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2cd6e598a1bdacd49954b654d3a2a7e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/138/material/bullet_138_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "467dad9e38ae85743ad81a222ac2ad48", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/138/material/bullet_138_1guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4c472037ad8eb8247968530bb86f9201", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/138/material/bullet_138_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74396cfd4a5d2424599b130f2e3483f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/138/material/cannon_138_22guang 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd704d3d85a9cdf4f999ed4d59ce8f6f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/138/material/fazheng_lc_04_liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "89c937e82861ae945a8528c916a26a8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/140/material/cannon_140_mask03_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b996321ca8196d6479142d5c67626685", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/140/material/obj_huoqiu_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0fb3fe4ea2707d746b1f361fbd9e7547", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/140/material/trail_zmz02_2_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4dd7dd61a72091e4d8d9fecabdf25490", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/cannon_moyuan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a4ed4e8241f045948a00b546489ac08e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/cannon_moyuan_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63dde8c78956f04459d228be8e89abc5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/jb_zmz02_1_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfec24ac0a36f3e40923620e1824dc14", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/jb_zmz02_2_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83d769d5b30abc84c9ad7d92bddc7f0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/moyuanpao_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "edb6b39acc178d949a9d82bbe541408f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/moyuanpao_mask02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd1ab83c54da30148bc1f2224c26e565", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/moyuanpao_mask03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b8ae0a1a020f3247b7cdb3216aec0c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/moyuanpao_mask04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29e75ea43dbb4e24d8f055de4991b300", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/moyuanpao_mask05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b1d6a6f2f57c2034bbf7093f4f9ee53b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/141/material/xulie_yanh<PERSON>_011_4x4_diss.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20d7eaef266c5734b99875b36e302f9f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/142/material/cannon_142_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aff7e4c604707bd4187c86b12d53ca67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/142/material/cannon_142_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d763d80498c2ade49ba7245070b312ef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/142/material/obj_yilaguan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d288c272aef42c044bc062ae1fa0ddb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/142/material/wenli_lc_05__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6307476aef93c6942ab0c4573c72dc9d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/cannon_144_3_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c8bdb0d7f6361704f9978d8e9c556c9e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/cannon_144_4_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b37229ee0169b749828b9d3ed171240", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/jb_bullet_144_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ede2530c9a2b484f8ad636544419d34", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/lx_zmz_shine01_shandian_sigle_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f17abf189f8ff6242a279514f896817e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/lx_zmz_shine01_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63812efa60cab5d439dad4a76b8b50f2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/lx_zmz_shine01_wenli024_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0bb932ff268060f43bb36eb71c01e265", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/lx_zmz_shine01_wenli_jingge_lc_02__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6495b1b4f0aa9e3498b3d04ada860484", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/normal_glass01_cubemap_5-7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f42f58e153192f438c53d1f61242814", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/t_148_jb01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c0091d8baeaade84ea60bb3dae9b1b37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/t_148_jb01__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8255e359ca4d2354b8de904404bc3b4f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/t_148_jb01__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59e5d4121ccb7004db3672dab5625e0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/t_shenv_glow00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bf1940de50e52ae4d9df8655abc43f14", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/t_shenv_glow01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c25df7def9aa3614a8564826d269d56a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/trail_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53b16d218199a784abad2c1faf64b868", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/144/material/wenli_jingge_lc_03_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "024933bc97978e141ac8689ae2415f87", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/145/material/glow_an_bb_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74d083c2dbbb0f14abc20d01d2c48f7c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/145/material/trail_zmz02_2__10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0dbc54cdc74baf745a51f4200705f169", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/145/material/wenli027_gai_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e6c3d98515292844cb86affd86519be7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/145/material/wuti_zidan_01lanse_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9e051940057ffa048bc7b5ebfb495fc3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/shanguang_ui_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d51bfdd09497893449a811c1ac94d785", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/t_lx_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f1ffb657ea01d6489dbb44300ede94d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/t_nangua_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a0b6fcc4cf8398848b419d15608496de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/t_nangua_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02ba99b1f03c84142ad6900edcb46124", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/t_nangua_02_wenli024_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cd313160107355246bf6d84e0b92f199", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/t_xl_face01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7c97fa19308d3140a03c7fdb0e524d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/wenli_guilian_01_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d6596d45021771c4d902e2fddce5aa23", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/146/material/wuti_xiaoemo_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e44f91dce226d0e4296025ffa2838cc2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/bullet_147_tuowei01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a98d915ebadff643944bb600dd39b9d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/jinniu<PERSON><PERSON>_wenli_7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f189aef90667794893c735461d565fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/line_lc_003_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "98ead3c739e4d954b8e354212f207a9a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/wuti_xinggui_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8dfcfa87b7aa88e40a7ecafa7197cf60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/wuti_xinggui_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "be476118be6628c469ed1edf4014ef9c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/xingjuepao_mask01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa10112303ac7684e86b98694b00ca8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/xingjuepao_mask02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01b0630eecabfa7438f3addadb7932be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/147/material/xl_bullet150_zmz02_wen<PERSON>_jingge_lc_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "53cc7f11e809c924c84d71807ced18c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/bai01_t_lx_zmz00_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03d4027b2c3cabb43aac330488db0357", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/bai01_t_lx_zmz00_1__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2763cd17f5d7e4499e7596b490c18ce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/bai01_t_lx_zmz00__13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39915920ec18d2748ae6c0aa1320543c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/jiguang_zmz01_cannon_148_3_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bde4d03b382d61e429afc177d995ffba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/jiguang_zmz04_cannon_148_2_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3fcf89f3e2873c5438d2199c04a574e0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/obj_148_sd00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a40d96dd7341d1f4a939e83fe8aa1edc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/obj_148_xueqiu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e565b990a1bbc43409f4adbd92a5336c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/obj_star_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0e6e5d421a59fea418fa39e2f206cfeb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b07c36c86719c04db3ac7e0041c3c37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/148/material/t_xl_gift.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4256d20ae2851384f923b952f62b702f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa40be52cf494534991ea5cf574e0a86", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4154fd1e1b25e5d40a302c704c63298d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz01add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ee4a6f9703e8b194eab989c1ccd0ad1b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f9a146f9fabd36046b4c534a2f385b01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz02add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79ea82144bdc2a24d8bcb6e69766cd33", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/149/material/t_bullet149_zmz05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b910fcd3a37a2294cb9abc5191041b52", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/cannon_150_2glow_wenli_jingge_lc_03_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9577e9697c40eb45a5e71d7dbf1b0bb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/cannon_150_6_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b7150a411c6d4147b134e61949aedf3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/jiguang_zmz05_zi_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b87cc0358c72e74493b423541c4aad5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/light005__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7e5a3fc1cca457b4b8c05cf94f993eda", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/mask_zmz_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa1e4602c6322ef41b8c394a436afd76", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/t_bullet150_mask01_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "62eb88f9c7d18e143b67bc0c819d5418", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/xl_bullet150_zmz02_1_wen<PERSON>_jingge_lc_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5bb751a1122ffc744a70cdb02c81b8bf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/xl_bullet150_zmz02_wen<PERSON>_jingge_lc_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9acfbdd0b55dac244813d2f8b12ae3f7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/xl_bullet150_zmz_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cf2ad2d7fa4ad25418032b1638906a72", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/150/material/xl_bullet150_zmz_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78c6e9046658e034b970f8e97816c30c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/cannon_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24f862c59e9788941a25220af06f6c0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/lx_smoke_zmz00_1_t_lx_zmz00__5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0acfeb8ad7001754899c0fd7db3ae5cb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/mask_zmz_01__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "47440735c2305c04480efefd0c3dfc0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_bullet151_mask01_t_fengchao01_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5437866eab3614044a20fe4874d5cf7a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_bullet151_zmz01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "450138938344c9b489d7100255bfc73e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_bullet151_zmz01__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ba0a04c48b4e1944a93cf1574ee1b0c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_bullet151_zmz01add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fb5d1501533cf8b4a902e9dd8a8d2a8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_bullet151_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e00f5390f66f44f4b82e639469ad35f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_cannon_151glow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "708bd06294d802948b21c59769213f84", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_lx_zmz00_t_wings3950_1_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb384d0085dc91b4dbf889cdf6ba76a9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/t_lx_zmz00x2__8.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f2fef81eeca94724b95743bc72726cf3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/151/material/xulie_fang<PERSON><PERSON>_01_suduxian_lc_02__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "87d3520aff8d9204ab82a5d64b069c2d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/152/material/bai01_t_lx_zmz00__15.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cc60dc1ad1003fd4d9b3daad24d7ea01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/152/material/cannon_152_fazheng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "caf52b0d4bf0f8b48a64f50416654c4c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/152/material/cannon_152_mask01_wenli01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c114e5b89767de246adcca037dccbb01", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/152/material/cannon_152_mask02_wenli01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b17b9e78d687044d9727b22fdf56d1b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/152/material/t_152_szt_wenli_lc_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8eddf004254bb147b6fc116d95d4ac8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/153_mask01_2_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44000afdb5f93514fadc6aa03e5b7ec5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/153_mask01_3_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "914d26c83c777ca4b95e6e9eb93f7d13", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/153_mask01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbdcb75f29bdbcb4e876415ed9620c87", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/bullet_153_zidantou.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b667a8fcb438df54b965fb9655145f91", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/bullet_153_zidantou_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd34fcdb5e926ac4eb38df1d929f977e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/net_153_dahua.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9cb821066f6faee4cb572898cd967ba7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/shandian_lc_02_diss_cy2_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7d200e283ab58743badbf202ae7d395", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/t_bullet149_zmz03_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c8426bdd4ce0b54782fe69aa5779ad6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/t_bullet149_zmz03_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8fd14a4e36f327046b86234f05804224", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/wenli_zidantou_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9847c1a88e728db45b36fe43d44e8a21", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/xulie_baozha_lc_01_1_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "36cf1159a570c2f4c910aa68c83bcacc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/153/material/xulie_baozha_lc_02_add_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f878b87432e4ebc4d9669745b5ec0616", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/cannon_154_1mask_guangzhu01_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b85967babd59c24191b2dd79fba402f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/cannon_154_2mask_guangzhu01_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "363109f551c08e64e9d3312e388b5ba5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/cannon_154_3mask_guangzhu01_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3733f348ab639f34a9e6958d6f257081", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/cannon_154_4mask_guangzhu01_1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d59e2aa56d922ef4380a19e93a4f7cc1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/cannon_154_6mask_guangzhu_lc_01r_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5bac858c45397854d8ae2f4e3e20e860", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/lx_zmz_shine01_t_lx_zmz00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ca905d7ce051874080f723c09cbf060", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/shine_zmz07add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bfed6b10206ea874696b7596df278fef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/t_bullet154_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41959eae1702154419f52d938f1b63a8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/154/material/t_net154_01_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b84e1626331bb1742b6872f0772e064e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/cannon_155_2mask_guangzhu01_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "52f9556bbe69fcc4d95d8125c1d18253", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/t_lx_zmz00x2__10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b5fd2c3657bacf14da6af38c85cb35e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/t_net155_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d80ba026c8020784da8b0cb0e5523ca0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/t_smoke_zmz04_t_smoke_zmz08_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c4731f59a3891040bfea3cbdcfe473e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/t_smoke_zmz05_2_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93e5ea7d3cb94aa4b9330fd301278ca3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/155/material/t_smoke_zmz05b01_t_smoke_zmz08_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "31e7be93e800ce3449df3331af116664", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/juxiezuo_tuimask01_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "529e899e999595e42ae080561ef235ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/juxiezuo_tuimask02_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13ba2ef21d0997a46b35d78b9b2011b9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/juxiezuo_tuimask03_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db52689dadbc01e4d8723e1623e63bf0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/shandian_02_2x2_g 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc97634e0d8192c4abcd6f7293378e49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/t_net156_01_lx_lc_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a7c7df1f11d5e204780870790a666fb6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/wuti_xieqian_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2398c7358cc478649ab079cc22386058", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/156/material/xingjia_wenli1_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "499045cfaf08a6f43989943f69948161", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/baiyang<PERSON><PERSON>_wenli_12.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "036a32ed6d3c50d49aebf6e18e9c7604", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/lightning_zmz03_lx_zmz_shine01__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97389edd808061641b92ecb7997484ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/lx_zmz_shine01_2_lightning_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "687952841cf3e71458d3907b18d2f8b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/lx_zmz_shine01_path_014.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2114b16cd337cb4eb8012e14ee66af3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/t_lx_zmz00_wenli020c.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "102cd3c1484f45e459c0fb49cac6c083", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/t_lx_zmz00x2__11.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "04012ec7e95be0449abde64e9c2d0506", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/157/material/t_smoke_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bdc2db2e5716b1c4c9b12632c22f1247", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/bullet_158_core 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ec864e1f7b2dc974ca6bad23943dcfa3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/bullet_158_fury_core_sapphire.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "214524aa8427a1047be15970dc8220ae", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/bullet_158_trail2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "30f71237977f7ec46963e552211b72d8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/bullet_158_trail3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1a6d2f26ab1b1cf4e92a409f0e239b78", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_ear_l.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24344462d544a334f90d849f2cf095cd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_ear_r.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83df618e0a656ae49bed902089af4a1c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_front.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "34918e5f909ca15408d259ff88e127db", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_spine_d.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "94a479fc25dfae3498ef5a1c3b05b152", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_spine_l.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40accb9e155fc0d4ea09600d5983cd0b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_spine_r.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "67beb151e0e5e574aa74e01c562ad6ae", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_wing_l.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03bf94c314d336b4aa0e1c5657403ddb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/cannon_158_mask_wing_r.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8fedfd73ce236740a2ed8c9f1da5a5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/net_158_fury_firestreak_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c4c0f7989a21589478be33a5a27c5a1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/net_158_fury_wave.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d3835d96d334364083c6ab95509c31d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/158/material/net_158_wave.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7099bfb72b245fe4da09518150b3260a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/159/material/158_4saoguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74dd182dfe26bb748aad4fc69d4116c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/159/material/159_saoguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d321bc18cdcf60945b35858f90e67492", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/159/material/fazhen_hy_002_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3607ad50fd9dbfb49b335feda6b42ea6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/159/material/quan_001_g 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3580e9dc51e67b84a8fb1a2bbf322c70", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/jb_zmz02_2_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "471df2ca09c30ff47bd576c19f6ddad3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/jb_zmz05_1_shine_zmz01c_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4df4026d911783b49a3e13af706a01f7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/lx_zmz_shine01_fazheng_lc_11_2_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ad8f7c0e3215e64884e8427c73be922", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_160_bullet01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "426c0f625a1a6774cba8d2d8cdedde88", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_160_bullet01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5264e58132969874683f99790a5d3fd7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_160_bullet02_jb_zmz05_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "72cb92dd0fa1ea84fb9f7b20eb16c17a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_ice_glow_t_lx_zmz00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "530e696614f93d640a10a04df36d4546", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_ice_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2cf1a1a3907fd9e42a1a66793b04ba16", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_lx_zmz00_1_t_smoke_zmz08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97e6216fa316e3642b0428b624dab55e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_tianxiezuo01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "674b09db099720646a903d64adfa5c56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_tianxiezuo02_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dea7f06c730ccdb43aed4b09a4c56cd8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/160/material/t_tianxiezuo02_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5447fdc75a2cdbc42ae731f3c4a59808", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/cannon_161_1_glow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0f0a639f5cbded94eb54b8d76fd21d81", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_mokun_lunzi01_lizi_fenmo04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "864c908f9a79b544eb609cdcc9f9c123", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_mokun_lunzi01_path_014.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8bdd3a3c0606d1e4babec28247417e0a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_mokun_lunzi01_trail_lc_06__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "25ddf5763697028448c6ff682f1be59f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_mokun_lunzi01_trail_zmz06.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b5161807d772fe74f82f845254ca4f4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_zmz02_2_t_lx_zmz00__4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8407cc59253bdac4fb4b0fc094da70d2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/jb_zmz02_2_t_lx_zmz00__5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e8d815b3b7512044a0fdc31718a461a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/t_sheshou01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f07006ca6669d2489578779170efee8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/161/material/t_sheshou02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6c18451f10862464ea7071965d980fc7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_2guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "76d74ff140d3bed4a9b539a7af4ba8b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_3guang 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8010cf98e46e5a64aaaade1d36cc4ad4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2f3891be5d834f042a5972d4b467bca1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93a6ae47e1c151944af653d8555b9963", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_6bian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a681a230ae35a294ea7e9eb5cefcb811", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_d1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9dbec72a40f9257469d41dff23ea68fc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/cannon_162_d2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3be35870fd335b448304fe03209287f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/net_162_fury.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3dcaeadd5561dea4f83b14672f2bbe07", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/net_162_mijie2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f4efac3c14f188044b60ca7913f23635", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/162/material/net_162_mojie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ef77eb9f71d50948b413e94f09b1ca9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/163/material/cannon_163_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa85426b6bb9e0d419f60785136bcb6e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/163/material/cannon_163_2shuijingguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5f9d71cd83d1f854892d380763fb486f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/163/material/cannon_163_3guang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "11b051d7bd3510e43987fbf2deb756f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/163/material/net_163_mijie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "37cab4b94c9f41f44ad6e0e22f8ea8f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/163/material/wuti_shuijing_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41e0d00f720176f4493cb9e1e36bcfe2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/jiguang_zmz04_t_164_mask02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1234669a94a826945a8f71207c30ea75", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/lx_zmz_shine01_t_164_mask01_t_smoke_zmz07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b79132dee5cb8b74aa734d4111b9bc51", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_banqiu01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "984ccc01ca5a8f44fafc4a844172350d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_lianxian03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f40b1766ba348234882bc51e34729649", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_lianxian_sanjiao.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e996790d58e62fa4a899858b907384ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_liuguang01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4c16a378a55aaf4458a8d7896ec852f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_shuiqiu02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "41dc3481036f1d6468c55e0e8db79543", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_smoke01_loop.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "edd21d298bf25844993905ed959c1773", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_toutuowei01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0eb6926dc87359f46a9b39fb29aff69a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_zidan01_sy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "382b2527e3a63be48bcfc73b70a1e61f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/m_zidantou01_all.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce391f5ffd8e83547bb3131624c91e63", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/t_net155_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eb61743a1f9eee04aa6fc8f929d4c69f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/164/material/t_net155_lx_zmz_shine03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "158c4cca774c54a478a2e21ba9c2a701", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/light005_9_t_wuxianpu00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0dbedfd6dec390a4faa5cdf36dab8d58", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/lightning_zmz05_water_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3ed15be739c4794784012284a6d50c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/lightning_zmz014_bai01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "117bf206cdf207444bf7ccc8b4399978", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/t_lx_zmz00_1_water_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "596dab310a1f88f4d87e12efa9a7942e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/t_xiaoch<PERSON>_01_jb_zmz05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de9c486281d7da844832be69ad53b6d4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/t_yinfu_01_jb_zmz05_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd84f31e84f932f4e98a2cf97b2e271c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/trail_zmz06_light005_9.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8dcac7930967e14f82b27f1ffdcfff5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/xiaochou_xiaoqiu_02_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e52ce1ae7b4dfc74288dd872c720596f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/165/material/xl_yinfu_01_jb_zmz05_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5f4f07a25b673e949a67b65cfb7db423", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/cannon_166_1_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "31e9b2fa78bfd114aab74fe8165f525c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/cannon_166_2_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cf76a072cf7bc5849bb52206ae8621e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/obj_ice_zmz05_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e01e8d0d4a0d5124a9a03dd36da9d5fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_bullet166_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6260ca12de395f14783ed8883653b6b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_bullet166_1glow.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96637022de6b04445911adc32aa4ae24", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_bullet166_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ee975df100616e44ea6ccc44d9de4d7b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_bullet_166_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfbef876a5d71ba478c47149f42363f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_lx_zmz00_1_t_lx_zmz00__13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b021c3f147d93764cbfec9fafc67c19b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_lx_zmz00_cannon_166_1_mask_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6d6f852ebd55dc448facd36f024106e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_smoke_zmz07alpha02_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b3b23c60e29fc344889b207b4ff45707", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/166/material/t_trail_zmz05_t_lx_zmz00__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "934985812c113b24185558dbc8472f1a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/bullet_167_core.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bdd36fb44104d93419b1e460ddd749e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/cannon_167_1_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "908a3adde65780c47ace685696117768", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/lizi_005_g_why01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "235faecb175796c438994305a1bc79c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/net_167_fury_elephant1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a82b6952e5c204f41b41aee0fa34a5ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/net_167_fury_elephant2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce8f0324af094e54baf79d2d53a73c8b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/t_lx_zmz00_cannon_167_1_mask_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09f440eb3ae3c524fabf638a84770857", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/t_lx_zmz00_cannon_167_2_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ba68542b3f630b4f81c5bc6977a038f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/t_lx_zmz00_water_why01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eda5c4ad8a858ca4a94f8005ad96bdb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/wenli024_1_light005_1__1_1_why.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ace5525dd64c4f44399d50787f3d7896", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/167/material/wenli024_1_light005_1__1_why.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bbaed8a53edf1c64eb3495f94270632a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/169/material/fresnel_net_169.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3358f5844daef7a468be20bf7ece9d4c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/169/material/guangzhu01_b07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "143c8fe4e02666a4a8bad3d11af0c330", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/169/material/light005_6_lightning_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d12ec58dcd09323408bd7d6100bf3792", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/169/material/lightning_zmz03_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "06622c5007545e040ab9ab927718e4aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_bullet_170_mask01_t_lx_zmz00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "09767cf49541570489d1ae4fa834fa64", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_jinli_yang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3868c6b918e812c479e454076fa8ee47", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_jinli_yin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a7b976fd5c59a814fa4f983f057be7f0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_jinli_yyy_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a59c0de8a94074c418c646b52f074876", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_lx_zmz00_ef_mat_heiwushi_9.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d624e595565cdf45828c5fdf593cf53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2acace0d615574f459c6c73dfb0a60f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__5_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3be5297b3004d6b479d6e09dff90ce06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5fb071dff912f3c46adae0801f382ffc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__6_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13f175c765c3f1b4da8acb9091c0a63b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "577f08e7c55c9804ebc4f1f99f6a603e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_lx_zmz00__7_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e72c113da0924dc43ad5fa43af697027", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_smoke_zmz08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2e212cfacbcd6c46b02d264b24033d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz05_1_t_smoke_zmz08__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b8a7971171cf69d4fb006e599634e3a2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz07alpha02_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5e70b80b5bef03448b5aa67f2c8ea0ef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/t_smoke_zmz07alpha03_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63ce5019930ceb04fa743ca4d1a8b50f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/trail_zmz04_t_light005_2_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d513500b4e028684cb472eff53ba7da0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/170/material/wenli020_1b_t_light005_2_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "711acb275d29b7e4ba5f91262cfc8efc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/lx_zmz_shine01_guangzhu033.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08f6d16b51c233e4bbe5a9b1c66b860b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_lx_zmz00_cannon_171_1_mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5076308f736387246b664e89a6a67bd7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_lx_zmz00_cannon_171_3_mask_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ead888c53d7845445973c8628e197c29", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz05_1_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "71d796af91b79c24fab2ef677c960db7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz05_1_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8e61295ba65bda0408c5dd411af023a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz05_1_t_lx_zmz00__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7934595eece97914ab299bcd4b40a3c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz07alpha01_t_smoke_zmz07alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0170d082a576266439da3cc1d008bddc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz07alphax2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d17b969e3c5c0dc4698a7045911c7c7b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_smoke_zmz07alphax2__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aed9d2955b3e471478e617ecaa767738", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_trail_zmz05_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "03e11da9a28a55540b304135946b0fca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/t_trail_zmz05_t_lx_zmz00__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a2cc1847ec0357f4bbff6196e1920dc7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/wenli020_1_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6ccedd2956a8b0842a9a37799afc99ca", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/171/material/wuti_lc_aixing_01_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ac3d50230fda7848babaa8a01ca3060", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/baiyang<PERSON><PERSON>_wenli_20.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "62a06f32aaee9fa488fefed9afcb9dc5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/leilong_ring_rongjie_cy_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63ddb640671b12c4fbff7e7ff7e07315", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/trail_lc_13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "114208e14cfbebe4c8725b96cb9ec337", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wenli027_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b16e6a66b693a3247aa1da49a273410f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wenli027_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "633ae13c069a7d04fafdb3c1b6db0a5b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wuti_hehua02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1dd63db8aa34f204b982c192135dec3c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wuti_piaodai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d27fa04d466c314f9c537845766c426", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wuti_piaodai_02c.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07553149ea14685429ebdfafa17d7e9e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/wuti_yueliang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a04ae60dd176a643af2875f60196d5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/xiankun_line_006.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f4a4072ab20e5e545bfda7e8199cbc4b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/172/material/xiankun_line_008.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "670161aeae28bfa4f8ae9ca2a60219a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/bullet_173_trail1_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fbdadbbddc3a8ca4f979b2047e6b1350", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/bullet_173_tuowei_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "817aa9655a96e924888d2cf7615dbdb5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/bullet_173_tuowei_add2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b45ebca55157ac34bb0d9b070577730f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/bullet_173_tuowei_add3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e4caaba47d5cc64a8cc0c05dd36a123", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/cannon_173_1mask.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "36c00b79903620c448cb858edb8d12e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/cannon_173_7_zuoyan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c45b19777b2b8e41bd59f64cdfca581", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/shandian_lc_06_add_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dcaf79b8fdd8a4d48b3f108af077b179", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/173/material/t_baozhu02_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "945c8c53f5d93984c92421e4cf338082", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/ef_mat_heiwushi_27.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02bd1f4ff7c5ab146bf6ef04c2d02978", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/ef_mat_heiwushi_31.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "132304f66e9c4d7478d333f991dedc8e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/ef_mat_heiwushi_34.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2bcced4c3bf44b4387e9681e8e9dce0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/ef_mat_heiwushi_37.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00814f7c59e30724bb7ba4a28d9e7796", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/ef_mat_heiwushi_46.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a57aa5ecb3357124cb96fb3401d7d6ec", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/fazheng_lc_08_add_1_why 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3295774544ead240a12b3db6b58e9a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/fish_jxsy_box_smoke_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f68ea1dc9be2a2c49aff715c2e1969d3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/glow_an_bb_03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "67a4c82bb0f05714cacfe27ed3ca2845", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/guangzhu03__4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0fecc9b89cd1f1b41b29b823d8f5cbb1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/guangzhu04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "554e38aa852cbac4e970d67f31dd5185", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/huolong_bullet_wenli_009.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dfc805a9ae9c10149897456cc48b2e66", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/jiguang_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4b089877cc51952439ad3ecee96f1cda", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/lx_zmz_shine01_fazheng_lc_11_2_ui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "08934ca4e4e8cab4fa9fcd743426e090", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/lx_zmz_shine01_fazheng_lc_11_2_ui 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d1963749e8b96a94db2cbba1aee4b3f1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/lx_zmz_shine01_fazheng_lc_11_2_ui 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6898efcbd5a0fd64d8fcdd49fcc35775", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/lx_zmz_shine01_shandian_sigle_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "12cab7f21f0c30c45927772a90a4da1e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/t_lx_zmz00_water_why03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ab35e995c46dd24386061ee0fdf4adf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/water_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8b2a582fa5375f1498bf35e487cf9830", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/xingjuepao_mask05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6b82db013a2953c49bbdb7bcda3c6a49", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/174/material/xingjuepao_mask07.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "90f465dfba309a146addc3d56939c821", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/bullet_175_fury_jian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "527fc748504821e45802ad983e34181e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/cannon_175_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "50e27c7e5cd55114d8d1603cb45df52c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/cannon_175_3liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5be51dea2487fdb4198e31c93ea0bef3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/cannon_175_5_zuo.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d5cbde3edf82a94184c8c5efd27d05e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/cannon_175_7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93348e7abc82a1a48b9850b1ee24c4d1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/175/material/cannon_175_jianqi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e43b11b0b0ade6a4eb8d8bf49b80115a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/176/material/cannon_176_3saoguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "44729cf8c204c414bbad2a7fda69ef1f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/176/material/cannon_176_4saoguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5ceef97996c4e6d4187fb37d02fc0022", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/bullet_147_tuowei09.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e65fa436fd45a34f80884ac930ea26c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/ef_daquan3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f12e8938710f124d920f2c514419dba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/fire_lc_03_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f98865784f672b745bbd25e3792d14ef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/fire_xulie_lc02_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b110380a635966445ad8f3d1ae329e37", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/fire_xulie_lc02_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "574cfbcb399ef504caf3e9681aa93f0d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/fire_xulie_lc03_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dafcba455c8b93844996f5070f887532", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/trail_12_golw_00020.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef3faf3bfeafd8e4e8b02a7b6b6d55e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/material/wuti_zuqiu_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "22eab560abebe4d44bd82a9126a7651e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/177/model/Materials/No Name.mat"}, {"Title": "错误信息", "Info": " | 包含官方标准着色器"}]}, {"GUID": "be4d210e218e2eb489edd2b74bd164c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/159_4cheng 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "adeaf5012b0df2f4cb3f471cc4d1ad38", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/baiyang<PERSON><PERSON>_wenli_22.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "be4f516395b9fd84d9c50c7c9a6c73ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/bullet_124_vio01_noise11.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9772bb8bf8f0c224ba1fc6503c44cb5a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/bullet_124_vio01_noise12.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5b8b77d996aef0043987fa27fa2b77c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/bullet_124_vio01_noise13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "186efda78eedb944e955649eeae2ae8f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/cannon_162_6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aab755ede53595340bc3bdd980f830ab", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/cannon_162_7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a6a57305d856184fba6ef1f8f85cc60", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/cannon_162_d3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "db6b9b3f0c5310b4fbbcb81304cabec5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/ef_mat_heiwushi_29.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "56a0d353be516fb4e86e9bec215fe073", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/t_bullet160_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b32cca1737718043b4882a9e0a16c89", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/t_lx_zmz00_t_lx_zmz00_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3944628a077a6ca419f54db802ebc176", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/xulie_yanhuo_010_water_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b086271c53f318c48b515a37640aae32", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/178/material/xulie_yanhuo_010_water_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b422536c24baad548b8afb35489b0598", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/bai01_t_rj_hz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fec95021c02c62846975c06c4f38912f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/guangzhu03__7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "20d8ec054e44d0641bebad0d4158eacc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/jiguang_zmz05_c_xulie_fang<PERSON>ai_01__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd70cd088b90fd0469683b2c5dad6594", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/light005_xl_bullet150_zmz.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e5572091e9faef41878f77ec9bbfc67", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/obj_lock_zmz01_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "760b19de339077448803d885b3476a06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/t_179_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "43c1ced887e4fbd45983846c7194cae0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/179/material/t_hz_mask02_lx_zmz_shine01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b6685d50e248fb440937905ad50a5377", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/cannon_162_2guang 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "07e917c9b1f9fea439aa10498703415f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/cannon_162_2guang 3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c7098636761029143bb16ef2c2593f7b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/cannon_162_8.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5343229fa06aa5e49bb3a721cd7ed73d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/jb_zmz02_t_lx_zmz04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "460173ce10fb6d54880ce152a79fca6a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/lx_zmz_shine01_glow_ui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2b982f6ad56c08b44b74f97ab7a03cb3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/lx_zmz_shine01_wen<PERSON>_jingge_lc_02__4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e8dd46519164a9248b3b27d992064607", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/m_guangzhu_wenli_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9b50814586512f84cbf075f445fb824a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/t_smoke_zmz05_1_t_lx_zmz00__19.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8a21daffc14860240a2393e13c5443d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/180/material/wenli_jingge_lc_03_lx_zmz_shine02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "28c2c6a26b0c3b94895744fbd2e2437e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/181/material/xl_bullet150_zmz02_wen<PERSON>_jingge_lc_03_cy_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f4e96a5620cd5494396fc187300df561", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/182/material/cannon_182_6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e282b82958713d44f80799b421951f56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/182/material/cannon_182_6_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d4586f2687c5c6242ad228ac5d0317ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/182/material/cannon_182_6_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9342c02ca3ac6fe4c83d78416b40ffaf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/183/material/cannon_115_wenli1_ui 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c4afaeab751d4e64e8ef6c71fed3f9a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/183/material/jinbi_huangguan_blend 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d63f8740d33bad34da830416b96a662a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/183/material/t_bullet151_mask02_t_hz_mask02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3aaf51170abf2714da76e07064d3e78c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/183/material/wuti_huluobu 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0ff2010d29b764e4a847080fdbf5d8e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/183/material/wuti_huluobu 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c96ce93df4d2dfb488119a4e14bb54fe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/184_jc_liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5aaa9ef220247774da9f2d62033c54eb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/184_xx_liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c0b294c85414d7428fe9a1945734425", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/184_zt_liuguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fcebce74865b62a4182ab2c8ddf2a6e5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/bullet_147_tuowei04_cy_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a3b08cf87ff7b7041be1ac0f8914d95c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/bullet_147_tuowei04_cy_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d3792889dad590f43838ddde10f7e950", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/bullet_158_core.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "510690e1e9eabab4e8cbf1cb5728335b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/fazheng_lc_13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e3b276d155f70154fa12f8cb2d0faff1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/t_bullet151_mask01c_water_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b5a685f9df86ab84ba0bbbe56d664bed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/wenli001_cy3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e901c1989fc10784c83a3627c1128977", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/184/material/wenli001_cy3_dan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e25360f5c9aa7724f880acc9b26ed261", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_001_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ad4b1708784fda7419c1f4fd157bc22d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_002_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5de4e39a472958f41a61868a29099a78", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_003_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "81cbc72819746b448be3da2d48dffec5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_004_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b37ea6a08284fe9429139322b78f6cdb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_005_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f9ccc9aaa3c159347a9ba5c8a5893fb0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_006_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f498afb8b372ac64fbd2205e540f0a5d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_007_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "42b0a3ca25222b44a9047f1d860ef893", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_cannon185_mask_008_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d504ace7f10912b40ad80fa03910a69e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_fengzheng_01_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "98ef983af8aaa1f49b78266acd265dea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_glow_add_zise_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "372c4e0df3328104daea72b4f379314d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_huaban_001_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "613d34785db8b964aabcb1d37daa39ab", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_huaban_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1f6f4232921ff34469f13c7a799d6c46", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_paopao_001_1_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "21008a59ecb80f64097f8f898eb0ee15", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_paopao_001_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e8d3724aa2b138b4d9f412d69433ca53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_paopao_002_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "79f62ac71cbae714892283d73b940908", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_shuye_001_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a536e25d522512c4e95fa774be13f6f4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_trail_001_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2211a98367104d34db82e46bef197ab2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_trail_002_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "620fec424c6c5d449a864500ce773d23", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_trail_003_yy.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00d44b7e5c76bb644abd65ee61888ee5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/fx_mat_trail_004_yy.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "93f74cc4eabd3c648b4ceb8da9b42ad8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/185/material/t_smoke_zmz07alpha02_t_trail166_mask01_cy_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5c193c84445965649ad066c2c3509b71", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_bullet186_mask_water.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "564abc569e8e37f43977f5023d91650c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_bullet_186_tuowei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8483da3c269b2fc40961a43a9ad56065", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_dntg.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c830552074c17346856887cbe2639e2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_majiang_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "13c9c77335b80904b919bda5f228a524", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_pao_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "661d3de6d98244044bad4971c94bdd9c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_ye1_light.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5026becb2cd062746a62fd9ee551e515", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_ye2_light.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3bc21c8cd8f1b154792d2434358dbc29", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_ye3_light.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c8faf6b8e5f6a544b8526f9123243295", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_ye4_light.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5b6ce3bc8acfaa342adb6bcbab8c7c6d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_ye5_light.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0dcb4d473cae4024a9c004b86b907659", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_yu_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "91ced528f6b6bda4ca7156a9c05d2bd2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon186_yuanbao_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "748a27f59e5f34e4097d9d10ff3831b3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon_186_huaban.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "91679b0576bfdf8429c1d8f564cceb91", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/fx_mat_cannon_186_majiang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "89cb723bbe5893e4780af1195d56b782", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/186/material/m_glow37_tuowei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e887610d2543b5c45bffa65f69132146", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_bullet_188_noise03_40_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "74615a4caf0f86a40a55b168dcb1fde8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_1mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f47f60948e5024544831fe6a52cd46e0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_2mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3bfb0422803d2e94ebf65d5cb3905496", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_3mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ab04f19728868c34489e719552b2a747", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_4mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2e09a07390f5f214f9da1e66c87fa7a1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_light_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e5beb1348a04e47448fef3e2b9bed552", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_light_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a15f3bd0056b1b547bd365e74830955f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_light_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a35835787ce52024b838905b6e89f7ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_light_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc85ffd81f1449c47be876f6da60a3dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_light_5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02d663a4549dbc147a59442ecb07d0a3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_Noise.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "73a2bc30c2a10014291347c3ad8541cc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_shandian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e7a61d0706d6aa5438b5c4078a1d5a44", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_suduxian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a8b8ecb46ff219f478e632fe9cbb3964", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/188/material/fx_mat_cannon188_tuowei.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "13f98d141e9479445811d3633c74070d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_baozha03ui_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "13882dfd17a3f44478d8732408fa10c9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_glow_big08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "50f49867d7e25a14aa8ae70706e5fff5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_guofengpiaodai01_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6af4ef0bad869d74b8aecac7b32c0e7f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_guofengpiaodai02_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6eab964bd1dd250458aa9378160f0ad9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_guofengpiaodai03_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "595252e2d9b0dbb43bc25f3a56f60263", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_wenli_075_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "27f6aafced641124c8abc68c93f03b95", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/189/material/fx_mat_zcmwenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "42cb8e3678d6dba4392090db5cc35418", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/200/material/fx_mat_cannon200_4mask2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0868e086aa5b49d47a0c3b401c2ad7a7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/200/material/fx_mat_cannon200_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "197dab31579e9fa469c3b46976425718", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/200/material/jijia_cy_wenli_006_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "00e6b3cf0485269409321dc3fb36dc88", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/201/material/fx_mat_cannon201_1mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "330ba27a745ed77488830ae289632d18", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/201/material/fx_mat_cannon201_2mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5df70d589eb84984fb06773adbe6c031", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/201/material/fx_mat_cannon201_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "96f66760709b7c04d9f9b365bea8a4b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/201/material/guangshu_06_1c__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad1078c5760183a4398b8a7944723eb6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/201/material/path_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cbe5d02ebb5e758479be61f438aa62e9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/202/material/fx_mat_cannon200_4mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf2cfb04cfff2544e80b1b651a054eaa", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/202/material/fx_mat_cannon202_4mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "da3b70d8d6ba4004698f7e1c2ba0862f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/202/material/fx_mat_kejichangge01_lq.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "84c0356470ec7da4d9f2b0d9783aa8a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/202/material/m_kejiliudong02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "77488db78fd217c43bde6cb9bbaf9373", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/202/material/m_zhushandian04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02d25a2bd382c2c49bf1a1dfbb119aae", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_203_beijing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e87b2c2cddc79984e95ab9c0efeebca5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_baodian_203_ld.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "886f34269298c5c4aa609072bad412d6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_guangxian01_ld.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4173a4c8ffe08a04995437949ca8eabe", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_machao_jiguang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d7c0e60516832a4a94105a64a6b0ee7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_paotai_ld.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a41ed760237c6ba46ac017a70542e7e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "365259e51fde76d4f8683896b8bc0a33", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9ed62ae130e91d24bb3d6087347ed7ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4fefbca479344d54fac4994d53f0da79", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0717a15932625ad49928da15aa2721c0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld8.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9f9d210c7c8b1134dbe3aab6269dff22", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld9.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fd6621a02e28d4b4f82b4f77d66bd65a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce0ea4908a394064abc8fbda7442664c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld11.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "62753eb597f3ac845ae158b9bdc2105a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld12.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ee26b47498bacd64a9974ce5daf96bf3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/fx_mat_zuanshi02_ld13.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "26faa38d2655b8f428ec4f8481492f72", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/glow_005_hgz_lw.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "392fa76dadadcff498b5871685e5e035", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/203/material/trail_zmz_07_shine_zmz01c_cy 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0760c66405813ca4f851d87936fef080", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/fx_mat_bullet204_lq1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "37cfd1f47af758144a0d953ab28fcf5d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/fx_mat_bullet204_lq2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f7a0d4ac376e76a46a02db652987df1b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/fx_mat_gp5_cannon204_3mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "32279d98684c1a645a6d93a2e8633992", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/fx_mat_gp5_cannon204_4mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "051dd1dda533c234a90ef4f5cafd6299", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/fx_mat_gp5_cannon204_mask.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a01044fe3971e09439ee5175d30d767c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/204/material/m_qiliang_qian.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5be12d91642fb3e4386b2ff12a20bed5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_203_beijing2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "afb0262a459867e48ab6fd747450b7b7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_baodian_paotai.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b1d04f87e77a29f4d9368e1987e8d55b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_cannon_14311_glow_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1c6f9c4bd4ea50f498268323ab9028c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_jiaxu_wep_keji002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0e564d04332fa14bb8b4609785323d7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_machao_jiguang3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a169b0276a787364a8ca1865498666dc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_machao_jiguang5.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97ae982dc93c17546a8699715854b7c6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_pao<PERSON>_smywenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8941161625178bc4bb64ae56fa03b572", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_paotai_wenli3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "763e4e21894bb6e4bb672f181604aa7d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_simayipt_ld.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "27a118284f6cda0478bf7fbda0e4dbe5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_simayipt_ld2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7ca800b8e960f2a42bead8b861bfba8b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_smy_zidan_ld.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "49bede782d128744fb8898826c36e362", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_smy_zidan_ld2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "11edb71bb20b5a045b6805e29391982f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_wenli_055_lc2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "888b27c003aa53143a97a4226b5629c3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_zuanshi02_ld20.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5d4593bf68e31b149bc31b2539c90185", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_zuanshi02_pao_ld.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "146e10b2a1e5aed45958f35b370b9302", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_zuanshi02_pao_ld2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "363f15a4dbd63bf4b9b5be4ab4b6f503", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_zuanshi02_pao_ld3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c330264de73410d45a914e6fdf66a799", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/fx_mat_zuanshi02_pao_ld4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f2e14e0b721fb2489b5da4834733440", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/leilong_ring_rongjie.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "89a59f62c48084f45b56eea16eee724f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/m_guofengzhuan02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "471ca310aa31a114a902ea3766bd67c5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/205/material/shandian_lq_04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d53f8ab95eaaed47b6a505eb342dd2a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/206/material/fx_mat_daodan01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b1fe19259cfa3f84690f380fe7069dda", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/206/material/fx_mat_dizuoliuguang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e27ec44998a3ec0439eba4550c065bc2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/206/material/fx_mat_dizuoliuguang02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6e492f33639f2fa4bb1d450728a4a76b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/206/material/fx_mat_pao<PERSON>uliug<PERSON>.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a5b3c686e1822574ead0ebb92f076939", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/206/material/fx_mat_zhanjianfaguang01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "eb04e1dbe2f57a746b89390f354ca0f6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_bullet_207_trail1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ee51bb59052e49340b7a9abcd7aa7d0c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_fuhao.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0e4ac167632cb54479cad3053311ad64", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ede02082405baeb45a9b4f1e8b8a087c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "92022ea93dda2dc42a8e7fdc4d1936ea", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1e36202349ff69f4b93082f3dece7602", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_4.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a84ffa20d65783442b9803095081e5b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_5.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5c0c1e1716db8dc4e832ff8df6cd9c6a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/207/material/fx_mat_cannon_207_mask_6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c3b737c133b7c1c4d9cc9d4989d43d8f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd8ce9ff10612a94d8634ee0b245aab6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f2b68cb6494467748b69d64e88d21b96", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "aa3672c7fb32c984da8f99cccb9e3228", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_7.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6569278f5ef1ee54194c5751b6d31dd4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_9.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a20dd433d748fd54cb6aa69569d13e66", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_10.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0a842718d1818824c92e286c2a210f82", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/208/material/fx_mat_cannon_208_mask_11.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "95ce2c7e7dad3e84a90cd1792d5c6576", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_dizuoliuguang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e088507a8ee28e848bf9f9af347bf3cb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_jiang<PERSON>_zidan.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e4938a344e415704e83bb80dab19415d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_jiang<PERSON>_zidan2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b75047d8e087bb4bb085c074819e2d5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_jijia_saoguang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cd29c41f94894fe47965b17be39364ce", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_zuanshi02_ld15.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8403c7f4dc0773a4588dbbf33342134d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_zuanshi02_ld114.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0d72caa25744a2b42a8cf3c6fe9eb312", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_zuanshi02_ld116.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "38edb79c95bd88743b760dc0186f0cb5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_zuanshi02_ld117.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "99d38b80f8ae4b04b86fb4ac5af3d1d3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/210/material/fx_mat_zuanshi02_pao_ld3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "31d13dd7dc692a74dac80b2482a70fb7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_pengqikou01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e59fd0c8d693a114c826bc4f5ed0d3cd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_headfire.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4cd53bb6619893147b47b39d0e75d8ec", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_headfire02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "2d57e513cdc17a44cb22ea5763864033", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_paodan01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ddc8f04ce0869a747867b19ef9468353", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_path01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ec1d3d7cce27063458cadb72d3b1cc47", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_shengqi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c41f95a51578ee347b0ae820ef05536d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_yanjing01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "335aa3c830261fc439af211bb60ee8a1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt/211/material/fx_mat_taikongmao_yanjing02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6fd9a53048170b44b9378af520f4f190", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fish_1714_aiyagesi01_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6f88f822f615367449124dce1803e467", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fish_1714_aiyagesi02_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1d03cd69727990b4c9419575f9ec5aef", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fish_1714_aiyagesi03_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd527fca30cdc854facd27c74c088f85", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fish_1714_aiyagesi04_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f554571e7c2cd7d489fa95b03561ef6d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mar_aiyagesi_yanjing01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1a34f0612cfe39041b9c616c263528a4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_baozha02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bd286ffeb68489e4ba5a190969659704", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_dilie01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f71c6b7925514904bb75579f055a6159", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_fazheng01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "db75743bd3ec123408b33b3b17ad5fa4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_glow01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9d0561f0029b2a441ba08625f0636d5b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_glow02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8630c728c817a034cb39d5f741424785", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_glow03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6cda4c3ef5de8d74aa93a4d4e5cb6599", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_jianjia_mask01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e9a0a7e60e238944ba7f03baa108f82e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_jianjia_mask02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "190ae5bbaae3d1d4894bfb3387128748", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_jiao_mask01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8bde1a58bed98cf4ab9a963ae741c040", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_jiguangzhu01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c15c9b5d6367bae458c72defbaef0f17", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "27ae5179aac19d1478689e0d89fedab0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "70754d9465722a44e91061bcd8d2ea19", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3afe24379ff5b6b438d4c7477a654794", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d40f1f4681df8a042a19d8f2ea778ccf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "881d2f74aee99e147982f95ef6d437c9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_line07.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4fa6b7501eabffc47b4bfeab457c9a2c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_Outline.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3e86b715315602042aa47ffe66cb8a3e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_Outline_huo.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "aad8161eb6067e34192c7ad92741d02b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_path01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ecd7c6f5ab838314d9bbfb435f5510be", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_path02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "68c247490d222f1459ae57b7c8a0197a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_path03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b7d0ff1dabbf2c346aaa40492d30f436", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_path04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "497bdf669ca164b4a9234f674fba2d5f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_shandian01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9ceee5c475e5bdc479645bc79beb5bf1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_shandian02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c6e4a044715b2f6498925d4a286d81d8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "212612b50cf1b0b4680a278adb6e09a0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "eb9c291ef05a5ae4cb0bc29172691206", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "17186a69e31ea18429c4248580eecaf4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail04.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "24e2429bd31a4db42a5e30f90e04ac2f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "494e425a1b44d2c4c924c126150f32ae", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_trail06.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0673f11de70871640a3ad72a3e8bc5b3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_wenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b3526c376669ec344b2157b0c00eddbf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_wenli02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e0513d6880d366e48a8b2e1c5f926792", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_wenli03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "91a94d3f22be2ec4a8306451975fada4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_wenli04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "702618ad97d0391488e9d4404e3a23d9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_wenli05.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b1d314bb3e2408b4da9bd572fa22d649", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_AiYaGeSi/Materials/fx_mat_aiyagesi_yanqiu_zhsnshenyong.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f82e7772c403f8d469e01211d79f4c9b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ac004d72a5d0a3e4d96b831a30b49720", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "db88148c624453549bddf6d297a22ef7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff_huangjin_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0ecd7ab862623ea4ea458b9fcfc01217", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo01_diff_pifeng.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "56a99d1712ea49d4cb5f06052cbbf0a1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a16782b94f688d040b122324802a8c73", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "3c20938dd265a0749b5a25324109ed31", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "79d3f87fb41b24e46a209b121814f4f3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo04_diff_fish 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "457b8a6bcce1708448db42903ed3a991", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo04_diff_fish_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b67c2dbf80a91714b8d0a1438c56410c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo04_diff_V3_yangjiao 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2cc1c5721b75dcc418986b0f42be2f0f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_BaiYangZuo/Materials/zhanshen_baiyangzuo05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c7a3838a97e07149b30bdb375a34fb0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_CaoCao/Materials/fish_1608_caocao_diff_01_V3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d52f9b2c79ad0e43b9648e055a37239", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_CaoCao/Materials/fish_1608_caocao_diff_02_V3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f47b7dcbbd3fe3140a1b528826c4de0d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_CaoCao/Materials/fish_1608_caocao_diff_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c1af5f98b24d18e4196ce67276d725e7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/beishi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3483a0c85daece7408b164a2e5c2cb30", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/beishi_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1485c6f9ec8b68c4f9f62c51db100bbe", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/beishi_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "28ffd8c3754e260478bf2bfc7ffddd7a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/beishi_ae_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1f374a6754620f248ba5838a2cfd1f22", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/body_level_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1efd1aad7404d6147babc7e09f85d319", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/body_level_01_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "086e2e52be30d6244b23a93a76d66639", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/body_level_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "47d1400446b6a3c44a949229c794d403", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/circle_mask_chunvzuo1_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bc19dc3cfad46fd4dbbcda7faa0fa419", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/Default.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3065eec1ca73c9544b90056c13844bd2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/eye.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0338bddc1192cb1499cebd95369921ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d6bbce20ce9dccd4eb3dc1098bc8cb15", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/fozhu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "10845b2cdbffcaa4b885204d365a9bd2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/hair 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fdb602c71f808ed4fb8f9890d2abe253", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/hair.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "01b54f7faba4a484f851c19452851882", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/hair_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c06382609d3b18e4b9b8bdfddeb16ac3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ChuNvZuo/Materials/pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4ad8684184366d442bb60e6df246f49e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_DaoShou/Materials/fish_1548_daoshou_mat001_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8582c0f5b69e1cd4e8a89ce891b2b1fb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_GuanYu/Materials/guanyu_dao_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "02e26088c4f66754e911c918f92f3279", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_GuanYu/Materials/guanyu_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3217862ecc957df4f873cf86a2fca68d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_GuanYu/Materials/sanguo_shenqi_guanyu_paotai_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3fe140499e482fc4ba83a4bf14ef9f81", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>/Materials/fish_1581_huangzhong_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f77da6e8d74797b40a9cf294f12b8e53", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>hong/Materials/fish_1581_huangzhong_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "380b5d3d5dfade844a50d7056b491720", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>/Materials/fish_1581_huangzhong_03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6492f1d977b9b3646a9fe265aacfcb99", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>/Materials/gongjian_hz_lq_003_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f86fa971bdac32c40921d1a89130645f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Huang<PERSON>/Materials/gongjian_hz_lq_004_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05ab2bd258f981d4eba9d20db65ce690", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Jiangwei/Materials/sanguo_shenqi_jiangwei_01_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4c6123a4b1602214a905582efbf88a3d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Jiangwei/Materials/sanguo_shenqi_jiangwei_02_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "89d945e9167f027409e369c6b88dc4dd", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo01_diff_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1b84f759f6dafa844b05bb82a45c8e06", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo01_diff_huangjin02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c282c6bd579ba9498d6cccc9558484f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiu<PERSON>uo/Materials/zhanshen_jinniuzuo01_diff_huangjin02_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "183823b089af1c04b990f5f6fd9639ca", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo02_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf2e3d9f24b30c84f9d28a2bf3a7deb1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6d4d7877788d8d64f95725fe4a3c6d57", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8593a5cb9543dca47a166610a8286535", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f2a30919fc361f74d831e3ec141b36c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JinNiuZuo/Materials/zhanshen_jinniuzuo05_diff_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "627c6913fbc57f6429fdd0ed62fc75c1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_01_2D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca96ade70db336e47a03103c2838686f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_01_2D_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f450eaf7e3cd96e4cb18cddecfda8a13", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_01_2D_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f3c5319aff6067b49b51df12df427c41", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_01_2D_v2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2aa1bce924fd76f49ac838e89f67b8e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_02_2D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5028c9a1325aee64a8c6b5c4ee60b95c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_03_2D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b40cebd734de8ee41b2dda803dec37ee", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_03_2D_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "290f16a86be66314b9f8418e3ce76acb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_04_2D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ea735f90b50d904e941f3b0e6658234", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_beishi_2D.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e0df7f3ef405ba40814cd86300088d7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_beishi_2D_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fc78913675989c441b0fc7492fc7c1ba", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_JuXieZuo/Materials/zhanshen_juxiezuo_beishi_2D_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "494669cbf6a4e07478d202de9ee7936a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Materials/fish_1716_ladamandisi01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e8fb2733ec132c04ca1850f703298604", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Materials/fish_1716_ladamandisi02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4c46810c723939849955b80fdf57bece", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Materials/fish_1716_ladamandisi03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4d82d48b18d021246a101c383c127d6d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaDaManDiSi/Materials/fish_1716_ladamandisi03_Outline_huo.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "87dfdefbf2afcc446951e4c3647d42b6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaoJun/Materials/fish_1588_laojun_01_diff_new.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "827253c9355c6484bada2d894b9d53a7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaoJun/Materials/fish_1588_laojun_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab258b1ee4dd2e34484e1bccd3c61724", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LaoJun/Materials/fish_1588_laojun_03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "816d5e93b0e82194fa1ac31da6011a7b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiJing/Materials/fish_1587_lijing01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ce988c440a70dda458f5e577b79c8192", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiJing/Materials/fish_1587_lijing02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c86965ab936cded49abc878893082b09", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiJing/Materials/fish_1587_lijing03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b42feb149bd26354abc2f7f3655617f8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiJing/Materials/lijing_ta_diff_Skill02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "fa4afa16f6b2bf743a69aadbd9d12b33", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiuBei/Materials/fish_liubei01_diff_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de374174a57abc04dbd7aa3ab208d668", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiuBei/Materials/fish_liubei02_diff_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6fd0c2dfaa9a6f94caab75a1929d01ad", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LiuBei/Materials/fish_liubei02_diff_weapon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f38598599e8775540bad18e3d3466620", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_LvBu/Materials/fish_1580_lvbu_diff_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "134666a9fe7de7f44bfeffd8498ea11b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_MaChao/Materials/machao_paoling_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29a88a52d27039d458a4650c23bfd148", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_MaChao/Materials/sanguo_shenqi_machao_paotai_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d3c6605c0f17a0947a4c00492710934b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fish_1715_minuosi01 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c7a371a9fb5f22f468d20add813830e9", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fish_1715_minuosi02 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1ef0995518473e745bf2b823868024a7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fish_1715_minuosi03 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4d8f4e981eb870a4da595b8c982e5c62", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fish_1715_minuosi04 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5c6e21b1198501445a234b91ca0b56e4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_blackfire01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fb639c2fb1a749d4594ac122558f5d4c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_01_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "93a1c090519ef914eaa8b34b11e43b5a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_02_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7fbc4e9b515f5cf4e8a10c54b385f8d0", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_03_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e06d023cc125af54c82404a60fcfe86c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_04_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "275a6ac946cf64d429746c572adb7586", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_05_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f443a57162b83c4d85f01940bc0d6aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_06_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4225a8fdd0700524b86d242c6755117b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_07_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e59d4171301f59246b46b30f95238f29", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_daoguang_08_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d1c63fd5f1fef245ae5d6d3675a6310", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_fazhengmiaobian01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ab96690dea25ef2408e0ed53f962a502", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_fire02_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "09e83462130a6a940a3064a5f22a0887", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_jiao01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5952f59bf0e70284c9e686387464f6e1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_chibangwenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0a7003974947ec44db191167e05c8486", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_chibangwenli02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9b8558051cd0df846a179cbdc600c0b7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_firewenli01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7ce2812ff36716644944ead1559501a6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_firewenli01_add.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b8852d445e5b35e4791ce99c2d6aeeb3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_Outline_huo 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "60dc9ac7b90bcfe4aa59de366301277a", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_minuosi_path02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e205f1d1f987c1746a1ca53b04881639", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_path_01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "879e06eec93efe14ab14c256f04dc5ef", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_shandian01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ad7398ee540fb22449ff5986ceadd483", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_shandian02_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "cac4323b0ae11164a9805c679b1ed4e3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_shijiu_01_minuosi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9b2807b570836294fab311a0b6144afb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_trail01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "27428d71f25cf6040a7e239d8e152f5b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_trail02_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d196a9c0523c88a4c8906655f58a7a4d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_trail03_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "45f8cb20912c18745806dd61f920fe80", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_trail04_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8c50e7352c43bec4fa98b87207bccb25", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_trail05_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9876aaa332afeca4e95f7aea1e44fd37", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_01_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "609452b5d1f527c4997b33a5f3f5d5e1", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_02_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "d2e3669f2f04b544b812efd38f38d9d6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_03_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "55d8ad4a9eaebbd47be1a80ddff4b840", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_04_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "11cfa83b7c008ae438e43cc32eb65656", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_05_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "bf5b4545dce9de842823575594523d18", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Minuosi/Materials/fx_mat_wenli_06_minuosi.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5f7d6c3011be9304bb1b76fc37620697", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "373f5794a74651e47b85d1b9bc010c61", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low01_diff_shenshengyi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2f32a4049659f6648afa31a572b0c5ee", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low01_diff_shenshengyi_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ead21b0f556d0cf469f5ea877850c9e1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low01_diff_shenshengyi_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "27f9675835d46c04098b760d179cd7c7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low02_diff_pifu.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef07a59adc1e88548b46048fbf056aed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low03_diff_toufa.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4f2dc69336a2fb943a316b2d3a09648a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low03_diff_toufa_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ed0ab9db37f895499868509f59bd797", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low04_diff_yanjing.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f0aecc6f747b4084a821c7d5e35eaebd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low05_diff_chibang.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dbd4b3dac7a43474b87ebcd28b8a59a4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low05_diff_chibang_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b6bfd3378b04c124fb839d034894ffef", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Mojiezuo/Materials/zhanshen_mojiezuo_low05_diff_chibang_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0f4bf9908c65ab74ea14f4c13145142a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_NvWa/Materials/fish_shj_shenv_001.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3c94095848e75894db8c85e36e05b49a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_NvWa/Materials/fish_shj_shenv_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "999ae3f011e9b2b4eb43994d92f62994", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54b9a6ccaa85fba41a42e93c2ebfc309", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo01_diff_shenshengyi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b0594f4a73693ac4d954f7bd82751e74", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo01_diff_shenshengyi_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "64452d0f2f627114b9cef7579bc6e951", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8cf795738219991409b3dc5f93a9ad90", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo02_diff_shenshengyi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2e13a7ed8b59d0f4e8ff5286e3dce182", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo02_diff_shenshengyi_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "728f315705547e040b29d93e57e72bb9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3216fc992b1ed2945ba4c9aad010023a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3e9048bba5e6c9b46bc0a80c29d04119", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo04_diff_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e645ea93a1987a441a300f17b14124de", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d814797eea2c16648990b047825e72aa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SheShouZuo/Materials/zhanshen_sheshouzuo06_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "05f983b247e3e0d43a8e8ba8f6f6d433", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/back.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d1b19b5f61b21f34cbe4d4e64bb2c5ac", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/back_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6164c48441268a948bcb12b801a3478b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/body.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e2f6b04ece1039a4bbdcd908b2c68a2b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/body_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "41cbb9f9352659841bb92079bdb2a893", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/body_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9a5045f3fcbcb5b4380cfb8cdede98fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/eye.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9b3b35a215030454191d2795832d9005", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/face.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad665a111702d8f449f31e89f9b69a0e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/hair.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7bc75c969a349ef47a5f6bf675195c3f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShiZiZuo/Materials/hair_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9fa450e3926d5f24792a3936eb97540d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Materials/mat_meiguihua_wenli01_lq2_change.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "101d1af3a87bbfc4b9127c4ddb9625bb", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Materials/mat_meiguihua_wenli01_lq3_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7cb47855c49ff8f4a8ba6c5883b6f844", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangYuZuo/Materials/mat_meiguihua_wenli01_lq4_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "243de87dae1214b41a24f28df04ca85f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>u<PERSON>uo/Materials/zhanshen_juxiezuo_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3f671fda03a1d84439d6065a1bab64b5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad2516f300d4ff444a18e7db0cd891e9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c3690e55e9001eb42a1bec552d3674e6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo01_diff_huangjin_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e080325618e0bfa4991b2cc21257fc58", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d692b1919afe1904a8e946896dc6c50f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "071d8b2dd84e4a8498639126d95d6be7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a37c38469321b34880d609f84a0ddf1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo04_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5393a2e2abbf6374ba1f2042cbfc7459", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7d5cf759137efc647ae02b4eee74f865", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Shuang<PERSON>uZuo/Materials/zhanshen_shuangyuzuo_meiguihua.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8c8ecd7852c1b8b40b8e478a7137aa8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fresnel_baoxiang_pp01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ff428475a03cb047a04090061606516", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_dianwenli03_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "edae50795b210bb43b9b53ceae7dee28", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_shuangziwenli01_lc.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f452b6bc2a17a28478799ddece92ad9b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshi01_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fd3c28fea2df3e741a4db71eeecc549d", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshi01_lc_add.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "52252bdfd7e1bb840be153233eeeeff6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshi02_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0b0360bd9bc460f4a94155df6ea7eed2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshi02_lc_add.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7596e025d2555234a94761f86bce93a3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshimask01_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1573e96584abe414db374794b32ac452", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshimask02_lc.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "738cfe92e0580ca418b0bc89c104e6e8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/fx_mat_yunshixialuowenli01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54aab5d7cbfa326499cf92014faa49cf", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_2_huangjin_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "184df7b0d16d0e4409d47af7ce730df4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_2_huangjin_cannon01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0c5dea6925a668547899134657228723", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_2_huangjin_cannon01_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "39472915a97e3c84794aaa80ff3b9a39", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_2_huangjin_cannon01_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7399a92f3fb0eda4485b198465c84980", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_2_huangjin_cannon_baijin.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "0cf19b8f38c5577418d3932d2a14b8a7", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo01_baijin_2_gaoji_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ec7285ed4b8800a448599af7a10a68fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo02_1_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a1bda6c46f8801d469301784afc8d9e4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo02_1_chibang_cannon01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "9c720d4f73120c740b81406557e1bd98", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo02_1_chibang_cannon02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c0683fccb4d75f94082a0aed1d3b3493", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo02_1_chibang_cannon02_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "45639c142a1f0904bbb4df9a51d5c769", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo02_1_chibang_cannon02_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a5ef2dfdea3d67044838337807a27684", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo03_baijin_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4a604cac1c1d9f04fa86b796fb7463c4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo03_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "541bfe2dc59d083489604d3990fc381f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo04_baijin_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "25a91535ead1cfc4b89615d40573a79c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo04_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "76652c944cf553043b139a6a7a348811", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo04_cannon_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "88446b7f5c8cef74997b07937845b31f", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo05_baijin_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7c1fbef72d2515640b2305bdb5edc5e2", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuangZiZuo/Materials/zhanshen_shuangzizuo05_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f8c0ac339a4974a459362d77d107252c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_huangjin_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2ba69ffc9d10c38419d03fb7bfa13193", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_huangjin_low_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "15ee89b7d7adc554db3071d63a3cb09d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b109718fa356bb04d99eac2f9fdb3663", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo01_diff_pifeng_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "61206b471cc03cc46a510b0cc784d528", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo02_diff_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "cfc60495082dab145b40f2e379099430", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo02_diff_low_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "18db4b1f0cebe8c4c92511c18f454704", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo02_diff_low_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "225ca1b96693db240bf912dfd3b4ac8d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo03_diff_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3ffc076c02a990147ba6b6363e5454db", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo04_diff_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "459bc26aaa478c14298513b12f841edf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo04_diff_low_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ff158fb4de433c24fa21e86059020148", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_ShuiPingZuo/Materials/zhanshen_shuipingzuo05_diff_low.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ff680c2aeb5a59e4f8c04adfadaeb803", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiMaYi/Materials/fish_1609_simayi_01_paotai.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dd4b87f0eecebbc45810cdaa962e8323", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiMaYi/Materials/fish_1609_simayi_02_diff_shanzi_paotai.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7fed8fecd50422e4cbf153ef75fddf96", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_SiMaYi/Materials/fish_1609_simayi_02_paotai.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "24b2e9008b6484b44b271d51bf0d0ca8", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_body_youchang_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "27d694a83d4232644bf31aaebff972ba", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_chenyi_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "e4f19b8e1a404f74f8284b88086d7c5b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_piqun_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "feff44cabf3615a4591f13bf74c1041c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_shouhuan_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7032220e97da7674aaf3c1dcad052dc6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_toufa01_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a863f175b6acf9a40a15e8e6451418b4", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_tui_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dc1f0eea2878d5546ab0f72feee3b878", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_xie_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "059cee491c525994d81e3dc79d129865", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/fish_1668_taiqiubaobei_yanqiu_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f545ddc73ba913b4793c48ea39cf7bb5", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TaiQiuBaoBei/Materials/mat_1688_taiqiu_taiqiugan_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "fdad05733c79a0a448855dc66dc3bc1d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianChengZuo/Materials/longtou_lq_001_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d5030c68f09beee48ae9acb585c6444b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo01_diff_baijin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "59654dcb06e41d64a868a3ae29631736", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "40019cf1d2abc874fa36e081d347d4d3", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo01_diff_huangjin_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "855a7bf6ddcd3b845bc178b3474a6ae7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d61ea1955ef7ae746a4ed1794becb87c", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo02_diff_ae.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "7878e1af190bc8d4abfa56cb7f3ac4d0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo02_diff_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e9b2da43bce50a940ba8507354303f44", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "77fc3026c20ddd14a9165c5f4527e6e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24b8efdc2e648c74eb4c4e812f7d8e73", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo04_diff_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "787c74facdcc88a41a4ad56e14ebe80f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianCheng<PERSON>uo/Materials/zhanshen_tianchengzuo05_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5541303374152b643acd6a19b4d58e68", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b2020884833652e499ce1702f5490cf7", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_01_diff_huangjin.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d47b8ba603cfcac4d8643c5d9ff9f2ed", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_01_diff_huangjin_ae 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c0f4313ef49369243aca3161538db3be", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_01_diff_huangjin_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "35d89d2f0695c3e41abd15f48fea5524", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_01_diff_pifeng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63ecf2d7a9ec7ec4f8c4a3cd7270406b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "df22d172d49395446a5920cb1d817be4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "56d7815b2b1c165419769f5e1cd5fec3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_03_diff_ae.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8d0a16693ab079846ac29c683af5338f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_04_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "21f56b55d5afb3e4686e57987a2878ff", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_TianXieZuo/Materials/zhanshen_tianxiezuo_beishi_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a6627a1334d06cd4fb9edb8723f81991", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tiesuoelong/Materials/fish_huolong_002.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b5c05286131f22e47b3dc13885e30362", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tiesuoelong/Materials/vfx_fish_huolong_003.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8ea345371d9e3d2458912ceaa2d14b04", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Materials/fish_1584_jijiatuzi_diff_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "1e48c09bfa35a994cbf4874817606184", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Materials/fish_1584_jijiatuzi_diff_cannon_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "efd04fece5060fa4cb56a7df3fdfab06", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Materials/fish_1584_jijiatuzi_diff_maofa_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "dee360ec8a615ab45a6ca949dfa15a47", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Tusiji/Materials/fish_1584_jijiatuzi_diff_yanjing_cannon.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "f759f8e9222e4e64489001ed59c34416", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaHouDun/Materials/fish_1582_xiahoudun_01_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f8ca3788d2af375448dd2ce92a53debc", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaHouDun/Materials/fish_1582_xiahoudun_02_diff 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "af83a4287f5c4ca4783259cf5be31692", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen01_diff_01.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "b3954e2416bdd884aa4f0c27314cd77e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen01_diff_02.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "8e5ce9de5b69ae341af76f4b5eaa9a7b", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen01_diff_03.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "68de7a30f1566cd418fb068939c416fc", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen03_diff 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "6292ad0605ceae84f9563b7567a8a970", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen03_diff 3.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "aec451cc313934344a9892325966d56e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen03_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "5d1f69848b657d04cb3315c2dd940640", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen04_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "4b9f5a54b1158df4ba408f02267b1be6", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiaRiZhanShen/Materials/fish_14316_xiarzhanshen05_diff.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "a12cc0d769db570439dcd629765c684f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiongMao/Materials/fish_1602_xiongmao_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "86bad444f5181534e85fecc951d5326b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XiongMao/Materials/fish_1602_xiongmao_diff_maozi.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "55fa484b9a55dd540ba46fc5b8bcd7e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XuChu/Materials/fish_1583_xuchu_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "de9a5fc8a7b8bcb459631e9ee1812254", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_XuChu/Materials/fish_1583_xuchu_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b38d5f571108cb5488ffea5be31f1a2e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_YangJian/Materials/fish_1589_yangjian01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3201f2a1afbd6bc4c9f34b8cd8daa435", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_YangJian/Materials/fish_1589_yangjian02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "96e67dd9a80407f41b18d8c7e654be62", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_YangJian/Materials/fish_1589_yangjian03_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c32a42f63c9b9a54e9eb0926b274c607", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON>/Materials/sanguo_shenqi_z<PERSON><PERSON>_01_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0395b102bde650647b5e2b30822e7253", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_<PERSON>/Materials/sanguo_shenqi_z<PERSON><PERSON>_02_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "2c81cea5a334ee74bb0d435d7dae9585", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhao<PERSON>un/Materials/sanguo_shenqi_z<PERSON><PERSON>_paotai_diff.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "98953e70a3fa5d74a83e716c02713c11", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhugeliang/Materials/mat_sanguo_shenqi_zhugeliang02_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8b888969cb0bac4da2e23fd7d83a073", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhugeliang/Materials/sanguo_shenqi_zhugeliang01_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "5df96211ba0ff51488468f31a80bb017", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhugeliang/Materials/sanguo_shenqi_zhugeliang02_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "342dbe9e46f4ac144a0880a7fae695d0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArt3D/cannon_Zhugeliang/Materials/sanguo_shenqi_zhugeliang03_cannon.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "a78c82dd5f22efa43ac2b60b8b67e53e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/159_4cheng.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "b831d81c4b1b36d449aa4a4de2cc5694", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/3965_wing_liuguang 1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "52732dcd2fadba54f921382670cc7a77", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/bai01_t_lx_zmz00__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "54279dc50de1eec4cba6668620a4f90e", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/Black_biankuang.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "ea189f266862fa442a827a033a746477", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/bullet_147_tuowei02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f519fe1dca78e0c4394d3afa544d816e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_115_wenli1_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d5448db26a931d4dae9e734486e47b1", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_115_wenli3_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "63af5e632993105428d9d5dc610c4377", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_120_ice01_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1ec5b82d26c8b4c45a9836805c84fcb8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_133_mask01_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4480af4967bd21042a9f289636fb6fd9", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_140_mask02_wenli.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8278c07c81138ec46870a4924f54fda5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/cannon_157.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e77ad250a81bfc49899ff0fce3284e2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/effect_level_line.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c6ae190b94a830f45b9c38038b91b8a5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/fazheng_lc_011.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e807b2eafcc58e94b8ce935d183e0778", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/glow_008_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ef8b8c6076d263d4b8103bef96b02b73", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/glow_jingtou02_a 2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "568c6dd24b88a6b49b2011e7f3576eb6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangshu_06_1c__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ad859706a6782de4b83b050f27c97abf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangzhu01_b04.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "376b979fef972674f823a1494dcc66fd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangzhu01_b09.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "24e1083184ed0534698e0ffe3158cccf", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangzhu03__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1075b540a92fe6e4dbe9f1049db46338", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangzhu03__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "540f960fc8a79cd419e2d601b9417a4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/guangzhu03__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "66ec0f66be95b9f4e88c5330fff9bab3", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/jb_zmz05_wenli020.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "9d782a125b5c47e488218221c904b623", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/lightning_xulie_zmz08.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7df4ddfa49d550542929b0bdb5a23634", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/lightning_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "78cd831df91cbb342a4299bba3000b54", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/lx_zmz_shine01_glow_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eeb9cbc9ef3d9394ca801b451fbedac6", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/lx_zmz_shine01_light005_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ca65aa7061cebd24e974a9d5a0c6d098", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/mask_zmz_01__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "87c988c6e06707d4c95cb549dd5194c4", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/mask_zmz_01add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7a9e2037d3e508d4ea12a03abfdf6b18", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/nazha_waike_wenli_4.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d72f60273e3ad2e43976096cc45b98fa", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/Noise10_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6658b9b0ebe40ea4a9190e27ae88203c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/Noise10_t_lx_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c873ad5b632fab4449a6dd555b41884a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/obj_shine_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "bd02f87a9c5ab7e4a9e183004599f852", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/obj_shine_zmz01_guangzhu01_c__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "55add0c87f19a73438c2746ff117fc56", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/ojb_zidan03_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "f73223fe0c97af84796116f136b5fa8c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/ojb_zidan_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "7b890f4c4f1a348489d086073a23d70f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/path_sanjiao_01_add.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9d205f975605f846808d3eab29b8bbb", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/ProjectorShadow 1.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "c4e74a9e5d8b36247b53e5753eaa8131", "Passes": false, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/ProjectorShadow 2.mat"}, {"Title": "错误信息", "Info": " | 包含冗余属性"}]}, {"GUID": "987c8fa16b9b5bc4ba57d11167119d9c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/ring_zmz05_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d9cf9ed34b4410942b747b6fd11773c2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/shandian_bb_01_t_lx_zmz00_change.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8347fa311a303d045bd29c11e5abe659", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/shandian_lc_09_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "0374c0e902fe2844fb5a9ce23d05b8dd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/shine_zmz08_2_t_lx_zmz00_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "98350843e6a11b546a5e8d981619c645", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_bullet149_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "962f730ff7cb0f343819dedebd4694d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_bullet151_mask01_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "97a3941a9b7142c44af4389cbb8fe12a", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_bullet151_mask02_t_hz_mask01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ab1e713eba0d41346b173a20733ff6b2", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_bullet151_zmz01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1e2f9682eb4ddfe4491712a5ed6d8520", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_lx_sdx02_suduxian_lc_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83ff78031b77ebd4294b3aa82379d292", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_lx_zmz00_t_lx_zmz00_1c_ui.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8a5996060a8fe014493fae881f4fc108", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_lx_zmz00_water_why02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "83725728c3f81574ba5ada47694f14a8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_lx_zmz00_water_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "eca387eb42d3d8142a598ebfdd14d042", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/t_smoke_zmz05_t_smoke_zmz08_1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d19fcfc98ce365549a6e3edcea717f83", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/tianxiezuo_bg.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "64845522d0453924fa47926738a40d5f", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail011_dis05.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d8431814b2ceff4bb3a224875816f4d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_lc_01.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "368330d8cd1b3904cbfba10bc9709901", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_lc_03_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "d8d33f845de1b244eb7f90bae795324d", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_lc_14.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dfafb9c3115c3244ea28427da68e8f90", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_zmz02__1.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "c0460575d143f844eb8574cc73977f43", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_zmz02_t_lx_zmz00__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "e9dfb26fa20c8f948b0083372560a35b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_zmz03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "ed4e726a5d1599445b0f1e2ee8a9c760", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/trail_zmz_10_t_lx_zmz00_2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dd7d50e5a8673b840aa7962817c6879c", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/water_zmz01_t_lx_zmz00.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "50ae2a12c155b38479222273c5c640bd", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/water_zmz02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "85fdc14e42d2f8d44ac4b9f58bae9735", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/wenli021__2.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "dc69eeee6ae275048b201c9a64dd0b3e", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/wenli021__6.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "8978a0ba67117264f94b8de4b287d5a0", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/wenli024__3.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "29463fd7140bc7345a54517f2d238c08", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/wenli024__fly.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4e7cb3fb420a2424993731f4820eb304", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/wenli026_wenli021.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "4094117881480144db585aab0fdfe392", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xiaochou_xiaoqiu_03_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "1b6ea1972676dbf45ab34d87b368f2ed", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xingjuepao_mask03.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "079f546691f87514cadc3a3f77e135b8", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xingkong_lc_01_shine_zmz01c_alpha.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "39fd3951844b99247b5c30dced4af13b", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xulie_baozha_lc_01_blend.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "6d9007a662f836d4085df5e0fad37611", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xulie_fang<PERSON><PERSON>_01_suduxian_lc_02.mat"}, {"Title": "错误信息", "Info": ""}]}, {"GUID": "3d665b1bf331bdc49ba69f7d712152d5", "Passes": true, "ScanInfos": [{"Title": "资源路径", "Info": "Assets/Fishes/Cannon/PrefabArtCommon/material/xulie_paokou_zmz03_ui.mat"}, {"Title": "错误信息", "Info": ""}]}]}